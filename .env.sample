APP_ENV=dev
LOG_LEVEL=trace

LOG_DAILY=true
LOG_PATH=logs
MAX_SIZE=300
MAX_AGE=5
MAX_BACKUPS=5
COMPRESS=true

DB_DRIVER=mysql
DB_CONNECTION=mysql
MYSQL_HOST=host.docker.internal or localhost
MYSQL_PORT=4374
MYSQL_DATABASE=admin_hpv
MYSQL_USER=root
MYSQL_PASS=xLrhS8US4QfAt24Qr88J

POSTGRES_HOST=localhost
POSTGRES_PORT=5450
POSTGRES_USER=postgres
POSTGRES_PASS=123456xyz
POSTGRES_DATABASE=brxcms_hpv
POSTGRES_SSLMODE=disable

PATH_MIGRATE=database
DB_DSN=${MYSQL_USER}:${MYSQL_PASS}@tcp(${MYSQL_HOST}:${MYSQL_PORT})/${MYSQL_DATABASE}?charset=utf8&parseTime=True&loc=Local

TELEGRAM_TOKEN=
TELEGRAM_GROUP_DEV=
TELEGRAM_DEV=true

API_TOKEN=nwabrancherXDev201288@2024TaOn
FIBER_PORT=4005
FIBER_MODE=release/debug

DOMAIN=http://localhost:${FIBER_PORT}
VIEWS_PATH=./views
PUBLIC_PATH=./public

PATH_IMG_HPV=../gocms_brx/editors
DOMAIN_IMG_HPV=https://img.hpv.vn

IMAGE_CMS_PATH=https://cms.hpv.vn/api/CMS/image/folder