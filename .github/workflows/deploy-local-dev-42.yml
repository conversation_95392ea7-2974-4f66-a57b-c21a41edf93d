
name: Deploy to local dev 42
on:
  push:
    branches:
      - dev
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
      - name: SSH Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_S42 }}
          USERNAME: ${{ secrets.USERNAME_S42 }}
          PORT: ${{ secrets.PORT_S42 }}
          KEY: ${{ secrets.PRI_KEY_S42_GOWEB }}
          script: |            
            systemctl stop goweb
            destination_dir="/home/<USER>/msd-goweb"              
            cd $destination_dir
            git pull origin dev
            systemctl restart goweb
