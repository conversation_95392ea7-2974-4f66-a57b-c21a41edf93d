package web

import (
	"fmt"
	"webgo/cmd/web/internal"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/fiberapp"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/template/html/v2"
	"github.com/spf13/cobra"
)

var (
	serviceNameServer = "web-service"
	versionSchedule   = "1.0.0"
)

func newServerServiceCtx() sctx.ServiceContext {
	return sctx.NewServiceContext(
		sctx.WithName(serviceNameServer),
		sctx.WithComponent(fiberapp.NewFiber(configs.KeyCompFIBER)),
		sctx.WithComponent(gormc.NewGormDB(configs.KeyCompGormMysql, "")),
		sctx.WithComponent(gormc.NewGormDB(configs.KeyCompGorm, "psg")),
		sctx.WithComponent(sctx.NewAppLoggerDaily(configs.KeyLoggerDaily)),
	)
}

var (
	ServerCmd = &cobra.Command{
		Use:     "web",
		Short:   "server run crawler social",
		Long:    `server CLI Long crawler social`,
		Version: versionSchedule,
		Run: func(cmd *cobra.Command, args []string) {
			serviceCtx := newServerServiceCtx()
			loggerSv := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")
			loggerSv.Info("--- start server ---- ")

			if err := serviceCtx.Load(); err != nil {
				loggerSv.Fatal(err)
			}

			fiberComp := serviceCtx.MustGet(configs.KeyCompFIBER).(fiberapp.FiberComponent)
			engine := html.New(configs.ViewsPath, ".html")
			if serviceCtx.EnvName() == configs.AppProd {
				engine.Reload(true)
			}

			engine.AddFuncMap(templates.FuncMap())
			fiberComp.SetEngineConfig(engine)

			router := fiberComp.GetApp()

			internal.RouterServer(router, serviceCtx)
			if err := router.Listen(fmt.Sprintf(":%d", fiberComp.GetPort())); err != nil {
				loggerSv.Fatal(err)
			}
		},
	}
)
