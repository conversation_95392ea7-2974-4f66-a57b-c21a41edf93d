version: "3.9"
services:
  goweb:
    build:
      context: '.'
      dockerfile: 'Dockerfile'
    env_file: .env
    ports:
      - "${FIBER_PORT:-3000}:${FIBER_PORT:-3000}"
    environment:
      DB_DSN: "${MYSQL_USER}:${MYSQL_PASS}@tcp(${MYSQL_HOST}:${MYSQL_PORT})/${MYSQL_DATABASE}?charset=utf8&parseTime=True&loc=Local"
      DB_DRIVER: "mysql"
      FIBER_PORT: "${FIBER_PORT}"
      VIEWS_PATH: "/views"
      PUBLIC_PATH: "/public"
    command: [ "./main", "web" ]
