proxy_cache_path /var/cache/nginx keys_zone=hpv_cache:10m max_size=10g inactive=60m use_temp_path=off;
upstream msd-goweb {
	ip_hash;
	server 127.0.0.1:4005;
	server **************:4005;
}
upstream msd-web {
	ip_hash;
	server 127.0.0.1:8074;
	server **************:8074;
}
server {

	listen 80;
	listen [::]:80;
	server_name hpv.vn www.hpv.vn;
	return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.hpv.vn;
	ssl_certificate /etc/letsencrypt/live/hpv.vn/fullchain.pem; # managed by Certbot
    	ssl_certificate_key /etc/letsencrypt/live/hpv.vn/privkey.pem; # managed by Certbot
    	

	ssl_session_timeout 10m;
    location / {
        return 301 https://$server_name$request_uri;
    }       
}


server {
   server_name hpv.vn; 
   proxy_cache hpv_cache;
   add_header X-Frame-Options "SAMEORIGIN";
   include /etc/nginx/conf.d/redirects_hpv.vn.conf.txt;
   location = / {
        root  /home/<USER>/msd-goweb;        
        try_files index.html @goweb;
    }
   access_log /var/log/nginx/hpv.vn/access.log;
   error_log /var/log/nginx/hpv.vn/error.log;

   location ~ ^/(apis|asset|dia-diem-tu-van|du-phong-hpv-cho-nu|du-phong-hpv-cho-nam|chien-dich-toan-quoc|wearegenv|du-phong-hpv-cho-nu-tu-9-18-tuoi|du-phong-hpv-cho-nu-tu-19-26-tuoi|du-phong-hpv-cho-nu-tu-27-45-tuoi|du-phong-hpv-cho-nam-tu-9-18-tuoi|du-phong-hpv-cho-nam-tu-19-26-tuoi|du-phong-hpv-cho-nam-tu-27-45-tuoi|chien-dich-toan-quoc-v2){
        root  /home/<USER>/msd-goweb;        
        try_files index.html @goweb;

   }
   
    location @goweb{
        	  proxy_pass http://msd-goweb;
		  proxy_buffering          on;
		  proxy_buffer_size        128k;
		  proxy_busy_buffers_size  256k;
		  proxy_buffers            4                   256k;
		  proxy_set_header         Host                $host;
		  proxy_set_header         X-Real-IP           $remote_addr;
		  proxy_set_header         X-Forwarded-For     $proxy_add_x_forwarded_for;
		  proxy_set_header         X-Forwarded-Proto   $scheme;
		  proxy_set_header         Upgrade             $http_upgrade;
		  proxy_set_header         Connection          $http_connection;

		 # Preserve Cookies
		 #proxy_set_header         Cookie              $http_cookie;
		 #proxy_pass_header        Set-Cookie;
		 proxy_cache hpv_cache;
		 proxy_cache_valid 200 60m;
		 proxy_cache_valid 404 1m;
   }

   location / {
         root  /home/<USER>/msd-web;
        try_files index.html @web;
    }

   
    location @web {
          proxy_pass http://msd-web;
		  proxy_buffering          on;
		  proxy_buffer_size        128k;
		  proxy_busy_buffers_size  256k;
		  proxy_buffers            4                   256k;
		  proxy_set_header         Host                $host;
		  proxy_set_header         X-Real-IP           $remote_addr;
		  proxy_set_header         X-Forwarded-For     $proxy_add_x_forwarded_for;
		  proxy_set_header         X-Forwarded-Proto   $scheme;
		  proxy_set_header         Upgrade             $http_upgrade;
		  proxy_set_header         Connection          $http_connection;
         
		# Preserve Cookies
		  #proxy_set_header         Cookie              $http_cookie;
		  #proxy_pass_header        Set-Cookie;
		proxy_cache hpv_cache;
                 proxy_cache_valid 200 5m;
                 proxy_cache_valid 404 1m;

	}


    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/hpv.vn/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/hpv.vn/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}

server {
    if ($host = hpv.vn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    server_name hpv.vn;
    listen 80;
    return 404; # managed by Certbot

}
