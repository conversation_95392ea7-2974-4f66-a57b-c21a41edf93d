package mapping

import (
	"webgo/hpv/blogs/transport/responses"
	"webgo/hpv/entity"
)

func MapperPostToBlogItemResq(posts []*entity.PostEntity) []responses.BlogItem {
	if posts == nil {
		return nil
	}
	var res []responses.BlogItem
	for _, post := range posts {
		res = append(res, responses.BlogItem{
			PagePath:    post.PagePath,
			Title:       post.Title,
			Img:         post.Img,
			Description: post.Description,
			Views:       post.Views,
		})
	}

	return res
}
