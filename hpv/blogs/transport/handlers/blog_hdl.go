package handlers

import (
	"context"
	"errors"
	"webgo/hpv/blogs/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/blogs"

	"github.com/gofiber/fiber/v2"
)

type BlogPostUsc interface {
	ListBlogUsc(ctx context.Context) (*responses.BlogListResp, error)
}

type blogHdl struct {
	usc BlogPostUsc
}

func NewBlogHdl(usc BlogPostUsc) *blogHdl {
	return &blogHdl{
		usc: usc,
	}
}

/**
 * List Blog Handlers
 */
func (h *blogHdl) ListBlogHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := h.usc.ListBlogUsc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}

		return templates.Render(c, blogs.ListNews(nil, datas))
	}
}
