package cerrs

import "errors"

var (
	ErrValidateId              = errors.New("id must be greater than 0")
	ErrName                    = errors.New("name is not empty")
	ErrTitle                   = errors.New("title is not empty")
	ErrSlug                    = errors.New("slug is not empty")
	ErrStatusNotFound          = errors.New("status not found")
	ErrID                      = errors.New("id is not in correct format")
	ErrData                    = errors.New("invalid form format")
	ErrStatus                  = errors.New("incorrect format status")
	ErrUpdateStatus            = errors.New("update status fail")
	ErrNotBlank                = errors.New("please enter complete information")
	ErrFormat                  = errors.New("wrong format")
	ErrStatusExist             = errors.New("please select status")
	ErrPathEmpty               = errors.New("path not empty")
	ErrTitleNotExist           = errors.New("please enter title seo")
	ErrPublish                 = errors.New("publish is in wrong format")
	ErrPublishPeriod           = errors.New("publish period is in wrong format")
	ErrOrdering                = errors.New("ordering must be greater than 0")
	ErrIDEdit                  = errors.New("you do not have permission to edit this category")
	ErrParseStrToTime          = errors.New("error parsing publish time")
	ErrPublishTime             = errors.New("if the status is Schedule, the publish time must be after the current time")
	ErrConvertToJson           = errors.New("convert to json failed")
	ErrUploadImg               = errors.New("upload image fail")
	ErrUploadListImg           = errors.New("upload list image fail")
	ErrUploadImgSeo            = errors.New("upload image seo fail")
	ErrMarshalHistory          = errors.New("marshal history fail")
	ErrUnMarshalHistoryContent = errors.New("unmarshal history content fail")
	ErrUnMarshalHistoryTitle   = errors.New("unmarshal history title fail")

	ErrValidateSearchName = errors.New("search name is not in correct format")
)
