package errs

import "errors"

var (
	//home page
	ErrHomePostsNewEmpty  = errors.New("home page posts new empty")
	ErrValidateSearchName = errors.New("search name is invalid")

	ErrValidateDetailSlugNotMatch = errors.New("detail slug and slug not match")
	ErrLocationEmpty          = errors.New("location empty")
	ErrValidateSearchAddress  = errors.New("search address is invalid")
	ErrValidateSearchProvince = errors.New("error validate search province id is not in correct format")
	ErrValidateSearchDistrict = errors.New("error validate search district id is not in correct format")
	ErrValidateEmpty          = errors.New("error validate search is not empty")
	ErrID                     = errors.New("id is not in correct format")
)
