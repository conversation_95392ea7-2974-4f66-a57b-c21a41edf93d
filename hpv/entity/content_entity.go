package entity

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

type Content struct {
	ID          int64        `gorm:"type:int(10);primaryKey;autoIncrement" json:"id"`
	Content     *string      `json:"content,omitempty"`
	Ordering    int64        `json:"ordering"`
	PostID      int64        `json:"post_id"`
	OptionID    int64        `json:"option_id"`
	Type        int          `json:"type"`
	Status      int          `json:"status"`
	Options     *Options     `json:"options,omitempty"`
	OptionValue *OptionValue `gorm:"foreignkey:ID;references:OptionID"`
}

func (Content) TableName() string {
	return "cms.contents"
}

type Options struct {
	Question   *string `json:"question,omitempty"`
	Questioner *string `json:"questioner,omitempty"`
	Age        *string `json:"age,omitempty"`
}

func (s *Options) Value() (driver.Value, error) {
	return json.Marshal(s)
}

func (s *Options) Scan(src interface{}) error {
	switch src := src.(type) {
	case nil:
		*s = Options{}
	case []byte:
		return json.Unmarshal(src, s)
	default:
		return fmt.Errorf("unsupported type for Options metadata: %T", src)
	}
	return nil
}
