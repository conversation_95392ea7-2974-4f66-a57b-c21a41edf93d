package entity

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type ImageList []ImageModel
type ImageModel struct {
	Name       string      `json:"name"`
	Img        string      `json:"img"`
	Size       int64       `json:"size"`
	Dimensions *Dimensions `json:"dimensions"`
}

type Dimensions struct {
	HeightPixels int `json:"heightPixels"`
	WidthPixels  int `json:"widthPixels"`
}

func (s *ImageList) Value() (driver.Value, error) {
	if s == nil {
		return nil, nil
	}

	return json.Marshal(s)
}

func (s *ImageList) Scan(src interface{}) error {
	if src == nil {
		return nil
	}
	var data []byte
	switch dataSrc := src.(type) {
	case string:
		data = []byte(dataSrc)
	case []byte:
		data = dataSrc
	default:
		return errors.New("unsupported type for ImageList")
	}

	var images []ImageModel
	if err := json.Unmarshal(data, &images); err != nil {
		return err
	}

	*s = images
	return nil
}
