package entity

import (
	"time"
)

type LocationsEntity struct {
	Id            int64               `json:"id"`
	Name          string              `json:"name"`
	PagePath 	  string              `json:"page_path"`
	Description   string              `json:"description"`
	Address       string              `json:"address"`
	WorkTime     string               `json:"work_time"`
	Ward          int                 `json:"ward_id"`
	Phone         string              `json:"phone"`
	Img           string              `json:"img"`
	// ImgMobile     string              `json:"img_mobile"`
	Lat 		  string 					`json:"lat"`
	Lng 		  string 					`json:"lng"`
	Rating        float32             `json:"rating"`
	Status        int                 `json:"status"`
	// Delf          int                 `json:"delf"`
	Timestamp     time.Time           `json:"timestamp"`
	Map      	*string             	  `json:"map"`               // Updated to allow NULL
	LinkMap       *string             `json:"link_map"`                // Updated to allow NULL
	// Types         int64               `json:"type" gorm:"column:type"` // Updated to allow NULL
	// LocationTypes *LocationTypeEntity `json:"location_types" gorm:"foreignKey:Id;references:Types"`
	ProvinceId    int64               `json:"province_id" gorm:"column:province_id"`
	// Province      *ProvinceEntity     `json:"province" gorm:"foreignKey:Id;references:ProvinceId"`
	DistrictId    int64               `json:"district_id" gorm:"column:district_id"`
	Ordering      int64               `json:"ordering,omitempty" gorm:"column:ordering"`
	// District      *DistrictEntity     `json:"district" gorm:"foreignKey:Id;references:DistrictId"`
}

func (LocationsEntity) TableName() string {
	return "hpv.locations"
}
