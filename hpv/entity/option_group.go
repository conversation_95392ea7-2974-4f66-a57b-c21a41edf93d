package entity

import (
	"time"
)

type OptionGroup struct {
	ID          int64          `gorm:"type:int(10);primaryKey;autoIncrement" json:"id"`
	Name        string         `json:"name"`
	Key         string         `json:"key"`
	Status      int            `json:"status"`
	CreatedBy   int64          `json:"created_by"`
	UpdatedBy   int64          `json:"updated_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	Options     []*OptionValue `gorm:"foreignKey:OptionGroupID"`
}

func (OptionGroup) TableName() string {
	return "cms.option_group"
}
