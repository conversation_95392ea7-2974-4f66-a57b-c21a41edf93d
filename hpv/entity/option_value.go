package entity

import (
	"time"
)

type OptionValue struct {
	ID            int64          `gorm:"type:int(10);primaryKey;autoIncrement" json:"id"`
	Name          string         `json:"name"`
	Key           string         `json:"key"`
	Img           string         `json:"img"`
	Status        int            `json:"status"`
	Ordering      int64          `json:"ordering"`
	Content       string         `json:"content"`
	Icon          string         `json:"icon"`
	Default       string         `json:"default"`
	IsContent     int            `json:"is_content"`
	OptionGroupID int64          `json:"option_group_id"`
	CreatedBy     int64          `json:"created_by"`
	UpdatedBy     int64          `json:"updated_by"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	
}

func (OptionValue) TableName() string {
	return "cms.options_values"
}
