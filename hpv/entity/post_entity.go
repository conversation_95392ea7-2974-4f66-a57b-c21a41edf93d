package entity

import (
	"encoding/json"
	"time"

	"github.com/lib/pq"
)

type PostEntity struct {
	ID       int64  `gorm:"type:bigint;primaryKey;autoIncrement" json:"id,string"`
	Title    string `json:"title"`
	Slug     string `json:"slug"`
	PagePath string `json:"page_path"`

	Description  string     `json:"description,omitempty"`
	Seo          *Seo       `json:"seo"`
	Img          string     `json:"img"`
	ListImg      *ImageList `json:"list_img,omitempty"`
	Publish      int        `json:"publish"`
	Status       int        `json:"-" gorm:"column:status;"`
	StatusString string     `json:"status" gorm:"-"`
	IsHot        int        `json:"is_hot"`
	IsHome       int        `json:"is_home"`
	IsFeature    int        `json:"is_feature"`
	IsLive       int        `json:"is_live"`
	IsOnlive     int        `json:"is_onlive"`
	IsVideo      int        `json:"is_video"`
	IsPhoto      int        `json:"is_photo"`
	IsAds        int        `json:"is_ads"`
	IsComment    int        `json:"is_comment"`
	VideoUrl     string     `json:"video_url,omitempty"`
	IsPin        int        `json:"is_pin"`

	Views              int64 `json:"views"`
	BounceRate         int64 `json:"bounce_rate"`
	AvgSessionDuration int64 `json:"avg_session_duration"`

	IsAudio     int             `json:"is_audio"`
	AudioUrl    string          `json:"audio_url,omitempty"`
	RedirectUrl string          `json:"redirect_url,omitempty"`
	EmbedVideo  string          `json:"embed_video,omitempty"`
	RelatedPost pq.Int64Array   `gorm:"type:integer[]" json:"related_post,omitempty"`
	CategoryID  int64           `json:"category_id"`
	Options     json.RawMessage `json:"options,omitempty"`
	Sources     *SourcesList    `json:"sources,omitempty"`
	CreatedBy   *int            `json:"created_by"`
	UpdatedBy   *int            `json:"updated_by"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	PostContent []*Content      `json:"-" gorm:"foreignKey:PostID"`

	Category   *CategoryEntity   `json:"-" gorm:"foreignkey:Id;references:CategoryID"`
	Categories []*CategoryEntity `json:"-" gorm:"many2many:cms.categories_posts;joinForeignKey:PostId;joinReferences:CategoryId"`
}

func (PostEntity) TableName() string {
	return "cms.posts"
}
 