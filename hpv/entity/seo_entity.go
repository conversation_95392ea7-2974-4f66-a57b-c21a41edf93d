package entity

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

type SnippetQA struct {
	Context    string       `json:"@context"`
	Type       string       `json:"@type"`
	MainEntity []MainEntity `json:"mainEntity"`
}

type MainEntity struct {
	Type           string         `json:"@type"`
	Name           string         `json:"name"`
	AcceptedAnswer AcceptedAnswer `json:"acceptedAnswer"`
}

type AcceptedAnswer struct {
	Type string `json:"@type"`
	Text string `json:"text"`
}

type SeoQaSlice []*SeoQa

type Seo struct {
	Title       string     `json:"title"`
	Keyword     *string    `json:"keyword,omitempty"`
	Description *string    `json:"description,omitempty"`
	Img         string     `json:"img"`
	Option      *string    `json:"option,omitempty"`
	Qa          SeoQaSlice `json:"qa"`
	SnippetQa   *string    `json:"snippet_qa,omitempty"`
}

type SeoQa struct {
	Answer   string `json:"answer"`
	Question string `json:"question"`
	Ordering int    `json:"ordering"`
}

func (s *Seo) Value() (driver.Value, error) {
	return json.Marshal(s)
}

func (s *Seo) Scan(src interface{}) error {
	switch src := src.(type) {
	case nil:
		*s = Seo{}
	case []byte:
		return json.Unmarshal(src, s)
	default:
		return fmt.Errorf("unsupported type for SEO metadata: %T", src)
	}
	return nil
}

func (qas *SeoQaSlice) Scan(value interface{}) error {
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, &qas)
	case string:
		return json.Unmarshal([]byte(v), &qas)
	default:
		return fmt.Errorf("failed to scan SeoQa slice")
	}
}

func (qas SeoQaSlice) Value() (driver.Value, error) {
	return json.Marshal(qas)
}

/**
 * Snippet QA
 */
func (qa *SeoQaSlice) SnippetQA() *SnippetQA {
	if qa == nil || len(*qa) == 0 {
		return nil
	}

	snippetQa := SnippetQA{
		Context: "https://schema.org",
		Type:    "FAQPage",
	}
	var listMainEntity []MainEntity
	for _, qna := range *qa {
		mainEntity := MainEntity{
			Type: "Question",
			Name: qna.Question,
			AcceptedAnswer: AcceptedAnswer{
				Type: "Answer",
				Text: qna.Answer,
			},
		}
		listMainEntity = append(listMainEntity, mainEntity)
	}

	snippetQa.MainEntity = listMainEntity

	return &snippetQa
}
