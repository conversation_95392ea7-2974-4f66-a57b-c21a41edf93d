package femalesv3

import (
	"webgo/hpv/femalesv3/transport/handlers"
	"webgo/hpv/femalesv3/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerFemaleV3 interface {
	PageFemaleV3Hdl() fiber.Handler
}

func ComposerFemaleV3Service(serviceCtx sctx.ServiceContext) composerFemaleV3 {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	repoCategory := repository.NewCategoryRepo(db)
	repoOptionValue := repository.NewOptionValueRepo(db)
	usc := usecase.NewFemaleV3Usc(repoCategory, repoOptionValue)

	hdl := handlers.NewFemaleV3Hdl(usc)
	return hdl
}
