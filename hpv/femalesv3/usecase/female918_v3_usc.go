package usecase

import (
	"context"
	"errors"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/hpv/femalesv3/mapping"
	"webgo/hpv/femalesv3/transport/responses"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/core"

	"gorm.io/gorm/clause"
)

type Female918V3PostRepo interface {
	FindPostRepo(ctx context.Context, filter *utils.Filters) ([]*entity.PostEntity, error)
}

type CategoryRepo interface {
	FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error)
}

type Female918V3OptionValueRepo interface {
	FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error)
}

type female918V3Usc struct {
	postRepo Female918V3PostRepo
	categoryRepo CategoryRepo
	optionRepo 	Female918V3OptionValueRepo
	logger   sctx.Logger
}

func NewFemale918V3Usc(postRepo Female918V3PostRepo, logger sctx.Logger, categoryRepo CategoryRepo, optionRepo Female918V3OptionValueRepo) *female918V3Usc {
	return &female918V3Usc{
		postRepo: postRepo,
		logger:   logger,
		categoryRepo: categoryRepo,
		optionRepo: optionRepo,
	}
}

/**
 * Process Female 918 usc
 * 1. get post & seo article by category 918
 */
func (usc *female918V3Usc)ListFemale918V3Usc(ctx context.Context) (*responses.Female918V3Resp, error) {
	conds := map[string]interface{}{
		"status": enums.POST_STATUS_PUBLISH,
	}

	subQuery := "SELECT post_id FROM cms.categories_posts WHERE category_id = ?"
	conds["id IN (?)"] = clause.Expr{SQL: subQuery, Vars: []interface{}{enums.CATEGORY_FEMALE_918}}

	filter := utils.Filters{
		Columns: &[]string{
			"id", 
			"title", 
			"img", 
			"page_path",
		},
		Conds:    &conds,
		PageSize: enums.LIMIT_10,
		OrderBy:  &[]string{"id DESC"},
	}

	posts, err := usc.postRepo.FindPostRepo(ctx, &filter)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	postsNew := mapping.MapperFemale918V3PostEntityToPostsNew(posts)

	options, err := usc.getOptionValueFemale918V3(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	seo, err := usc.getCategoryFemale918V3(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	return &responses.Female918V3Resp{
		PostNew: postsNew,
		Options: options,
		Seo:    seo,
	}, nil

}

/**
 * get option value Female 918
 */
func (usc *female918V3Usc) getOptionValueFemale918V3(ctx context.Context) (map[string]string, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"option_group_id": enums.OPTION_GROUP_FEMALE,
			"status":          enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"key",
			"content",
		},
	}

	options, err := usc.optionRepo.FindOptionValueRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperOptionValueToMapFemale(options), nil
}
/**
 * get seo Female 918
 */
func (usc *female918V3Usc) getCategoryFemale918V3(ctx context.Context) (*entity.Seo, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"id": 		enums.CATEGORY_FEMALE_918,
			"status":          	enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"seo",
		},
	}

	category, err := usc.categoryRepo.FirstCategoryRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return category.Seo, nil
}

