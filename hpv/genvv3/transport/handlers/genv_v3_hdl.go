package handlers

import (
	"context"
	"errors"
	"webgo/hpv/genvv3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/genv"

	"github.com/gofiber/fiber/v2"
)

type GenvV3Usc interface {
	GetGenvV3Usc(ctx context.Context) (*responses.GenvV3Resp, error)
}

type genvV3Hdl struct {
	usc GenvV3Usc
}

func NewGenvV3Hdl(usc GenvV3Usc) *genvV3Hdl {
	return &genvV3Hdl{
		usc: usc,
	}
}

/***
 * Page: LDP genv v3
 * Link: /wearegenv-v3
 * View: views/v3/genv/genv.templ
 * Components: true
 */
func (h *genvV3Hdl) PageGenvV3Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		detail, err := h.usc.GetGenvV3Usc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
		return templates.Render(c, genv.GenvV3(detail))

	}
}
