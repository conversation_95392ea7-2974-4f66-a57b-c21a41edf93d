package usecase

import (
	"context"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/hpv/genvv3/transport/responses"
	"webgo/pkg/gos/utils"
)

type GenvV3CategoryRepo interface {
	FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error)
}

type genvV3Usc struct {
	categoryRepo GenvV3CategoryRepo
}

func NewGenvV3Usc(categoryRepo GenvV3CategoryRepo) *genvV3Usc {
	return &genvV3Usc{
		categoryRepo: categoryRepo,
	}
}

/**
 * usc for hdl genv
 */
func (usc *genvV3Usc) GetGenvV3Usc(ctx context.Context) (*responses.GenvV3Resp, error) {
	filter := &utils.Filters{
		Conds: &map[string]any{
			"slug":   enums.CATEGORY_WEAREGENV,
			"status": enums.STATUS_ACTIVE,
		},
	}

	category, err := usc.categoryRepo.FirstCategoryRepo(ctx, filter)
	if err != nil {
		return nil, err
	}

	return &responses.GenvV3Resp{
		Views: utils.FormatNumberWithDot(category.Views),
		Seo:   category.Seo,
	}, nil
}
