package handlers

import (
	"context"
	"errors"
	"webgo/hpv/homev3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/home"
	"webgo/views/v3/homepagespeed"

	"github.com/gofiber/fiber/v2"
)

type HomePostUsc interface {
	ListHomeUsc(ctx context.Context) (*responses.HomeListResp, error)
	ListHomePageSpeedUsc(ctx context.Context) (*responses.HomeListResp, error)
}

type homeV3Hdl struct {
	usc HomePostUsc
}

func NewHomeHdl(usc HomePostUsc) *homeV3Hdl {
	return &homeV3Hdl{
		usc: usc,
	}
}

/**
 * List Home Handlers
 */
func (h *homeV3Hdl) ListHomeHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := h.usc.ListHomeUsc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
			
		return templates.Render(c, home.Home(datas))
	}
}

func (h *homeV3Hdl) ListHomePageSpeedHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := h.usc.ListHomeUsc(c.Context())
		if err != nil {
			return c.Redirect("/page-404")
		}

		return templates.Render(c, homepagespeed.HomePagespeedHTML(datas))
	}
}