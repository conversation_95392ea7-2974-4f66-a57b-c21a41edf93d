package usecase

import (
	"context"
	"errors"
	"webgo/hpv/homev3/mapping"
	"webgo/hpv/homev3/transport/responses"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/core"
)

type HomePostRepo interface {
	FindPostRepo(ctx context.Context, filter *utils.Filters) ([]*entity.PostEntity, error)
}

type CategoryRepo interface {
	FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error)
}

type HomeOptionValueRepo interface {
	FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error)
}


type homeUsc struct {
	repo       HomePostRepo
	categoryRepo    CategoryRepo
	optionRepo HomeOptionValueRepo
	logger     sctx.Logger
}

func NewHomeUsc(repo HomePostRepo,categoryRepo    CategoryRepo, optionRepo HomeOptionValueRepo, logger sctx.Logger) *homeUsc {
	return &homeUsc{
		repo:       repo,
		categoryRepo: categoryRepo,
		optionRepo: optionRepo,
		logger:     logger,
	}
}

/**
 * List home usc
 */
func (usc *homeUsc) ListHomeUsc(ctx context.Context) (*responses.HomeListResp, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
   			"status": enums.POST_STATUS_PUBLISH,
		},
		Columns: &[]string{
			"id",
			"page_path",
			"title",
			"img",
			"description",
			"views",
		},
		PageSize: enums.LIMIT_10,
		OrderBy:  &[]string{enums.ORDERBY_ID_DESC},
	}
	postHomeNew, err := usc.repo.FindPostRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	postDatas := mapping.MapperPostToHomeItemResq(postHomeNew)

	options, err := usc.getOptionValueHome(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	seo, err := usc.getCategoryHome(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	return &responses.HomeListResp{
		HomeNew:           postDatas,
		Options:           options,
		Seo:               seo,
	}, nil
}

func (usc *homeUsc) ListHomePageSpeedUsc(ctx context.Context) (*responses.HomeListResp, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
   			"status": enums.POST_STATUS_PUBLISH,
		},
		Columns: &[]string{
			"id",
			"page_path",
			"title",
			"img",
			"description",
			"views",
		},
		PageSize: enums.LIMIT_10,
		OrderBy:  &[]string{enums.ORDERBY_ID_DESC},
	}
	postHomeNew, err := usc.repo.FindPostRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	postDatas := mapping.MapperPostToHomeItemResq(postHomeNew)

	options, err := usc.getOptionValueHome(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	seo, err := usc.getCategoryHome(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	return &responses.HomeListResp{
		HomeNew:           postDatas,
		Options:           options,
		Seo:               seo,
	}, nil
}

/**
 * get option value
 *
 */
func (usc *homeUsc) getOptionValueHome(ctx context.Context) (map[string]string, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"option_group_id": enums.OPTION_GROUP_HOME,
			"status":          enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"key",
			"content",
		},
	}

	options, err := usc.optionRepo.FindOptionValueRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperOptionValueToMapHome(options), nil
}

/**
 * get Seo Home
 *
 */
func (usc *homeUsc) getCategoryHome(ctx context.Context) (*entity.Seo, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"id": 		enums.CATEGORY_HOME,
			"status":          	enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"seo",
		},
	}

	category, err := usc.categoryRepo.FirstCategoryRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return category.Seo, nil
}
