package genv

import (
	"webgo/hpv/ldp_genv/transport"
	"webgo/hpv/ldp_genv/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerGenv interface {
	PageGenvHdl() fiber.Handler
}

func ComposerGenvService(serviceCtx sctx.ServiceContext) composerGenv {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	repoCategory := repository.NewCategoryRepo(db)
	usc := usecase.NewGenvUsc(repoCategory)

	hdl := transport.NewGenvHdl(usc)
	return hdl
}
