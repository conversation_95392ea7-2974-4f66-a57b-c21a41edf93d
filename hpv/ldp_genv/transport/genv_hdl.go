package transport

import (
	"context"
	"errors"
	"webgo/hpv/ldp_genv/transport/responses"
	"webgo/pkg/sctx/core"

	"github.com/gofiber/fiber/v2"
)

type GenvUsc interface {
	GetGenvUsc(ctx context.Context) (*responses.GenvResp, error)
}

type genvHdl struct {
	usc GenvUsc
}

func NewGenvHdl(usc GenvUsc) *genvHdl {
	return &genvHdl{
		usc: usc,
	}
}

/***
 * Page: LDP genv
 * Link: /wearegenv
 * View: views/hpv/dlp_genv/genv.html
 * Components: true
 */
func (h *genvHdl) PageGenvHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		detail, err := h.usc.GetGenvUsc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}

		return c.Ren<PERSON>("hpv/dlp_genv/genv", fiber.Map{
			"detail": detail,
			"seo":    detail.Seo,
		}, "hpv/layouts/master")
	}
}
