package composers

import (
	"webgo/hpv/locations/repository"
	api "webgo/hpv/locations/transport/apis"
	"webgo/hpv/locations/usecase"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerApiDistrict interface {
	WithProvinceDistrictApi() fiber.Handler
}

func ComposerApiDistrictService(serviceCtx sctx.ServiceContext) composerApiDistrict {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()

	repo := repository.NewDistrictRepo(db)
	usc := usecase.NewApiDistrictUsc(repo)
	apis := api.NewDistrictApi(usc)

	return apis
}
