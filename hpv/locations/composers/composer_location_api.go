package composers

import (
	"webgo/hpv/locations/repository"
	"webgo/hpv/locations/transport/apis"
	"webgo/hpv/locations/usecase"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerApiLocations interface {
	SearchLocationApi() fiber.Handler
}

func ComposerApiLocationsService(serviceCtx sctx.ServiceContext) composerApiLocations {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	log := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	repo := repository.NewLocationsRepo(db)
	usc := usecase.NewApiLocationUsc(repo, log)
	api := apis.NewLocationApi(usc)

	return api
}
