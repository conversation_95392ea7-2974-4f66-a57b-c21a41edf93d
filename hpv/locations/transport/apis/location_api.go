package apis

import (
	"context"
	"webgo/hpv/entity"
	"webgo/hpv/locations/transport/requests"

	"github.com/gofiber/fiber/v2"
)

type ApiLocationUsc interface {
	SearchLocationApi(ctx context.Context, payload *requests.SearchLocationReq) ([]*entity.LocationsEntity, error)
}

type locationApi struct {
	usc ApiLocationUsc
}

func NewLocationApi(usc ApiLocationUsc) *locationApi {
	return &locationApi{
		usc: usc,
	}
}

/**
 * search location api
 */
func (a *locationApi) SearchLocationApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.SearchLocationReq
		if err := c.BodyParser(&payload); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid request payload",
			})
		}

		if err := payload.Validate(); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": err.<PERSON>rror(),
			})
		}

		locations, err := a.usc.SearchLocationApi(c.Context(), &payload)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		}

		return c.JSON(fiber.Map{
			"data": locations,
		})
	}
}
