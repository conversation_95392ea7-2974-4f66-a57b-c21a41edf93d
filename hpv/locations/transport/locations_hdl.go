package transport

import (
	"context"
	"errors"
	"webgo/hpv/locations/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/locations"

	"github.com/gofiber/fiber/v2"
)

type LocationsUsc interface {
	ListLocationsUsc(ctx context.Context) (*responses.LocationsResp, error)
}

type locationsHdl struct {
	usc LocationsUsc
}

func NewLocationsHdl(usc LocationsUsc) *locationsHdl {
	return &locationsHdl{
		usc: usc,
	}
}

/***
 * Page: Locations
 * Link: /dia-diem-tu-van
 * View: views/hpv/locationss/index.html
 * Components: true
 */

func (h *locationsHdl) PageLocationsHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		result, err := h.usc.ListLocationsUsc(c.Context())
		if err != nil {
			return c.Redirect("/404.html")
		}

		return c.Render("hpv/locations/index", fiber.Map{
			"locationsNew": result.LocationsNew,
			"provinces":    result.Provinces,
		}, "hpv/layouts/master")
	}
}

/***
 * Page: Locations V3
 * Link: /dia-diem-tu-van
 * View: views/hpv/locations/index.html
 * Components: true
 */

func (h *locationsHdl) PageLocationsV3Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		result, err := h.usc.ListLocationsUsc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
		return templates.Render(c, locations.Locations(result))

	}
}
