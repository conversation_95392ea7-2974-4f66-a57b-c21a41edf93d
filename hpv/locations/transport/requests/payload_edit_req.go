package requests

import (
	"strings"
	"webgo/hpv/common/cerrs"
	"webgo/pkg/sctx/core"
	"webgo/pkg/sctx/gorules"

	"github.com/go-playground/validator/v10"
)

type PayloadEditReq struct {
	ID     int64  `json:"-"`
	FakeId string `json:"fid" form:"fid" validate:"required,ruleFakeId"`
}

func (req *PayloadEditReq) Validate() error {
	validate := validator.New()
	validate.RegisterValidation("ruleFakeId", gorules.RuleValidateFakeId)
	req.FakeId = strings.TrimSpace(req.FakeId)

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "FakeId":
				return cerrs.ErrID
			}
		}
	}

	uid, err := core.FromBase58(req.FakeId)
	if err != nil {
		return cerrs.ErrID
	}

	req.ID = int64(uid.GetLocalID())

	return nil
}
