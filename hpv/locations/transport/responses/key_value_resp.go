package responses

import "webgo/pkg/sctx/core"

type KeyValueResp struct {
	ID   *core.UID `json:"id"`
	Name string    `json:"name"`
}

type KeyValueCountResp struct {
	ID    *core.UID `json:"id"`
	Name  string    `json:"name"`
	Count int64     `json:"count"`
}

type KeyValueCountStatusResp struct {
	Status     int    `json:"status"`
	Count      int64  `json:"count"`
	StatusName string `json:"status_name"`
}
