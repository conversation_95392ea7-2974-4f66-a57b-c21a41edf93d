package responses

import "webgo/hpv/entity"

type LocationsResp struct {
	LocationsNew *[]Locations
	Provinces    *[]KeyValueResp
	Options      map[string]string
	Seo          *entity.Seo
}

type Locations struct {
	Id       int64   `json:"id"`
	Name     string  `json:"name"`
	PagePath string  `json:"page_path"`
	Img      string  `json:"img"`
	Address  string  `json:"address"`
	Phone    string  `json:"phone"`
	WorkTime string  `json:"work_time"`
	LinkMap  *string `json:"link_map"`
	Map      *string `json:"map"`
	Lat      string  `json:"lat"`
	Lng      string  `json:"lng"`
}
