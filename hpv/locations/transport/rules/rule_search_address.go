package rules

import (
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
)

func RuleSearchAddressNoSQLInjection(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	value = strings.TrimSpace(value)

	if value == "" {
		return true
	}

	// Cho phép chữ cái (bao gồm Unicode tiếng Việt), số, kho<PERSON>ng trắng, "-", "_", ",", "."
	validPattern := `^[a-zA-Z0-9ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẰẮẲẴẶắẳẵặẸẻẽẹỀẾỂỄỆềếểễệỈỊỉịỌỌỎỎỐỒỔỖỘốồổỗộỚỜỞỠỢớờởỡợỤỦỨỪỬỮỰụủứừửữựỳỵỷỹýỵỷỹ\s\-_.,]*$`
	match, _ := regexp.MatchString(validPattern, value)

	// Chặn SQL Injection bằng cách kiểm tra các từ khóa nguy hiểm
	sqlKeywords := regexp.MustCompile(`(?i)\b(union|select|insert|update|delete|drop|;|--|\/\*|\*\/|xp_)\b`)
	isSQLInject := sqlKeywords.MatchString(value)

	return match && !isSQLInject
}
