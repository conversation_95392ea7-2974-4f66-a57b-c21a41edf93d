package usecase

import (
	"context"
	"webgo/hpv/common/gocommon"
	"webgo/hpv/entity"
	"webgo/hpv/locations/mapping"
	"webgo/hpv/locations/transport/responses"
	"webgo/pkg/gos/utils"
)

type apiDistrictRepo interface {
	FindDistrictRepo(ctx context.Context, filter *utils.Filters) ([]*entity.DistrictEntity, error)
}

type apiDistrictUsc struct {
	repo   apiDistrictRepo
}

func NewApiDistrictUsc(repo apiDistrictRepo) *apiDistrictUsc {
	return &apiDistrictUsc{
		repo:   repo,
	}
}

/**
 * get list district by province
 */
func (usc *apiDistrictUsc) WithProvinceApiDistrictUsc(ctx context.Context, provinceId int64) (*[]responses.DistrictValueLabelResp, error) {
	filter := utils.Filters{
		Columns: &[]string{"district_id", "title"},
		Conds: &map[string]interface{}{
			"status":      gocommon.STATUS_ACTIVE,
			"province_id": provinceId,
		},
	}
	districts, err := usc.repo.FindDistrictRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	result := mapping.MapperDistrictEntityToKeyValue(districts)

	return result, nil
}
