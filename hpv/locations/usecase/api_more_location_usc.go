package usecase

import (
	"context"
	"webgo/hpv/common/enums"
	"webgo/hpv/common/errs"
	"webgo/hpv/entity"
	"webgo/hpv/locations/mapping"
	"webgo/hpv/locations/transport/responses"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx"
)

type MoreLocationsRepo interface {
	FindLocationsRepo(ctx context.Context, filter *utils.Filters) ([]*entity.LocationsEntity, error)
}

type moreLocationsUsc struct {
	repo   MoreLocationsRepo
	logger sctx.Logger
}

func NewMoreLocationsUsc(repo MoreLocationsRepo, logger sctx.Logger) *moreLocationsUsc {
	return &moreLocationsUsc{
		repo:   repo,
		logger: logger,
	}
}

/**
 * locations usc
 * load more locations khi scroll
 */
func (usc *moreLocationsUsc) ListMoreLocationsUsc(ctx context.Context, page int) (*responses.MoreLocationsResp, error) {
	const limit = enums.LIMIT_10
	offset := (page - 1) * limit

	filter := utils.Filters{
		Columns: &[]string{
			"id", "name", "img", "page_path", "address", "phone", "work_time", "link_map", "map", "lat", "lng",
		},
		Conds: &map[string]interface{}{
			"status": enums.LOCATION_STATUS_ACTIVE,
		},
		PageSize: limit,
		Offset:   offset,
		OrderBy:  &[]string{"id DESC"},
	}

	locations, err := usc.repo.FindLocationsRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	if len(locations) == 0 {
		return nil, errs.ErrLocationEmpty
	}

	locationsNew := mapping.MapperLocationsEntityToMoreLocations(locations)

	return &responses.MoreLocationsResp{
		LocationsNew: locationsNew,
	}, nil
}
