package usecase

import (
	"context"
	"errors"
	"webgo/hpv/common/enums"
	"webgo/hpv/common/errs"
	"webgo/hpv/common/gocommon"
	"webgo/hpv/entity"
	"webgo/hpv/locations/mapping"
	"webgo/hpv/locations/transport/responses"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/core"
)

type LocationsRepo interface {
	FindLocationsRepo(ctx context.Context, filter *utils.Filters) ([]*entity.LocationsEntity, error)
}

type LocationProvinceRepo interface {
	FindProvinceRepo(ctx context.Context, filter *utils.Filters) ([]*entity.ProviceEntity, error)
}

type CategoryRepo interface {
	FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error)
}

type LocationsOptionValueRepo interface {
	FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error)
}

type locationsUsc struct {
	repo         LocationsRepo
	provinceRepo LocationProvinceRepo
	optionRepo   LocationsOptionValueRepo
	categoryRepo CategoryRepo
	logger       sctx.Logger
}

func NewLocationsUsc(repo LocationsRepo, provinceRepo LocationProvinceRepo,categoryRepo CategoryRepo, optionRepo LocationsOptionValueRepo, logger sctx.Logger) *locationsUsc {
	return &locationsUsc{
		repo:         repo,
		provinceRepo: provinceRepo,
		optionRepo:   optionRepo,
		categoryRepo: categoryRepo,
		logger:       logger,
	}
}

/**
 * locations usc
 * list 20 locations moi nhat
 */
func (usc *locationsUsc) ListLocationsUsc(ctx context.Context) (*responses.LocationsResp, error) {
	filter := utils.Filters{
		Columns: &[]string{
			"id", "name", "img", "page_path", "address", "phone", "work_time", "link_map", "map", "lat", "lng",
		},
		Conds: &map[string]interface{}{
			"status": enums.LOCATION_STATUS_ACTIVE,
		},
		PageSize: enums.LIMIT_20,
		Offset:  0,
		OrderBy:  &[]string{"id DESC"},
	}

	locations, err := usc.repo.FindLocationsRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	if len(locations) == 0 {
		return nil, errs.ErrLocationEmpty
	}

	locationsNew := mapping.MapperLocationsEntityToLocations(locations)

	provinces, err := usc.getListProvinces(ctx)
	if err != nil {
		return nil, err
	}

	options, err := usc.getOptionValueLocations(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	seo, err := usc.getCategoryLocations(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	result := responses.LocationsResp{
		LocationsNew: locationsNew,
		Provinces:    provinces,
		Seo:          seo,
		Options:      options,
	}

	return &result, nil
}

/**
 * list province
 */
func (usc *locationsUsc) getListProvinces(ctx context.Context) (*[]responses.KeyValueResp, error) {
	filter := utils.Filters{
		Columns: &[]string{"province_id", "title"},
		Conds: &map[string]interface{}{
			"status": gocommon.STATUS_ACTIVE,
		},
		OrderBy: &[]string{"ordering asc"},
	}

	provinces, err := usc.provinceRepo.FindProvinceRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}
	datas := mapping.MapperProvinceKeyValue(provinces)

	return datas, nil
}

/**
 * get option value
 *
 */
func (usc *locationsUsc) getOptionValueLocations(ctx context.Context) (map[string]string, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"option_group_id": enums.OPTION_GROUP_LOCATIONS,
			"status":          enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"key",
			"content",
		},
	}

	options, err := usc.optionRepo.FindOptionValueRepo(ctx, &filter)

	if err != nil {
		return nil, err
	}

	return mapping.MapperOptionValueToLocations(options), nil
}

/**
 * get Seo Locations
 *
 */
func (usc *locationsUsc) getCategoryLocations(ctx context.Context) (*entity.Seo, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"id":     enums.CATEGORY_LOCATIONS,
			"status": enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"seo",
		},
	}

	category, err := usc.categoryRepo.FirstCategoryRepo(ctx, &filter)

	if err != nil {
		return nil, err
	}

	return category.Seo, nil
}
