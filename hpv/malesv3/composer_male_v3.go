package malesv3

import (
	"webgo/hpv/malesv3/transport/handlers"
	"webgo/hpv/malesv3/usecase"
	"webgo/hpv/repository"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/component/gormc"
	"webgo/pkg/sctx/configs"

	"github.com/gofiber/fiber/v2"
)

type composerMaleV3 interface {
	PageMaleV3Hdl() fiber.Handler
}

func ComposerMaleV3Service(serviceCtx sctx.ServiceContext) composerMaleV3 {
	db := serviceCtx.MustGet(configs.KeyCompGorm).(gormc.GormComponent).GetDB()
	repoCategory := repository.NewCategoryRepo(db)
	repoOptionValue := repository.NewOptionValueRepo(db)
	usc := usecase.NewMaleV3Usc(repoCategory, repoOptionValue)

	hdl := handlers.NewMaleV3Hdl(usc)
	return hdl
}
