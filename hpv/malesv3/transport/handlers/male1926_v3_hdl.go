package handlers

import (
	"context"
	"errors"
	"webgo/hpv/malesv3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/males"

	"github.com/gofiber/fiber/v2"
)

type Male1926V3PostUsc interface {
	ListMale1926V3Usc(ctx context.Context) (*responses.Male1926V3Resp, error)
}

type Male1926V3Hdl struct {
	usc Male1926V3PostUsc
}

func NewMale1926V3Hdl(usc Male1926V3PostUsc) *Male1926V3Hdl {
	return &Male1926V3Hdl{
		usc: usc,
	}
}

/***
 * 
 * Page: Female 1926
 */

func (h *Male1926V3Hdl) ListMale1926V3Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := h.usc.ListMale1926V3Usc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
		return templates.Render(c, males.Male1926(datas))
	}
}