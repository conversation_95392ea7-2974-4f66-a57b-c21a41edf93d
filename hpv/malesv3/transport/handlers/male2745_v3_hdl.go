package handlers

import (
	"context"
	"errors"
	"webgo/hpv/malesv3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/males"

	"github.com/gofiber/fiber/v2"
)

type Male2745V3PostUsc interface {
	ListMale2745V3Usc(ctx context.Context) (*responses.Male2745V3Resp, error)
}

type Male2745V3Hdl struct {
	usc Male2745V3PostUsc
}

func NewMale2745V3Hdl(usc Male2745V3PostUsc) *Male2745V3Hdl {
	return &Male2745V3Hdl{
		usc: usc,
	}
}

/***
 * 
 * Page: Female 2745
 */

func (h *Male2745V3Hdl) ListMale2745V3Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := h.usc.ListMale2745V3Usc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
		return templates.Render(c, males.Male2745(datas))
	}
}