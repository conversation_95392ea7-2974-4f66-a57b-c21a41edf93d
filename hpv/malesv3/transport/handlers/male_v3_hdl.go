package handlers

import (
	"context"
	"errors"
	"webgo/hpv/malesv3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/males"

	"github.com/gofiber/fiber/v2"
)

type MaleV3Usc interface {
	GetMaleV3Usc(ctx context.Context) (*responses.MaleV3Resp, error)
}

type maleV3Hdl struct {
	usc MaleV3Usc
}

func NewMaleV3Hdl(usc MaleV3Usc) *maleV3Hdl {
	return &maleV3Hdl{
		usc: usc,
	}
}

/***
 * Page: Home Male V3
 * Link: /du-phong-hpv-cho-nam-v3
 * View: views/v3/male.templ
 * Components: true
 */

func (h *maleV3Hdl) PageMaleV3Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		pages, err := h.usc.GetMaleV3Usc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}
			
		return templates.Render(c, males.MaleIndex(pages))
	}
}
