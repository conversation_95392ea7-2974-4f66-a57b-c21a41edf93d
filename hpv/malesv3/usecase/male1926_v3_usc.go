package usecase

import (
	"context"
	"errors"
	"webgo/hpv/common/enums"
	"webgo/hpv/entity"
	"webgo/hpv/malesv3/mapping"
	"webgo/hpv/malesv3/transport/responses"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/core"

	"gorm.io/gorm/clause"
)

type Male1926V3PostRepo interface {
	FindPostRepo(ctx context.Context, filter *utils.Filters) ([]*entity.PostEntity, error)
}

type CategoryRepoMale1926 interface {
	FirstCategoryRepo(ctx context.Context, filter *utils.Filters) (*entity.CategoryEntity, error)
}

type Male1926V3OptionValueRepo interface {
	FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error)
}

type Male1926V3Usc struct {
	postRepo     Male1926V3PostRepo
	categoryRepo CategoryRepoMale1926
	optionRepo   Male1926V3OptionValueRepo
	logger       sctx.Logger
}

func NewMale1926V3Usc(postRepo Male1926V3PostRepo, logger sctx.Logger, categoryRepo CategoryRepoMale1926, optionRepo Male1926V3OptionValueRepo) *Male1926V3Usc {
	return &Male1926V3Usc{
		postRepo:     postRepo,
		logger:       logger,
		categoryRepo: categoryRepo,
		optionRepo:   optionRepo,
	}
}

/**
 * Process male 1926 usc
 * 1. get post & seo article by category 1926
 */
func (usc *Male1926V3Usc) ListMale1926V3Usc(ctx context.Context) (*responses.Male1926V3Resp, error) {
	conds := map[string]interface{}{
		"status": enums.POST_STATUS_PUBLISH,
	}

	subQuery := "SELECT post_id FROM cms.categories_posts WHERE category_id = ?"
	conds["id IN (?)"] = clause.Expr{SQL: subQuery, Vars: []interface{}{enums.CATEGORY_MALE_1926}}

	filter := utils.Filters{
		Columns: &[]string{
			"id",
			"title",
			"img",
			"page_path",
		},
		Conds:    &conds,
		PageSize: enums.LIMIT_10,
		OrderBy:  &[]string{"id DESC"},
	}

	posts, err := usc.postRepo.FindPostRepo(ctx, &filter)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	postsNew := mapping.MapperMale1926V3PostEntityToPostsNew(posts)

	options, err := usc.getOptionValueMale1926V3(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	seo, err := usc.getCategoryMale1926V3(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}

	return &responses.Male1926V3Resp{
		PostNew: postsNew,
		Options: options,
		Seo:     seo,
	}, nil

}

/**
 * get option value Female 1926
 */
func (usc *Male1926V3Usc) getOptionValueMale1926V3(ctx context.Context) (map[string]string, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"option_group_id": enums.OPTION_GROUP_MALE,
			"status":          enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"key",
			"content",
		},
	}

	options, err := usc.optionRepo.FindOptionValueRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperOptionValueToMapMale(options), nil
}

/**
 * get seo Female 1926
 */
func (usc *Male1926V3Usc) getCategoryMale1926V3(ctx context.Context) (*entity.Seo, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"id":     enums.CATEGORY_MALE_1926,
			"status": enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"seo",
		},
	}

	category, err := usc.categoryRepo.FirstCategoryRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return category.Seo, nil
}
