package handlers

import (
	"context"
	"errors"
	"webgo/hpv/mohV3/transport/responses"
	"webgo/pkg/gos/templates"
	"webgo/pkg/sctx/core"
	"webgo/views/v3/moh"

	"github.com/gofiber/fiber/v2"
)

type MohPostUsc interface {
	ListMohUsc(ctx context.Context) (*responses.MohListResp, error)
}

type mohV3Hdl struct {
	usc MohPostUsc
}

func NewMohHdl(usc MohPostUsc) *mohV3Hdl {
	return &mohV3Hdl{
		usc: usc,
	}
}

/**
 * List Moh Handlers
 */
func (h *mohV3Hdl) ListMohHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		datas, err := h.usc.ListMohUsc(c.Context())
		if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
			return c.Redirect("/page-404")
		}

		return templates.Render(c, moh.MohV3(datas.Seo, datas))
	}
}
