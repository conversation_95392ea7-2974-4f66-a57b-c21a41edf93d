package transport

import "github.com/gofiber/fiber/v2"

type NewsPageSubUsc interface {}

type newPageSubHdl struct {
	usc NewsPageSubUsc
}

func NewNewsPageSubHdl(usc NewsPageSubUsc) *newPageSubHdl {
	return &newPageSubHdl{
		usc: usc,
	}
}

/***
 * Page: News Page
 * Link: /tin-tuc-tong-hop
 * View: views/hpv/news_page/news.html
 * Components: true
 */

func (s *newPageSubHdl) NewsPageSubHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		
		return c.Render("hpv/news_page_sub/news_sub", fiber.Map{
		}, "hpv/layouts/master")
	}
}

