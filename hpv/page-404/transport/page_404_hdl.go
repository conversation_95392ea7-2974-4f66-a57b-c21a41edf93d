package transport

import (
	"webgo/pkg/gos/templates"
	"webgo/views/v3/page404"

	"github.com/gofiber/fiber/v2"
)

type Page404Usc interface {}

type page404Hdl struct {
	usc Page404Usc
}

func NewPage404Hdl(usc Page404Usc) *page404Hdl {
	return &page404Hdl{
		usc: usc,
	}
}

/***
 * Page: Page 404
 * Link: /page404
 * View: views/hpv/page-404/page-404.html
 * Components: true
 */

func (f *page404Hdl) Page404Hdl() fiber.Handler {
	return func(c *fiber.Ctx) error {

		return templates.Render(c, page404.Page404())
	}
}
