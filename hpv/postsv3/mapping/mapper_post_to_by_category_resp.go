package mapping

import (
	"webgo/hpv/entity"
	"webgo/hpv/postsv3/transport/responses"
)

func MapperPostToPostWithCategoryResp(posts []*entity.PostEntity) *[]responses.PostListCategory {
	var postListCategory []responses.PostListCategory

	for _, post := range posts {
		postListCategory = append(postListCategory, responses.PostListCategory{
			Title:       post.Title,
			PagePath:    post.PagePath,
			Description: post.Description,
			Img:         post.Img,
			Views:       post.Views,
		})
	}

	return &postListCategory
}
