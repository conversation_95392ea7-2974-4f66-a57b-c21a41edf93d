package mapping

import (
	"webgo/hpv/entity"
	"webgo/hpv/postsv3/transport/responses"
)

func MapperPostToDetailResp(post *entity.PostEntity) *responses.PostDetail {
	var postDetail responses.PostDetail

	postDetail.Title = post.Title
	postDetail.Slug = post.Slug
	postDetail.PagePath = post.PagePath
	postDetail.Description = post.Description
	postDetail.Seo = post.Seo
	postDetail.Img = post.Img
	postDetail.Views = post.Views

	postDetail.Contents = post.PostContent
	postDetail.Sources = post.Sources
	postDetail.CreatedAt = post.CreatedAt

	postDetail.CategoryName = post.Category.Name
	postDetail.CategorySlug = post.Category.Slug

	return &postDetail
}
