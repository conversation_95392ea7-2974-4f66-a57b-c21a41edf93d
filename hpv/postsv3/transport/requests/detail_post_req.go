package requests

import (
	"webgo/hpv/postsv3/transport/rules"
	"github.com/go-playground/validator/v10"
)

type DetailPostReq struct {
	ID   string `json:"-"`
	Slug string `json:"slug" validate:"required"`
}

func (r *DetailPostReq) Validate() error {
	validate := validator.New()
	if err := validate.Struct(r); err != nil {
		return err
	}

	slug, id, err := rules.RuleSlugID(r.Slug)
	if err != nil {
		return err
	}

	r.Slug = slug
	r.ID = id

	return nil
}