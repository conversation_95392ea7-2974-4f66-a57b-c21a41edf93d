package rules

import (
	"errors"
	"regexp"
	"strings"
)

var (
	ErrValidateSlug = errors.New("slug không đúng định dạng: <slug>-<id>")
)

func RuleSlugID(raw string) (string, string, error) {
	raw = strings.TrimSpace(raw)
	pattern := regexp.MustCompile(`^([a-z0-9-]+)-([0-9]+)$`)

	matches := pattern.FindStringSubmatch(raw)
	if len(matches) < 2 {
		return "", "", ErrValidateSlug
	}

	return matches[1], matches[2], nil
}
