package usecase

import (
	"context"
	"errors"
	"webgo/hpv/common/enums"
	"webgo/hpv/common/errs"
	"webgo/hpv/entity"
	"webgo/hpv/postsv3/mapping"
	"webgo/hpv/postsv3/transport/requests"
	"webgo/hpv/postsv3/transport/responses"
	"webgo/pkg/gos/utils"
	"webgo/pkg/sctx"
	"webgo/pkg/sctx/core"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type DetailPostRepo interface {
	FirstPostRepo(ctx context.Context, filter *utils.Filters) (*entity.PostEntity, error)
	FindPostRepo(ctx context.Context, filter *utils.Filters) ([]*entity.PostEntity, error)
}

type DetailOptionValueRepo interface {
	FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error)
}

type detailPostUsc struct {
	Repo   DetailPostRepo
	optionRepo DetailOptionValueRepo
	Logger sctx.Logger
}


func NewDetailPostUsc(repo DetailPostRepo, optionRepo DetailOptionValueRepo, logger sctx.Logger) *detailPostUsc {
	return &detailPostUsc{
		Repo:   repo,
		optionRepo: optionRepo,
		Logger: logger,
	}
}

func (usc *detailPostUsc) DetailPostUsc(ctx context.Context, param *requests.DetailPostReq) (*responses.DetailPostResp, error) {
	filter := utils.Filters{
		Columns: &[]string{
			"id",
			"title",
			"slug",
			"page_path",
			"description",
			"seo",
			"img",
			"views",
			"sources",
			"created_at",
			"category_id",
		},
		Conds: &map[string]interface{}{
			"id":     param.ID,
			"status": enums.POST_STATUS_PUBLISH,
		},
		Preloads: &map[string]interface{}{
			"Category":                nil,
			"PostContent.OptionValue": nil,
			"PostContent": func(db *gorm.DB) *gorm.DB {
				return db.Order("cms.contents.ordering asc")
			},
			"Categories": nil,
		},
	}

	options, err := usc.getOptionValueDetail(ctx)
	if err != nil && !errors.Is(err, core.ErrRecordNotFound) {
		return nil, err
	}


	post, err := usc.Repo.FirstPostRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	postDetail := mapping.MapperPostToDetailResp(post)
	data := responses.DetailPostResp{
		Post: postDetail,
		Options:  options,
	}

	if post.Slug != param.Slug {
		return &data, errs.ErrValidateDetailSlugNotMatch
	}

	//post by category id
	if post.CategoryID == 0 {
		return &data, nil
	}

	conds := map[string]interface{}{
		"status":  enums.POST_STATUS_PUBLISH,
		"id != ?": post.ID,
	}

	subQuery := "SELECT post_id FROM cms.categories_posts WHERE category_id = ?"
	conds["id IN (?)"] = clause.Expr{SQL: subQuery, Vars: []interface{}{post.CategoryID}}

	filterCate := utils.Filters{
		Columns: &[]string{
			"id",
			"title",
			"page_path",
			"description",
			"img",
			"views",
			"category_id",
		},
		Conds:    &conds,
		PageSize: enums.LIMIT_4,
		OrderBy:  &[]string{"id DESC"},
	}

	postsWithCate, err := usc.Repo.FindPostRepo(ctx, &filterCate)
	if err != nil {
		if errors.Is(err, core.ErrRecordNotFound) {
			return &data, nil
		}
		return nil, err
	}

	postListCategory := mapping.MapperPostToPostWithCategoryResp(postsWithCate)
	data.PostListCategory = postListCategory

	return &data, nil
}

/**
 * get option value
 *
 */
func (usc *detailPostUsc) getOptionValueDetail(ctx context.Context) (map[string]string, error) {
	filter := utils.Filters{
		Conds: &map[string]interface{}{
			"option_group_id": enums.OPTION_GROUP_POST,
			"status":          enums.STATUS_ACTIVE,
		},
		Columns: &[]string{
			"key",
			"content",
		},
	}

	options, err := usc.optionRepo.FindOptionValueRepo(ctx, &filter)
	if err != nil {
		return nil, err
	}

	return mapping.MapperOptionValueToMapDetail(options), nil
}