package repository

import (
	"context"
	"webgo/hpv/entity"
	"webgo/pkg/gos/generics"
	"webgo/pkg/gos/utils"

	"gorm.io/gorm"
)

type optionValueRepo struct {
	db *gorm.DB
}

func NewOptionValueRepo(db *gorm.DB) *optionValueRepo {
	return &optionValueRepo{db: db}
}

/**
 * find one option value
 *
 * @param ctx
 * @param filter
 *
 * @return *entity.OptionValue
 * @return error
 */
func (r *optionValueRepo) FindOneOptionValueRepo(ctx context.Context, filter *utils.Filters) (*entity.OptionValue, error) {
	tableName := entity.OptionValue{}.TableName()
	return generics.FirstGeneric[entity.OptionValue](ctx, r.db, tableName, filter)
}


/**
 * find option value
 *
 * @param ctx
 * @param filter
 *
 * @return *entity.OptionValue
 * @return error
 */
func (r *optionValueRepo) FindOptionValueRepo(ctx context.Context, filter *utils.Filters) ([]*entity.OptionValue, error) {
	tableName := entity.OptionValue{}.TableName()
	return generics.FindGeneric[entity.OptionValue](ctx, r.db, tableName, filter)
}
