package repository

import (
	"context"
	"webgo/hpv/entity"
	"webgo/pkg/gos/generics"
	"webgo/pkg/gos/utils"

	"gorm.io/gorm"
)

type postRepo struct {
	db *gorm.DB
}

func NewPostRepo(db *gorm.DB) *postRepo {
	return &postRepo{db: db}
}

/**
 * Find Post
 * Use generic
 */
func (r *postRepo) FindPostRepo(ctx context.Context, filter *utils.Filters) ([]*entity.PostEntity, error) {
	tableName := entity.PostEntity{}.TableName()
	return generics.FindGeneric[entity.PostEntity](ctx, r.db, tableName, filter)
}

/**
 * first Post
 */
func (r *postRepo) FirstPostRepo(ctx context.Context, filter *utils.Filters) (*entity.PostEntity, error) {
	tableName := entity.PostEntity{}.TableName()
	return generics.FirstGeneric[entity.PostEntity](ctx, r.db, tableName, filter)
}
