package mapping

import (
	"webgo/hpv/searchpost/transport/responses"
	"webgo/hpv/entity"
)

func MapperPostToSearchPost(posts []*entity.PostEntity) *responses.SearchPostResp {
	var items []responses.PostItem

	for _, post := range posts {
		items = append(items, responses.PostItem{
			Title:    post.Title,
			PagePath: post.PagePath,
			Img:      post.Img,
		})
	}

	return &responses.SearchPostResp{
		Posts: items,
	}
}

