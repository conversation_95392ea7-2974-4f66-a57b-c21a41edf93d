package api

import (
	"context"
	"webgo/hpv/entity"
	"webgo/hpv/searchpost/mapping"
	"webgo/hpv/searchpost/transport/requests"
	"webgo/pkg/gos/templates"
	"webgo/views/v3/partials/menus"

	"github.com/gofiber/fiber/v2"
)

type searchPostUsc interface {
	SearchPostUsc(ctx context.Context, keyword string) ([]*entity.PostEntity, error)
	ShowPostUsc(ctx context.Context) ([]*entity.PostEntity, error)
}

type searchPostApi struct {
	usc searchPostUsc
}

func NewSearchPostApi(usc searchPostUsc) *searchPostApi {
	return &searchPostApi{
		usc: usc,
	}
}

/**
 * Search Post API
 */
func (a *searchPostApi) ListSearchPostApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.SearchPostReq
		if err := c.BodyParser(&payload); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"code":    400,
				"message": "Invalid request payload",
			})
		}

		if err := payload.Validate(); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"code":    400,
				"message": err.Error(),
			})
		}

		if payload.SearchName == nil || *payload.SearchName == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"code":    400,
				"message": "Từ khoá tìm kiếm không được để trống",
			})
		}

		posts, err := a.usc.SearchPostUsc(c.Context(), *payload.SearchName)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"code":    500,
				"message": err.Error(),
			})
		}

		results := mapping.MapperPostToSearchPost(posts)
		html, err := templates.RenderToString(c, menus.MenuModalSearch(results))
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"code":    500,
				"message": err.Error(),
			})
		}

		return c.JSON(fiber.Map{
			"html":  html,
		})
	}
}

func (a *searchPostApi) GetPostApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		posts, err := a.usc.ShowPostUsc(c.Context())
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"code":    400,
				"message": err.Error(),
			})
		}

		results := mapping.MapperPostToSearchPost(posts)

		html, err := templates.RenderToString(c, menus.MenuModalSearch(results))

		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"code":    400,
				"message": err.Error(),
			})
		}

		return c.JSON(fiber.Map{
			"html":  html,
		})
	}
}
