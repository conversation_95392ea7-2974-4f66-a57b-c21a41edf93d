package requests

import (
	"strings"
	"webgo/hpv/common/errs"
	"webgo/hpv/searchpost/transport/rules"

	"github.com/go-playground/validator/v10"
)

type SearchPostReq struct {
	SearchName *string `json:"search" form:"search" validate:"omitempty,searchName"`
}

func (req *SearchPostReq) Validate() error {
	if req == nil {
		return nil
	}
	validate := validator.New()
	validate.RegisterValidation("searchName", rules.RuleSearchNameNoSQLInjection)

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "SearchName":
				return errs.ErrValidateSearchName
			}
		}
	}

	if req.SearchName != nil && *req.SearchName != "" {
		str := strings.TrimSpace(*req.SearchName)
		req.SearchName = &str
	}

	return nil
}
