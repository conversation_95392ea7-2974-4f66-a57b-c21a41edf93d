package errs

import (
	"errors"
	"fmt"
	"net/http"
	"strings"
)

/**
 * StatusCode:
 * RootErr:
 * Message:
 * Log: message gốc
 * Key : key danh da ngon ngu
 */
type AppError struct {
	StatusCode int    `json:"status_code"`
	RootErr    error  `json:"-"`
	Message    string `json:"message"`
	Log        string `json:"log"`
	Key        string `json:"error_key"`
}

func NewErrorReponse(rootErr error, msg, log, key string) *AppError {
	return &AppError{
		StatusCode: http.StatusBadRequest,
		RootErr:    rootErr,
		Message:    msg,
		Log:        log,
		Key:        key,
	}
}

func (er *AppError) Error() string {
	return er.RootErr.Error()
}

// AppError(AppError(...(error)))
func (er *AppError) RootError() error {
	if err, ok := er.RootErr.(*AppError); ok {
		return err.RootErr
	}
	return er.RootErr
}

func NewFullErrorResponse(statusCode int, rootErr error, msg, log, key string) *AppError {
	return &AppError{
		StatusCode: statusCode,
		RootErr:    rootErr,
		Message:    msg,
		Log:        log,
		Key:        key,
	}
}

func NewUnauthorized(rootErr error, msg, key string) *AppError {
	return &AppError{
		StatusCode: http.StatusUnauthorized,
		RootErr:    rootErr,
		Message:    msg,
		Key:        key,
	}
}

func NewCustomError(rootErr error, msg, key string) *AppError {
	if rootErr != nil {
		return NewErrorReponse(rootErr, msg, rootErr.Error(), key)
	}

	return NewErrorReponse(errors.New(msg), msg, msg, key)
}

// loi db
func ErrDB(err error) *AppError {
	return NewFullErrorResponse(
		http.StatusInternalServerError,
		err,
		"something went wrong with DB",
		err.Error(),
		"DB_ERROR",
	)
}

func ErrInvalidRequest(err error) *AppError {
	return NewErrorReponse(err, "invalid request", err.Error(), "ErrInvalidRequest")
}

func ErrInternal(err error) *AppError {
	return NewFullErrorResponse(
		http.StatusInternalServerError,
		err,
		"something went wrong in the server",
		err.Error(),
		"ErrInternal",
	)
}

/**
 * error common
 * cac project lon nen chi tiet
 * danh cho tang usecase
 */
func ErrCannotListEntity(entity string, err error) *AppError {
	return NewCustomError(
		err,
		fmt.Sprintf("Cannot list %s", strings.ToLower(entity)),
		fmt.Sprintf("ErrCannotList%s", entity),
	)
}

func ErrCannotGetEntity(entity string, err error) *AppError {
	return NewCustomError(
		err,
		fmt.Sprintf("Cannot get %s", strings.ToLower(entity)),
		fmt.Sprintf("ErrCannotGet%s", entity),
	)
}

func ErrCannotDeleteEntity(entity string, err error) *AppError {
	return NewCustomError(
		err,
		fmt.Sprintf("Cannot delete %s", strings.ToLower(entity)),
		fmt.Sprintf("ErrCannotDelete%s", entity),
	)
}

func ErrCannotUpdateEntity(entity string, err error) *AppError {
	return NewCustomError(
		err,
		fmt.Sprintf("Cannot update %s", strings.ToLower(entity)),
		fmt.Sprintf("ErrCannotUpdate%s", entity),
	)
}

func ErrEntityDelete(entity string, err error) *AppError {
	return NewCustomError(
		err,
		fmt.Sprintf("%s delete", strings.ToLower(entity)),
		fmt.Sprintf("Err%sDelete", entity),
	)
}

func ErrEntityExisted(entity string, err error) *AppError {
	return NewCustomError(
		err,
		fmt.Sprintf("%s already exists", strings.ToLower(entity)),
		fmt.Sprintf("Err%sAlreadyExists", entity),
	)
}

func ErrEntityNotFound(entity string, err error) *AppError {
	return NewCustomError(
		err,
		fmt.Sprintf("%s not found", strings.ToLower(entity)),
		fmt.Sprintf("Err%sNotFound", entity),
	)
}

func ErrCannotCreateEntity(entity string, err error) *AppError {
	return NewCustomError(
		err,
		fmt.Sprintf("Cannot create %s", strings.ToLower(entity)),
		fmt.Sprintf("ErrCannotCreate%s", entity),
	)
}

func ErrNoPermission(err error) *AppError {
	return NewCustomError(
		err,
		"You have no permission",
		"ErrNoPermission",
	)
}

var ErrRecordNotFound = errors.New("record not found")
