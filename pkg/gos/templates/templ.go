package templates

import (
	"bytes"
	"webgo/pkg/sctx/configs"

	"github.com/a-h/templ"
	"github.com/gofiber/fiber/v2"
)

func AssetURL(p string) string {
	domain := configs.Domain
	if domain == "" {
		domain = "/"
	}
	return domain + p
}

func SafeURL(path string) templ.SafeURL {
	domain := configs.Domain
	if domain == "" {
		domain = "/"
	}
	return templ.SafeURL(domain + path)
}

func ImgURL(path string) templ.SafeURL {
	pathImg := configs.Domain + "/media"
	if configs.AppEnv == configs.AppProd {
		pathImg = configs.DomainImgHpv
	}
	return templ.SafeURL(pathImg + path)
}

func Render(c *fiber.Ctx, component templ.Component) error {
	c.Set("Content-Type", "text/html; charset=utf-8")
	return component.Render(c.Context(), c.Response().BodyWriter())
}

func RenderToString(c *fiber.Ctx, component templ.Component) (string, error) {
	var buf bytes.Buffer
	err := component.Render(c.Context(), &buf)
	if err != nil {
		return "", err
	}
	return buf.String(), nil
}

