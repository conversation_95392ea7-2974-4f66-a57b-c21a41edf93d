package templates

import (
	"fmt"
	"html"
	"html/template"
	"time"
	"webgo/pkg/sctx/configs"
)

func FuncMap() map[string]interface{} {
	return map[string]interface{}{
		"add": func(i int) int {
			return i + 1
		},
		"attrescaper": func(s string) string {
			return template.HTMLEscapeString(s)
		},
		"plusVal": func(a, b int) int {
			return a + b
		},
		"subVal": func(a, b int) int {
			return a - b
		},
		"site": func() template.URL {
			domain := configs.Domain
			if domain == "" {
				domain = "/"
			}

			return template.URL(domain)
		},
		// "imageCMSPath": func() template.URL {
		// 	imageCMSPath := conf.ImageCMSPath
		// 	if imageCMSPath == "" {
		// 		imageCMSPath = "/"
		// 	}
		// 	return template.URL(imageCMSPath)
		// },
		"toHtml": func(str string) template.HTML {
			encap := html.UnescapeString(str)
			return template.HTML(encap)
		},
		"dict": func(values ...interface{}) (map[string]interface{}, error) {
			if len(values)%2 != 0 {
				return nil, fmt.Errorf("invalid number of arguments to dict")
			}
			dict := make(map[string]interface{})
			for i := 0; i < len(values); i += 2 {
				key, ok := values[i].(string)
				if !ok {
					return nil, fmt.Errorf("dict keys must be strings")
				}
				dict[key] = values[i+1]
			}
			return dict, nil
		},
		"domainImg": func() template.URL {
			pathImg := configs.Domain + "/media"
			if configs.AppEnv == configs.AppProd {
				pathImg = configs.DomainImgHpv
			}

			return template.URL(pathImg)
		},
		"timeFormatDay": func(day time.Time) string {
			return day.Format("2006-01-02")
		},
		"checkAppDev": func() bool {
			return configs.AppEnv == configs.AppDev
		},
	}
}


