package configs

import (
	"os"

	"github.com/joho/godotenv"
	"github.com/namsral/flag"
)

var (
	Domain       = "#"
	ViewsPath    = ""
	PublicPath   = ""
	PathImgHpv   = ""
	DomainImgHpv = ""

	LogLevel = "info"
	AppEnv   = "dev"

	AppDev  = "dev"
	AppProd = "prd"
	AppStg  = "stg"

	RedisUri              = ""
	MaxActive             = 0
	MaxIde                = 0
	defaultRedisMaxActive = 0 // 0 is unlimited max active connection
	defaultRedisMaxIdle   = 10
)

func init() {
	if err := ReadFileEnv(); err != nil {
		panic("read file env")
	}

	flag.StringVar(&LogLevel, "log-level", "info", "logger level for appsLog level: panic | fatal | error | warn | info | debug | trace")
	flag.StringVar(&AppEnv, "app-env", "dev1", "Env for service. Ex: dev | stg | prd")

	flag.StringVar(&RedisUri, "redis-uri", "redis://localhost:6379", "(For go-redis) Redis connection-string. Ex: redis://localhost/0")
	flag.IntVar(&MaxActive, "redis-pool-max-active", defaultRedisMaxActive, "(For go-redis) Override redis pool MaxActive")
	flag.IntVar(&MaxIde, "redis-pool-max-idle", defaultRedisMaxIdle, "(For go-redis) Override redis pool MaxIdle")

	flag.StringVar(&Domain, "domain", os.Getenv("DOMAIN"), "Domain https://example.com")
	flag.StringVar(&ViewsPath, "views-path", "./views", "Views path")
	flag.StringVar(&PublicPath, "public-path", "./public", "Public path")

	flag.StringVar(&PathImgHpv, "path-img-hpv", "./public", "Path img hpv")
	flag.StringVar(&DomainImgHpv, "domain-img-hpv", "https://media.hpv.vn", "domain img hpv")

	flag.Parse()
}

func ReadFileEnv() error {
	envFile := os.Getenv("ENV_FILE")
	if envFile == "" {
		envFile = ".env"
	}
	_, err := os.Stat(envFile)
	if err == nil {
		err := godotenv.Load(envFile)
		if err != nil {
			return err
		}
	}
	return nil
}
