package gorules

import (
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
)

var alphanumericPattern = regexp.MustCompile(`^[a-zA-Z0-9]+$`)

func RuleFakeId(fakeId string) bool {
	return alphanumericPattern.MatchString(fakeId)
}

func RuleValidateFakeId(fl validator.FieldLevel) bool {
	fakeId := fl.Field().Interface().(string)
	fakeId = strings.TrimSpace(fakeId)
	return alphanumericPattern.MatchString(fakeId)
}

func RuleValidateFakeIds(fl validator.FieldLevel) bool {
	fakeIds := fl.Field().Interface().([]string)
	for _, id := range fakeIds {
		id = strings.TrimSpace(id)
		if !alphanumericPattern.MatchString(id) {
			return false
		}
	}
	return true
}
