/**
 * Responsive break points
 */
$sm: 576px;
$md: 768px;
$lg: 992px;
$xl: 1200px;
$xxl: 1400px;

/**
 * Colors Brand
 */


// Neutral
$neutral-100: #000000;
$neutral-200: #222222;
$neutral-300: #464749;
$neutral-400: #666666;
$neutral-500: #adadad;
$neutral-600: #d6d6d6;
$neutral-700: #ebebeb;
$neutral-800: #f5f5f5;
$neutral-900: #ffffff;

// Primary
$primary_500: #009585;
// Secondary
$secondary-500: #FEE000;
// Third
$third-500: #005CFF;


:root {

   /* Neutral */
   --neutral-100: #000000;
   --neutral-200: #222222;
   --neutral-300: #464749;
   --neutral-400: #666666;
   --neutral-500: #adadad;
   --neutral-600: #d6d6d6;
   --neutral-700: #ebebeb;
   --neutral-800: #f5f5f5;
   --neutral-900: #ffffff;

   /* Primary */
   --primary-50: #004c3f;
   --primary-100: #b2dfda;
   --primary-200: #80cac3;
   --primary-300: #4db5aa;
   --primary-400: #26a598;
   --primary-500: #009585;
   --primary-600: #008879;
   --primary-700: #007869;
   --primary-800: #00685a;
   --primary-900: #004c3f;

   /* Secondary */
   --secondary-50: #fffce4;
   --secondary-100: #fff6bd;
   --secondary-200: #ffef91;
   --secondary-300: #ffe964;
   --secondary-400: #fee43d;
   --secondary-500: #FEE000;
   --secondary-600: #fdcd00;
   --secondary-700: #fbb500;
   --secondary-800: #fa9c00;
   --secondary-900: #f86f00;

   // Third
   --third-50: #e1f3ff;
   --third-100: #b8dfff;
   --third-200: #84ccff;
   --third-300: #3cb8ff;
   --third-400: #00a7ff;
   --third-500: #005CFF;
   --third-600: #0071ff;
   --third-700: #2836e0;
   --third-800: #2836e0;
   --third-900: #2836e0;

   // Forth
   --forth-500: #05A8AE;

   // Fifth
   --fifth-500: #1952A0;

   // Sixth
   --sixth-500: #4EC3BD;

   /* Functions color */
   --error: #FF513C;
   --info: #0085FF;
   --waring: #F9B746;
   --success: #64C550;

   /* Error variant */
   --error-50: #ffecef;
   --error-100: #ffd0d5;
   --error-200: #f59f9e --error-300: #ed7b78;
   --error-400: #f95d56;
   --error-500: #ff503c;
   /* Primary Error */
   --error-600: #f0453b;
   --error-700: #dd3b35;
   --error-800: #d0352e;
   --error-900: #c12a21;


   /* Info variant */
   --info-50: #e2f2ff;
   --info-100: #baddff;
   --info-200: #8cc9ff;
   --info-300: #58b3ff --info-400: #2ba3ff;
   --info-500: #0092ff;
   --info-600: #0084ff;
   /* Primary Info */
   --info-700: #1271eb;
   --info-800: #185fd8;
   --info-900: #1e3eb9;

   /* Warning variant */
   --warning-50: #fef3df;
   --warning-100: #fce0b0;
   --warning-200: #fbcc7c;
   --warning-300: #f9b746;
   /* Primary Warning */
   --warning-400: #f8a817;
   --warning-500: #f89900;
   --warning-600: #f48d00;
   --warning-700: #ef7d00;
   --warning-800: #e96d00;
   --warning-900: #e15200;

   /* Success variant */
   --success-50: #e9f7e6;
   --success-100: #caeac2;
   --success-200: #a7dc9b;
   --success-300: #81d071;
   --success-400: #64c550;
   /* Primary Success */
   --success-500: #46ba2b;
   --success-600: #3bab21;
   --success-700: #2d9813;
   --success-800: #1d8701;
   --success-900: #006800;


   /**
 * Responsive break points
 */
   --breakpoint-sm: 576px;
   --breakpoint-md: 768px;
   --breakpoint-lg: 992px;
   --breakpoint-xl: 1200px;
   --breakpoint-xxl: 1400px;

   /* Border-Radius */
   --radius-xs: 4px;
   --radius-sm: 12px;
   --radius-md: 20px;
   --radius-lg: 30px;
}

.bg-white {
   background-color: white;
   ;
}

.bg-primary {
   background-color: var(--primary-500);
}

.bg-secondary {
   background-color: var(--secondary-500);
}

.bg-third {
   background-color: var(--third-500);
}


.bg-gradient-primary {

   // background-size: 150% 100%;
   background-position: 0px 0px,
0px 0px,
0px 0px,
0px 0px,
0px 0px,
0px 0px,
0px 0px,
0px 0px,
0px 0px,
0px 0px,
0px 0px;
background-image: radial-gradient(25% 70% at -1% 91%, #38ab9f 1%, #38ab9f 1%, #073AFF00 91%),
radial-gradient(30% 53% at 62% 163%, #73F2FFFF 0%, #073AFF00 100%),
linear-gradient(125deg, #bdfbe8 8%, #d3e9d6 44%, #ACE0C8FF 93%); // background: linear-gradient(to bottom right,
   //       #f8fcf8 0%,
   //       #e1f5ea 20%,
   //       #c9efe5 40%,
   //       #a1e4db 60%,
   //       #7cd3ce 80%,
   //       #2fa89b 100%);
}

.bg-gradient-secondary {
   background: linear-gradient(to bottom right,
         #ffffff 0%,
         #dcecff 20%,
         #90d5ff 40%,
         #77c3ff 50%,
         #b3e2a2 60%,
         #eef3c4 70%,
         #ffed7f 80%,
         #ffe87c 90%,
         #ffef82 100%)
}

.bg-gradient-third {
   background: linear-gradient(to bottom right,
         #e0f8f8 0%,
         #b1eced 25%,
         #7bd6e6 50%,
         #53b7e8 75%,
         #306eff 100%)
}


.text-white {
   color: white;
}

.text-black {
   color: var(--neutral-200);
}

// Box Shadow 
.box-shadow-sm {
   box-shadow: 0px 3px 7px 0px #3333333f;
}

.box-shadow-md {
   box-shadow: 0px 4px 10px 0px #3333333f;
}

.box-shadow-lg {
   box-shadow: 0px 4px 15px 0px #3333333f;
}

::-webkit-scrollbar-track {
   height: 50%;
   max-height: 50%
}

::-webkit-scrollbar {
   border-radius: 16px;
   height: 5px;
   width: 4px
}

::-webkit-scrollbar-thumb {
   border-radius: 16px;
   box-shadow: inset 0 0 6px #151515
}