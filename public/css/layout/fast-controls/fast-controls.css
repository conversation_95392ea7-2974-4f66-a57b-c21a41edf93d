.mobile-fast-action-controls {
  font-size: 1rem;
  position: fixed;
  bottom: 7.3rem;
  left: 0;
  right: 0;
  z-index: 42;
}
.mobile-fast-action-controls .action-items {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
}
.mobile-fast-action-controls .action-items .action-item {
  display: flex;
  flex-direction: row;
  border-radius: 50px;
  padding: 6px 12px;
  color: white;
  font-size: 10.5px;
  background-color: var(--primary-500);
  vertical-align: middle;
  align-items: center;
  gap: 7px;
  font-weight: 600;
}
.mobile-fast-action-controls .action-items .action-item.active {
  background-color: var(--primary-800);
}
.mobile-fast-action-controls .action-items .action-item img {
  width: 20px;
  height: 18px;
}
.mobile-fast-action-controls .action-items .action-item #btn-chat-now img {
  width: 16px;
}

@media (min-width: 1100px) {
  .mobile-fast-action-controls {
    display: none;
  }
}/*# sourceMappingURL=fast-controls.css.map */