.menu-footer-mobile {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: white;
  padding: 8px 10px;
  z-index: 43;
}
.menu-footer-mobile .list-menu-items {
  display: flex;
  gap: 25px;
  align-items: center;
  justify-content: space-around;
}
.menu-footer-mobile .list-menu-items .menu-item {
  height: -moz-fit-content;
  height: fit-content;
}
.menu-footer-mobile .list-menu-items .menu-item a {
  border-radius: 50px;
  padding: 12px 12px;
  color: white;
  font-size: 18px;
  background-color: var(--forth-500);
  display: inline-block;
  vertical-align: middle;
}
.menu-footer-mobile .list-menu-items .menu-item a.active {
  background-color: var(--primary-800);
}

@media (min-width: 1100px) {
  .menu-footer-mobile {
    display: none;
  }
}