.menu-footer-mobile {
   position: fixed;
   bottom: 0;
   width: 100%;
   background-color: white;
   padding: 8px 10px;
   z-index: 43;


   .list-menu-items {
      display: flex;
      gap: 25px;
      align-items: center;
      justify-content: space-around;

      .menu-item {
         height: fit-content;

         a {
            border-radius: 50px;
            padding: 12px 12px;
            color: white;
            font-size: 18px;
            background-color: var(--forth-500);
            display: inline-block;
            vertical-align: middle;

            &.active {
               background-color: var(--primary-800);
            }
         }
      }
   }
}

@media (min-width: 1100px) {
   .menu-footer-mobile {
      display: none;
   }
}