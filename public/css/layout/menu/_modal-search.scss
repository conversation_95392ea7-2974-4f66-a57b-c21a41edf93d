.modal-search#modal-search {
   position: absolute;
   left: 0;
   right: 0;
   bottom: 0;
   z-index: 102;
   top: calc(100% + 1em);
   transition: all 200ms ease-in-out;
   display: none;
   opacity: 0;
   height: 0;
   max-height: fit-content;

   &.active {
      display: block !important;
      opacity: 1;
      height: 100vh;
      height: calc-size(auto);
   }


   .search-container {
      width: 100%;
      opacity: 1;

      // overflow-y: auto;

      translate: 0 0;
      transition-property: opacity;
      transition-duration: .4s;
      transition-behavior: allow-discrete;
      -o-transition-timing-function: linear;

      @starting-style {
         opacity: 0;
         translate: 0 25vh;
      }
   }

   .overlay {
      display: none;
      position: fixed;
      z-index: -1;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background-color: #00000086;
   }


}

.search-content {
   // max-width: 1200px;
   display: flex;
   flex-direction: column;
   gap: 1em;
   padding: 2em 2.5em;
   position: relative;
   font-size: 1rem;
   margin: auto;
   background-color: white;
   border-radius: var(--radius-lg);
   box-shadow: 0em 2em 5em rgba(51, 51, 51, 0.3);
   max-width: 98%;

   .search-control {
      margin-top: 3em;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 1em;
      width: 100%;
      max-width: 900px;
      margin: auto;

      input,
      button {
         height: 3.2em;
      }

      input {
         font-size: 1.8em;
         padding: 8px 12px;
         border-radius: var(--radius-sm);
         border: 1.5px solid var(--primary-500);
         flex-grow: 1;

         &:focus {
            outline: none;
         }
      }

      button {
         font-size: 1.8em;
         padding: 0.5em 2em;
         background-color: var(--primary-500);
         color: white;
         border: none;
         border-radius: var(--radius-sm);
         text-transform: uppercase;
         line-height: 1;
         cursor: pointer;
         transition: background-color 200ms ease-in-out;

         &:hover {
            background-color: var(--primary-700);
         }
      }

      span.close-modal {
         position: absolute;
         right: 1em;
         width: 2.5em;
         height: 2.5em;
         border-radius: 50%;
         color: var(--neutral-300);
         background-color: rgba(0, 0, 0, 0.1254901961);
         transition: all 0.2s ease-in-out;
         display: flex;
         align-items: center;
         justify-content: center;
         font-size: 2em;
         cursor: pointer;

         &:hover {
            background-color: var(--primary-100);
            color: var(--primary-700);
            ;
         }
      }
   }

   .results-search {
      display: flex;
      flex-wrap: wrap;
      gap: 1em;
      margin-top: 2em;
      font-size: 1rem;
      width: 100%;
      align-content: flex-start;
      max-height: calc(100svh - 33em);
      overflow-y: auto;

      .title {
         font-size: 2em;
         font-weight: 400;
         text-transform: uppercase;
         width: 100%;
         text-align: center;
      }
   }
}


.list-results {
   display: flex;
   flex-wrap: wrap;
   gap: 2.5em;
   margin: 2em 0;
   width: 100%;
   justify-content: center;
   // display: grid;
   // grid-template-columns: repeat(auto-fill, 25em);
   // grid-gap: 10px;
   // justify-content: center;
   /* Chia các cột với kích thước tối thiểu là 15em */
   gap: 2.2em;
   width: 100%;
   height: fit-content;

   /* Khoảng cách giữa các phần tử */

   .result-item {
      min-width: 15%;
      max-width: 15em;

      cursor: pointer;
      display: flex;
      flex-direction: column;
      font-size: 1.5em;
      border-radius: var(--radius-sm);
      box-shadow: 0 0 2.5em rgba(0, 0, 0, 0.05);
      overflow: hidden;
      flex: 1;
      transition: all 0.25s linear;
      transform: scale(1);

      &__avatar {
         aspect-ratio: 3 / 2;

         img {
            width: 100%;
            height: 100%;
            object-fit: cover;
         }
      }

      &__des {
         padding: 1em;
         color: white !important;
         background-color: var(--primary-500);
         font-size: 0.8em;
         font-weight: 400;
         position: relative;
         z-index: 1;
         flex-grow: 1;

         a {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.5em;
            max-height: 4.5em;
         }
      }

      &:hover {
         transform: scale(1.05);
         box-shadow: 0 1em 1.5em rgba(0, 0, 0, 0.2);
      }
   }
}

@media (max-width: 1099px) {
   .search-content {
      max-width: 100%;
   }

   .modal-search#modal-search {
      top: 0;
      z-index: 99999;

      .overlay {
         display: block;
         backdrop-filter: blur(6px);
      }

      .search-control {
         margin: 1.2em auto;
      }

      .search-content {
         border-radius: 0;
         height: calc(100svh - 12.5em);
         padding: 1em;

         .results-search {
            max-height: calc(100svh - 14em);
            overflow-y: auto;
         }

         input,
         button {
            font-size: 1.5em;
         }

         button {
            padding: 0.5em 1em;
         }

         span.close-modal {
            position: static;
         }
      }
   }
}

@media (max-width: 768px) {
   .modal-search#modal-search .search-content .results-search .list-results {
      .result-item {
         min-width: 12em;
      }
   }
}