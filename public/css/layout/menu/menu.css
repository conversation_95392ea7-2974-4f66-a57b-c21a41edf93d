@charset "UTF-8";
.modal-search#modal-search {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 102;
  top: calc(100% + 1em);
  transition: all 200ms ease-in-out;
  display: none;
  opacity: 0;
  height: 0;
  max-height: -moz-fit-content;
  max-height: fit-content;
}
.modal-search#modal-search.active {
  display: block !important;
  opacity: 1;
  height: 100vh;
  height: calc-size(auto);
}
.modal-search#modal-search .search-container {
  width: 100%;
  opacity: 1;
  translate: 0 0;
  transition-property: opacity;
  transition-duration: 0.4s;
  transition-behavior: allow-discrete;
  -o-transition-timing-function: linear;
}
@starting-style {
  .modal-search#modal-search .search-container {
    opacity: 0;
    translate: 0 25vh;
  }
}
.modal-search#modal-search .overlay {
  display: none;
  position: fixed;
  z-index: -1;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5254901961);
}

.search-content {
  display: flex;
  flex-direction: column;
  gap: 1em;
  padding: 2em 2.5em;
  position: relative;
  font-size: 1rem;
  margin: auto;
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: 0em 2em 5em rgba(51, 51, 51, 0.3);
  max-width: 98%;
}
.search-content .search-control {
  margin-top: 3em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1em;
  width: 100%;
  max-width: 900px;
  margin: auto;
}
.search-content .search-control input,
.search-content .search-control button {
  height: 3.2em;
}
.search-content .search-control input {
  font-size: 1.8em;
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  border: 1.5px solid var(--primary-500);
  flex-grow: 1;
}
.search-content .search-control input:focus {
  outline: none;
}
.search-content .search-control button {
  font-size: 1.8em;
  padding: 0.5em 2em;
  background-color: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  line-height: 1;
  cursor: pointer;
  transition: background-color 200ms ease-in-out;
}
.search-content .search-control button:hover {
  background-color: var(--primary-700);
}
.search-content .search-control span.close-modal {
  position: absolute;
  right: 1em;
  width: 2.5em;
  height: 2.5em;
  border-radius: 50%;
  color: var(--neutral-300);
  background-color: rgba(0, 0, 0, 0.1254901961);
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2em;
  cursor: pointer;
}
.search-content .search-control span.close-modal:hover {
  background-color: var(--primary-100);
  color: var(--primary-700);
}
.search-content .results-search {
  display: flex;
  flex-wrap: wrap;
  gap: 1em;
  margin-top: 2em;
  font-size: 1rem;
  width: 100%;
  align-content: flex-start;
  max-height: calc(100svh - 33em);
  overflow-y: auto;
}
.search-content .results-search .title {
  font-size: 2em;
  font-weight: 400;
  text-transform: uppercase;
  width: 100%;
  text-align: center;
}

.list-results {
  display: flex;
  flex-wrap: wrap;
  gap: 2.5em;
  margin: 2em 0;
  width: 100%;
  justify-content: center;
  /* Chia các cột với kích thước tối thiểu là 15em */
  gap: 2.2em;
  width: 100%;
  height: -moz-fit-content;
  height: fit-content;
  /* Khoảng cách giữa các phần tử */
}
.list-results .result-item {
  min-width: 15%;
  max-width: 15em;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  font-size: 1.5em;
  border-radius: var(--radius-sm);
  box-shadow: 0 0 2.5em rgba(0, 0, 0, 0.05);
  overflow: hidden;
  flex: 1;
  transition: all 0.25s linear;
  transform: scale(1);
}
.list-results .result-item__avatar {
  aspect-ratio: 3/2;
}
.list-results .result-item__avatar img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.list-results .result-item__des {
  padding: 1em;
  color: white !important;
  background-color: var(--primary-500);
  font-size: 0.8em;
  font-weight: 400;
  position: relative;
  z-index: 1;
  flex-grow: 1;
}
.list-results .result-item__des a {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5em;
  max-height: 4.5em;
}
.list-results .result-item:hover {
  transform: scale(1.05);
  box-shadow: 0 1em 1.5em rgba(0, 0, 0, 0.2);
}

@media (max-width: 1099px) {
  .search-content {
    max-width: 100%;
  }
  .modal-search#modal-search {
    top: 0;
    z-index: 99999;
  }
  .modal-search#modal-search .overlay {
    display: block;
    -webkit-backdrop-filter: blur(6px);
            backdrop-filter: blur(6px);
  }
  .modal-search#modal-search .search-control {
    margin: 1.2em auto;
  }
  .modal-search#modal-search .search-content {
    border-radius: 0;
    height: calc(100svh - 12.5em);
    padding: 1em;
  }
  .modal-search#modal-search .search-content .results-search {
    max-height: calc(100svh - 14em);
    overflow-y: auto;
  }
  .modal-search#modal-search .search-content input,
  .modal-search#modal-search .search-content button {
    font-size: 1.5em;
  }
  .modal-search#modal-search .search-content button {
    padding: 0.5em 1em;
  }
  .modal-search#modal-search .search-content span.close-modal {
    position: static;
  }
}
@media (max-width: 768px) {
  .modal-search#modal-search .search-content .results-search .list-results .result-item {
    min-width: 12em;
  }
}
.popup-menu-mobile {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 45;
  transition: all 250ms ease-in-out;
}
.popup-menu-mobile .overlay {
  position: absolute;
  z-index: 0;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(51, 51, 51, 0.4156862745);
}
.popup-menu-mobile .container-menu-mobile {
  position: absolute;
  z-index: 2;
  left: 20px;
  top: 20px;
  max-width: 30em;
  background: linear-gradient(130.73deg, rgba(255, 255, 255, 0.8352) -15%, rgba(189, 255, 219, 0.96) 110%);
  width: -moz-fit-content;
  width: fit-content;
  background-color: white;
  border-radius: var(--radius-md);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: var(--neutral-400);
}
.popup-menu-mobile .container-menu-mobile .logos {
  width: 100%;
  justify-content: center;
  display: flex;
  padding: 1.5em;
  background-color: var(--neutral-900);
}
.popup-menu-mobile .container-menu-mobile .logos img {
  height: 3.6em;
}
.popup-menu-mobile .container-menu-mobile .nav-menu {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 1.5em 1em;
}
.popup-menu-mobile .container-menu-mobile .nav-menu__item {
  font-size: 15px;
  color: var(--primary-500);
  display: inline;
}
.popup-menu-mobile .container-menu-mobile .nav-menu__item a {
  padding: 10px 12px;
  width: 100%;
  display: inline-block;
}
.popup-menu-mobile .container-menu-mobile .nav-menu__item:hover {
  background: linear-gradient(269.45deg, rgba(32, 133, 233, 0.11) -10.77%, var(--primary-500) 77.84%);
  border-radius: 16px;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  color: var(--neutral-900);
}
.popup-menu-mobile .container-menu-mobile .nav-menu__item.is-male:hover {
  background: linear-gradient(269.45deg, rgba(32, 133, 233, 0.11) -10.77%, var(--third-500) 77.84%);
  border-radius: 16px;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  color: var(--neutral-900);
}
.popup-menu-mobile .container-menu-mobile .nav-menu__item.is-female:hover {
  background: linear-gradient(269.45deg, rgba(255, 229, 15, 0.11) -10.77%, var(--secondary-500) 77.84%);
  border-radius: 16px;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  color: var(--neutral-100);
}
.popup-menu-mobile .container-menu-mobile .popup-menu-mobile__footer {
  display: block;
  padding: 1.5em 2em;
  position: relative;
}
.popup-menu-mobile .container-menu-mobile .popup-menu-mobile__footer:after {
  content: "";
  background: rgba(109, 109, 109, 0.5490196078);
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 1px;
  width: 50%;
}
.popup-menu-mobile .container-menu-mobile .popup-menu-mobile__footer .content-menu-footer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  gap: 1em;
}
.popup-menu-mobile .container-menu-mobile .popup-menu-mobile__footer .content-menu-footer span {
  line-height: 1.1;
}
.popup-menu-mobile .container-menu-mobile .popup-menu-mobile__footer .content-menu-footer .policy {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  line-height: 1.4;
  font-weight: 600;
}

.main-menu {
  display: flex;
  border-radius: 50px;
  flex: auto;
  align-items: center;
  justify-content: space-between;
  padding: 0.5em 1.8em;
  background-color: transparent;
  box-shadow: none;
  position: relative;
}
.main-menu .btn-open-menu-mobile {
  cursor: pointer;
  font-size: 2.2em;
  padding: 10px 10px;
  border-radius: var(--radius-sm);
  transition: all 250ms ease-in-out;
  color: var(--primary-800);
}
.main-menu .btn-open-menu-mobile:hover {
  color: white;
  background-color: var(--primary-800);
}
.main-menu .logos {
  display: flex;
  gap: 10px;
}
.main-menu .logos img {
  height: 3.6em;
}
.main-menu .nav-menu {
  justify-content: space-between;
  align-items: center;
  gap: 0.4em;
  display: flex;
}
.main-menu .nav-menu ul {
  display: none;
  gap: 0.3em;
  width: 100%;
  justify-content: center;
  justify-content: flex-end;
}
.main-menu .nav-menu__item {
  display: inline-block;
}
.main-menu .nav-menu__item a {
  text-transform: capitalize;
  text-decoration: none;
  display: block;
  padding: 0.6em 1.5em;
  border: 1px solid var(--primary-800);
  border-radius: 50px;
  font-size: 1em;
  transition: all 0.25s ease-in-out;
  color: var(--primary-800);
  font-weight: 400;
  text-align: center;
  transition: all 0.4s;
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.main-menu .nav-menu__item a:before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: calc(100% + 2px);
  background-color: var(--primary-800);
  transition: all 0.3s;
  border-radius: 10rem;
  z-index: -1;
  left: -2px;
}
.main-menu .nav-menu__item a:hover {
  background-color: var(--primary-800);
  border-color: var(--primary-500);
  color: white;
}
.main-menu .nav-menu__item.active a {
  color: green;
}
.main-menu .nav-menu .search-control {
  padding: 8px;
  cursor: pointer;
  border-radius: 50%;
  color: var(--primary-800);
  transition: all 0.25s ease-in-out;
}
.main-menu .nav-menu .search-control svg {
  font-size: 30px;
}
.main-menu .nav-menu .search-control:hover {
  background-color: var(--primary-800);
  color: white;
}
.main-menu .btn-open-menu-mobile {
  display: block;
}

@media (min-width: 1100px) {
  .main-menu {
    background-color: white;
    justify-content: flex-start;
  }
  .main-menu .btn-open-menu-mobile {
    display: none;
  }
  .main-menu .nav-menu {
    flex-grow: 1;
  }
  .main-menu .nav-menu ul {
    flex-grow: 1;
    display: flex;
  }
  .main-menu .nav-menu ul .nav-menu__item {
    transition: all 250ms ease-in-out;
  }
  .main-menu .nav-menu ul .nav-menu__item.is-female a:hover {
    color: var(--neutral-100);
    background-color: var(--secondary-500);
    border-color: var(--secondary-500);
  }
  .main-menu .nav-menu ul .nav-menu__item.is-female a:hover:before {
    background-color: var(--secondary-500);
  }
  .main-menu .nav-menu ul .nav-menu__item.is-male a:hover {
    color: var(--neutral-900);
    background-color: var(--third-500);
    border-color: var(--third-500);
  }
  .main-menu .nav-menu ul .nav-menu__item.is-male a:hover:before {
    background-color: var(--third-500);
  }
  .popup-search .container-search {
    width: 400px;
  }
}
@media (min-width: 1300px) {
  .main-menu {
    font-size: 1.1rem;
  }
  .main-menu .nav-menu {
    display: flex;
  }
  .main-menu .nav-menu ul {
    gap: 1em;
  }
  .main-menu .nav-menu__item.active a {
    color: green;
  }
}
@media (min-width: 1800px) {
  .main-menu {
    font-size: 1.3rem;
  }
}