@import url("https://fonts.googleapis.com/css2?family=Source+Serif+4:ital,opsz,wght@0,8..60,200..900;1,8..60,200..900&family=Unbounded:wght@200..900&display=swap");
html {
  font-size: 55.25%;
}

a {
  color: inherit;
}

body {
  font-family: "Unbounded", sans-serif;
  font-optical-sizing: auto;
  font-weight: 300;
  font-style: normal;
  -webkit-overflow-scrolling: auto;
  font-display: swap;
}

body {
  min-height: 100vh;
  margin: 0;
  overflow-x: hidden;
}

svg {
  width: 1em;
  height: 1em;
  overflow: visible;
}

@media (min-width: 1100px) {
  html {
    font-size: 60.25%;
  }
}
/*
  1. Use a more-intuitive box-sizing model.
*/
*,
*::before,
*::after {
  box-sizing: border-box;
}

/*
   2. Remove default margin
 */
* {
  margin: 0;
}

/*
   Typographic tweaks!
   3. Add accessible line-height
   4. Improve text rendering
 */
body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

/*
   5. Improve media defaults
 */
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

/*
   6. Remove built-in form typography styles
 */
input,
button,
textarea,
select {
  font: inherit;
}

/*
   7. Avoid text overflows
 */
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

a {
  text-decoration: none;
}

/*
   8. Create a root stacking context
 */
#root,
#__next {
  isolation: isolate;
}

/**
 * Responsive break points
 */
/**
 * Colors Brand
 */
:root {
  /* Neutral */
  --neutral-100: #000000;
  --neutral-200: #222222;
  --neutral-300: #464749;
  --neutral-400: #666666;
  --neutral-500: #adadad;
  --neutral-600: #d6d6d6;
  --neutral-700: #ebebeb;
  --neutral-800: #f5f5f5;
  --neutral-900: #ffffff;
  /* Primary */
  --primary-50: #004c3f;
  --primary-100: #b2dfda;
  --primary-200: #80cac3;
  --primary-300: #4db5aa;
  --primary-400: #26a598;
  --primary-500: #009585;
  --primary-600: #008879;
  --primary-700: #007869;
  --primary-800: #00685a;
  --primary-900: #004c3f;
  /* Secondary */
  --secondary-50: #fffce4;
  --secondary-100: #fff6bd;
  --secondary-200: #ffef91;
  --secondary-300: #ffe964;
  --secondary-400: #fee43d;
  --secondary-500: #FEE000;
  --secondary-600: #fdcd00;
  --secondary-700: #fbb500;
  --secondary-800: #fa9c00;
  --secondary-900: #f86f00;
  --third-50: #e1f3ff;
  --third-100: #b8dfff;
  --third-200: #84ccff;
  --third-300: #3cb8ff;
  --third-400: #00a7ff;
  --third-500: #005CFF;
  --third-600: #0071ff;
  --third-700: #2836e0;
  --third-800: #2836e0;
  --third-900: #2836e0;
  --forth-500: #05A8AE;
  --fifth-500: #1952A0;
  --sixth-500: #4EC3BD;
  /* Functions color */
  --error: #FF513C;
  --info: #0085FF;
  --waring: #F9B746;
  --success: #64C550;
  /* Error variant */
  --error-50: #ffecef;
  --error-100: #ffd0d5;
  --error-200: #f59f9e --error-300: #ed7b78;
  --error-400: #f95d56;
  --error-500: #ff503c;
  /* Primary Error */
  --error-600: #f0453b;
  --error-700: #dd3b35;
  --error-800: #d0352e;
  --error-900: #c12a21;
  /* Info variant */
  --info-50: #e2f2ff;
  --info-100: #baddff;
  --info-200: #8cc9ff;
  --info-300: #58b3ff --info-400: #2ba3ff;
  --info-500: #0092ff;
  --info-600: #0084ff;
  /* Primary Info */
  --info-700: #1271eb;
  --info-800: #185fd8;
  --info-900: #1e3eb9;
  /* Warning variant */
  --warning-50: #fef3df;
  --warning-100: #fce0b0;
  --warning-200: #fbcc7c;
  --warning-300: #f9b746;
  /* Primary Warning */
  --warning-400: #f8a817;
  --warning-500: #f89900;
  --warning-600: #f48d00;
  --warning-700: #ef7d00;
  --warning-800: #e96d00;
  --warning-900: #e15200;
  /* Success variant */
  --success-50: #e9f7e6;
  --success-100: #caeac2;
  --success-200: #a7dc9b;
  --success-300: #81d071;
  --success-400: #64c550;
  /* Primary Success */
  --success-500: #46ba2b;
  --success-600: #3bab21;
  --success-700: #2d9813;
  --success-800: #1d8701;
  --success-900: #006800;
  /**
  * Responsive break points
  */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
  /* Border-Radius */
  --radius-xs: 4px;
  --radius-sm: 12px;
  --radius-md: 20px;
  --radius-lg: 30px;
}

.bg-white {
  background-color: white;
}

.bg-primary {
  background-color: var(--primary-500);
}

.bg-secondary {
  background-color: var(--secondary-500);
}

.bg-third {
  background-color: var(--third-500);
}

.bg-gradient-primary {
  background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px;
  background-image: radial-gradient(25% 70% at -1% 91%, #38ab9f 1%, #38ab9f 1%, rgba(7, 58, 255, 0) 91%), radial-gradient(30% 53% at 62% 163%, #73f2ff 0%, rgba(7, 58, 255, 0) 100%), linear-gradient(125deg, #bdfbe8 8%, #d3e9d6 44%, #ace0c8 93%);
}

.bg-gradient-secondary {
  background: linear-gradient(to bottom right, #ffffff 0%, #dcecff 20%, #90d5ff 40%, #77c3ff 50%, #b3e2a2 60%, #eef3c4 70%, #ffed7f 80%, #ffe87c 90%, #ffef82 100%);
}

.bg-gradient-third {
  background: linear-gradient(to bottom right, #e0f8f8 0%, #b1eced 25%, #7bd6e6 50%, #53b7e8 75%, #306eff 100%);
}

.text-white {
  color: white;
}

.text-black {
  color: var(--neutral-200);
}

.box-shadow-sm {
  box-shadow: 0px 3px 7px 0px rgba(51, 51, 51, 0.2470588235);
}

.box-shadow-md {
  box-shadow: 0px 4px 10px 0px rgba(51, 51, 51, 0.2470588235);
}

.box-shadow-lg {
  box-shadow: 0px 4px 15px 0px rgba(51, 51, 51, 0.2470588235);
}

::-webkit-scrollbar-track {
  height: 50%;
  max-height: 50%;
}

::-webkit-scrollbar {
  border-radius: 16px;
  height: 5px;
  width: 4px;
}

::-webkit-scrollbar-thumb {
  border-radius: 16px;
  box-shadow: inset 0 0 6px #151515;
}

.text-neutral {
  color: var(--neutral-500);
}

.text-primary {
  color: var(--primary-500);
}

.text-secondary {
  color: var(--secondary-500);
}

.text-forth {
  color: var(--forth-500);
}

.text-fifth {
  color: var(--fifth-500);
}

.text-sixth {
  color: var(--sixth-500);
}

.btn-primary-outline {
  display: inline-block;
  border: 1px solid var(--primary-500);
  color: var(--neutral-200);
  transition: all 0.25s ease-in-out;
  cursor: pointer;
  text-decoration: none;
  padding: 5px 20px;
  background-color: white;
  font-size: 15px;
  text-align: center;
}
.btn-primary-outline:hover {
  color: white;
  background-color: var(--primary-500);
}

.btn.full-radius {
  border-radius: 50px;
}

.btn-primary-animation,
.btn-dark-primary-animation {
  transition: all 0.4s;
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.btn-primary-animation:before,
.btn-dark-primary-animation:before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: calc(100% + 2px);
  background-color: var(--primary-500);
  transition: all 0.3s;
  border-radius: 10rem;
  z-index: -1;
  left: -2px;
}
.btn-primary-animation:hover,
.btn-dark-primary-animation:hover {
  background-color: transparent;
  border-color: var(--primary-500);
  color: white;
}
.btn-primary-animation:hover:before,
.btn-dark-primary-animation:hover:before {
  width: calc(100% + 4px);
}

.btn-dark-primary-animation:before {
  background-color: var(--primary-800);
}

.btn-primary-animation.is-blue:before {
  background-color: var(--third-800);
}

.btn-primary-animation.is-yellow:before {
  background-color: var(--secondary-800);
}

/* HTML MSD-Loader: 
<div class="msd-loader-container">
<div class="msd-loader"></div>
<div class="msd-loader-text">Loading...</div>
</div> */
.msd-loader-container {
  margin: 5.5%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2.2em;
  justify-content: center;
  font-size: 1rem;
}

.msd-loader-text {
  font-size: 2.2rem;
  color: var(--primary-500);
  font-weight: 400;
}

.msd-loader {
  width: 2.5em;
  height: 2.5em;
  position: relative;
  --c: no-repeat linear-gradient(#25b09b 0 0);
  background: var(--c) center/100% 10px, var(--c) center/10px 100%;
}
.msd-loader:before {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--c) 0 0, var(--c) 100% 0, var(--c) 0 100%, var(--c) 100% 100%;
  background-size: 1.5em 1.5em;
  animation: msd-animation 1.5s infinite cubic-bezier(0.3, 1, 0, 1);
}

@keyframes msd-animation {
  33% {
    inset: -10px;
    transform: rotate(0deg);
  }
  66% {
    inset: -10px;
    transform: rotate(90deg);
  }
  100% {
    inset: 0;
    transform: rotate(90deg);
  }
}
.modal-start-hbs {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  transition: all 250ms ease-in-out;
  font-size: 1.2rem;
  display: none;
}
.modal-start-hbs .overlay {
  position: absolute;
  z-index: 0;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(51, 51, 51, 0.4);
  transition: all 200ms ease-in-out;
  opacity: 0;
}
.modal-start-hbs.active .modal-content {
  transform: scale(1);
  opacity: 1;
}
.modal-start-hbs.active .overlay {
  opacity: 1;
}
.modal-start-hbs .ribbon-white {
  left: 0;
  top: -28px;
  width: 65%;
  height: 40px;
  border-top-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
  background-color: white;
  position: absolute;
  z-index: -1;
}
.modal-start-hbs .title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5em;
  font-weight: 500;
  color: var(--neutral-200);
}
.modal-start-hbs .title__left {
  font-size: 1.6em;
  line-height: 1.3;
  padding-left: 1em;
  margin-bottom: 0.4em;
}
.modal-start-hbs .modal-content {
  border-bottom-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  border-top-left-radius: 0;
  padding-top: 0;
  max-width: 45em;
  transition: all 200ms ease-in-out;
  background-color: white;
  transform: scale(0.3);
  opacity: 0;
  transform-origin: right bottom;
  padding: 2em;
  padding-top: 0;
}
.modal-start-hbs .content .questions {
  display: flex;
  flex-direction: column;
  gap: 0.8em;
}
.modal-start-hbs .content .questions .question-item {
  background-color: var(--primary-900);
  color: white;
  border-radius: var(--radius-md);
  font-size: 1.3em;
  display: flex;
  flex-direction: row;
  gap: 0.2em;
  font-weight: 400;
  justify-content: space-between;
}
.modal-start-hbs .content .questions .question-item p {
  flex-grow: 1;
  font-size: 1em;
  padding: 1.5em;
}
.modal-start-hbs .content .questions .question-item .item-selects {
  background-color: var(--primary-300);
  padding: 1em;
  border-radius: var(--radius-md);
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}
.modal-start-hbs .content .questions .question-item .item-selects label {
  text-transform: uppercase;
  font-size: 1em;
  border-radius: 4em;
  color: var(--neutral-300);
  background-color: white;
  padding: 0.6em 1.2em;
  border: 1px solid white;
  text-align: center;
  white-space: nowrap;
  width: 9em;
  font-weight: 400;
  cursor: pointer;
  transition: background-color 200ms ease-out;
  line-height: 1.8;
}
.modal-start-hbs .content .questions .question-item .item-selects label:hover {
  color: white;
  background-color: transparent;
}
.modal-start-hbs .content .questions .question-item .item-selects input[type=radio]:checked + label {
  color: white;
  background-color: transparent;
}
.modal-start-hbs .content .questions .question-item .item-selects input[type=radio]:checked + label:hover {
  color: white;
  background-color: transparent;
}
.modal-start-hbs .thank-you {
  border-radius: var(--radius-md);
  padding-top: 0;
  max-width: 45em;
  transition: all 200ms ease-in-out;
  background-color: var(--primary-300);
  transform: scale(0.3);
  opacity: 0;
  transform-origin: right bottom;
  font-size: 1.8em;
  font-weight: 600;
  color: white;
  padding: 1.5em;
  margin-top: 1em;
  display: none;
}
.modal-start-hbs .thank-you.active {
  transform: scale(1);
  opacity: 1;
}
.modal-start-hbs .modal-container {
  z-index: 9;
  position: absolute;
  bottom: 4em;
  right: 6em;
}

@media (max-width: 1099px) {
  .modal-start-hbs .title__left {
    font-size: 1.6em;
    margin-top: 1em;
  }
  .modal-start-hbs .modal-container {
    left: 0.3em;
    right: 0.3em;
    margin: auto;
    padding: 2em;
    padding-top: 0;
    bottom: 50%;
    transform: translateY(50%);
  }
  .modal-start-hbs .content {
    font-size: 1em;
  }
  .modal-start-hbs .content .questions .question-item p {
    padding: 1em;
    font-size: 0.9em;
  }
  .modal-start-hbs .content .questions .question-item .item-selects label {
    padding: 0.5em 1em;
    font-size: 0.8em;
  }
  .modal-start-hbs .thank-you {
    background-color: white;
    color: var(--neutral-200);
    padding: 1.2em;
    font-size: 1.5em;
    font-weight: 500;
  }
}
.container,
.container-fluid {
  width: 100%;
  padding-right: 0.75rem;
  padding-left: 0.75rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 1100px) {
  .container {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
  }
}
@media (min-width: 1500px) {
  .container {
    max-width: 1728px;
  }
}
.footer-container {
  text-align: center;
  color: var(--primary-500);
  margin-top: 0px;
  display: block;
}

.footer__content {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: center;
  color: var(--neutral-200);
  padding: 1em;
  text-align: center;
  align-items: center;
  font-size: 0.625vw;
}
.footer__content .code-footer {
  display: inline-block;
}
.footer__content a,
.footer__content h3 {
  font-size: 1.5em;
  font-weight: 500;
  display: inline-block;
  color: var(--neutral-300);
}
.footer__content h3 {
  font-weight: 300;
}
.footer__content a {
  font-size: 1.5em;
  font-weight: 500;
  color: var(--neutral-300);
}
.footer__content a:hover {
  color: var(--primary-500);
}
.footer__content .direction {
  display: flex;
  align-items: center;
  gap: 1em;
}

@media (max-width: 1099px) {
  .footer-container {
    display: none;
  }
  .footer__content .code-footer {
    display: contents;
  }
  .footer__content a,
  .footer__content h3 {
    font-size: 1em;
  }
  .footer__content .direction {
    margin-top: 1em;
  }
}
html {
  scroll-behavior: smooth;
}

.root-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 100vh;
}

.menu-header {
  position: sticky;
  top: 0px;
  z-index: 45;
  margin-top: 0;
}
.menu-header.is-sticky {
  border-radius: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: white;
  margin: 0;
  width: 100vw;
}

.container-content {
  display: flex;
  flex-direction: column;
  flex: auto;
  gap: 10px;
}
.container-content .main-container {
  max-height: calc(100svh - 170px);
  overflow-y: auto;
  flex-grow: 1;
  width: 100;
  display: flex;
  flex: auto;
  flex-direction: column;
  gap: 0px;
  border-radius: var(--radius-md);
  position: fixed;
  left: 1em;
  right: 1em;
}
.container-content .main-container .banner-main {
  border-top-left-radius: var(--radius-lg);
  border-top-right-radius: var(--radius-lg);
}
.container-content .main-container .banner-main__desktop {
  display: none;
}
.container-content .main-container .banner-main__mobile {
  display: block;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: top;
     object-position: top;
}
.container-content .body-main-content {
  flex-grow: 1;
}
.container-content .sidebar-mobile {
  display: flex;
}
.container-content .footer-main-content.bg-secondary {
  color: var(--neutral-200);
}
.container-content::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}
.container-content::-webkit-scrollbar-thumb {
  display: none;
}
.container-content::-webkit-scrollbar-track {
  display: none;
}

.footer-main-content {
  font-size: 11px;
  text-align: justify;
  padding: 10px;
  color: white;
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}
.footer-main-content .content ul {
  padding: 0;
  text-align: left;
}
.footer-main-content .content ul li {
  display: block;
  margin-bottom: 10px;
  font-size: 4.5px;
  overflow-wrap: break-word;
}
.footer-main-content.paragraph-2-col {
  -moz-column-count: 2;
       column-count: 2;
  -moz-column-gap: 10px;
       column-gap: 10px;
}

.sidebar {
  display: none;
  flex-direction: column;
  width: 100%;
  flex: 0;
}

@media (min-width: 600px) {
  .container-content .main-container .banner-main__desktop {
    display: block;
  }
  .container-content .main-container .banner-main__mobile {
    display: none;
  }
}
@media (max-width: 1099px) {
  body {
    max-height: 100svh;
    min-height: 100svh;
    overflow-y: hidden;
  }
  .root-container {
    max-height: 100svh;
    min-height: 100svh;
  }
}
@media (min-width: 1100px) {
  .root-container {
    gap: 15px;
  }
  .menu-header {
    margin-top: 2vw;
  }
  .container-content {
    flex-direction: row;
    gap: 20px;
  }
  .container-content .main-container {
    width: 0;
    max-height: 100%;
    overflow: hidden;
    border-radius: var(--radius-lg);
    position: static;
  }
  .container-content .main-container .banner-main__desktop {
    display: block;
  }
  .container-content .main-container .banner-main__mobile {
    display: none;
  }
  .container-content .main-container .footer-main-content {
    padding: 50px;
    gap: 40px;
    border-bottom-left-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }
  .container-content .main-container .footer-main-content.paragraph-2-col {
    -moz-column-count: 2;
         column-count: 2;
    -moz-column-gap: 40px;
         column-gap: 40px;
  }
  .container-content .main-container .footer-main-content .content ul li {
    font-size: 10px;
  }
  .container-content .container-sidebar-mobile {
    display: none;
  }
  .container-content .sidebar {
    display: flex;
    max-width: 480px;
    min-width: 480px;
    width: 480px;
  }
}
@media (min-width: 1500px) {
  .sidebar {
    display: flex;
    max-width: 550px;
    min-width: 550px;
    width: 550px;
  }
}/*# sourceMappingURL=main.css.map */

.py-15 {
  padding-top: 15px;
  padding-bottom: 15px;
}