/* Container */
@media (min-width: 320px) {

    .container,
    .container-sm {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
        max-width: 95% !important;
    }
}

@media (min-width: 576px) {

    .container,
    .container-sm {
        padding-left: 10px;
        padding-right: 10px;
        max-width: 98% !important;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 98% !important;
    }
}

@media (min-width: 992px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm {
        max-width: 98% !important;
    }
}

@media (min-width: 1199px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl {
        max-width: 100% !important;
    }
}

@media (min-width: 1400px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl {
        max-width: 100% !important;
    }
}

@media (min-width: 1600px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl {
        max-width: 100% !important;
    }
}

/* MENU - view width */
.main-menu {
    font-size: 0.7vw;
    padding: 0.5em 3em;
}

.nav-menu__item img {
    width: 1.2em;
}

.main-menu .logos img {
    height: 4em;
}

.main-menu .nav-menu__item>a {
    border-radius: 5em;
    font-size: 0.7vw;
}

.main-menu .nav-menu .search-control svg {
    font-size: 3em;
}

.main-menu {
    border-radius: 5em;
}

.search-content .results-search {
    max-height: calc(100svh - 33em);
    font-size: 0.652vw;
}

.search-content .search-control input {
    font-size: 1.8em;
    padding: 0.6em 0.9em;
    border-radius: var(--radius-sm);
    border: 1.5px solid var(--primary-500);
    flex-grow: 1;
}

.search-content .search-control {
    max-width: 60%;
}

.search-content {
    font-size: 0.652vw
}

@media (min-width: 1100px) {
    .root-container {
        gap: 0;
    }
}

@media (max-width: 575px) {
    .main-menu {
        font-size: 1rem;
    }

    .main-menu.container {
        max-width: 100% !important;
    }

    .search-content .results-search {
        max-height: calc(100svh - 33em);
        font-size: 0.8rem;
    }

    .search-content .search-control {
        max-width: 100%;
    }

    .search-content {
        font-size: 0.7rem
    }
}