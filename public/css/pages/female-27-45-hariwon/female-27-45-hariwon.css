.root-container {
    background: var(--neutral-900);
    background-size: cover;
}

.border {
    border: 1px solid black;
}

.menu-header {
    margin-top: 0;
}

img.lazyload {
    filter: blur(10px);
    transition: filter 0.3s;
}

img:not(.lazyload) {
    filter: blur(0);
}

/* Main banner */
.content-banner {
    width: fit-content;
    position: absolute;
    bottom: 20%;
    right: 10%;
    z-index: 1;
    aspect-ratio: 397 / 29;
    font-size: 0.625vw;
}

.female-banner-main {
    position: relative;
}

.img-banner {
    position: relative;
    display: flex;
    flex-direction: column;
}

.img-banner__thumbnail {
    box-sizing: border-box;
}

.img-banner:before {
    content: "";
    display: table;
    box-sizing: border-box;
    width: 0;
    padding-bottom: 56.25%
}

.img-banner-background {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    aspect-ratio: 16 / 9;
}

.img-banner-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 2rem;
    max-width: 100%;
}

.img-banner-background picture {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 2rem;
    max-width: 100%;
}

.btn-cta {
    background: rgba(0, 158, 164, 1);
    color: var(--neutral-900);
    border-radius: 5em;
    text-align: center;
    font-size: 2em;
    font-weight: 700;
    padding: 5px 0;
    box-shadow: 0 4px 4px rgba(14, 55, 54, 0.6);
    cursor: pointer;
    border: 1px solid #FFFFFF;
    display: block;
    box-sizing: border-box;
    width: fit-content;
    padding: 0.6em 3em;
    transition: 0.3s linear;
}

.btn-cta:hover {
    background: var(--secondary-700);
    color: var(--neutral-100);
    transform: translateY(-3px);
}

.banner-main__mobile {
    display: none;
}

@media (max-width: 575px) {
    .img-banner {
        position: relative;
        display: flex;
        flex-direction: column;
        padding-top: 31em;
    }

    .content-banner {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        bottom: 30%;
        z-index: 1;
        right: 0;
        left: 0;
        margin: auto;
        width: 100%;
    }

    .btn-cta {
        font-size: 1rem;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    section.female-banner-main {
        font-size: 0.65rem;
    }

    .btn-cta {
        font-size: 3em;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    section.female-banner-main {
        font-size: 0.8rem;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {}

/* Video Component */
.video {
    max-width: 95%;
    width: 100%;
    margin: 0 auto;
    font-size: 0.625vw;
}

.video__container {
    position: relative;
    width: 100%;
    aspect-ratio: 16 / 9;
    z-index: 1;
}

.video__thumb {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    border-radius: 2em;
    overflow: hidden;
    transition: opacity 0.5s ease;
    z-index: 2;
}

.video__thumb--fade-out {
    opacity: 0;
    pointer-events: none;
}

.video__img {
    width: 100%;
    height: auto;
    border-radius: 2em;
    display: block;
}

.video__play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6em;
    height: 6em;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.video__play:hover {
    background-color: rgba(0, 0, 0, 0.9);
    transform: translate(-50%, -50%) scale(1.1);
}

.video__play-icon {
    width: 0;
    height: 0;
    border-left: 2em solid white;
    border-top: 1em solid transparent;
    border-bottom: 1em solid transparent;
    margin-left: 0.3em;
}



.video__iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 2em;
    overflow: hidden;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.video__iframe--fade-in {
    opacity: 1;
}

/* Fallback for older browsers */
@supports not (aspect-ratio: 16 / 9) {
    .video__iframe {
        height: 0;
        padding-bottom: 56.25%;
    }
}

.video__iframe iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 2em;
    display: block !important;
}

/* Responsive */
@media (max-width: 768px) {
    .video {
        max-width: 100%;
        font-size: 1vw;
    }

    .video__play {
        width: 4em;
        height: 4em;
    }

    .video__play-icon {
        border-left: 1.5em solid white;
        border-top: 0.75em solid transparent;
        border-bottom: 0.75em solid transparent;
    }
}

@media (max-width: 575px) {
    .video {
        font-size: 1.5vw;
    }

    .video__img {
        border-radius: 1em;
    }

    .video__iframe {
        border-radius: 1em;
    }
}

/* Banner Container - Shared for all banners */
.container__banner {
    max-width: 120em;
    margin: 0 auto;
    position: relative;
}

/* Banner Components - Common Styles */
.banner1,
.banner2,
.banner3 {
    position: relative;
    overflow: hidden;
    font-size: 0.652vw;
    margin-top: 5em;
}

.banner1__content,
.banner2__content,
.banner3__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40em;
    border-radius: 6em;
}

.banner1__content {
    background: linear-gradient(to top, #D4EBC2 0%, transparent 85%);
}

.banner2__content {
    flex-direction: row-reverse;
    background: linear-gradient(to top, #D4EBC2 0%, transparent 85%);
    align-items: flex-end;
}

.banner3__content {
    flex-direction: row-reverse;
    align-items: flex-end;
    background: linear-gradient(to top, #D4EBC2 0%, transparent 85%);
}

.banner1__woman {
    position: relative;
    z-index: 2;
    flex: 3;
    padding-left: 3em;
}

.banner2__woman {
    position: relative;
    z-index: 2;
    flex: 4;
    padding-right: 1em;
}

.banner3__woman {
    position: relative;
    z-index: 2;
    flex: 3;
}

.banner1__woman-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 614 / 716;
}

.banner2__woman-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 1028 / 770;
}

.banner3__woman-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 614 / 716;
}

.banner1__info,
.banner2__info,
.banner3__info {
    position: relative;
    text-align: center;
    z-index: 3;
    flex: 7;
    display: flex;
    flex-direction: column;
}

.banner1__info {
    transform: translate(-10em, 3em);
}

.banner2__info {
    flex: 6;
    transform: none;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 2em;
    margin-bottom: 5em;
}

.banner3__info {
    flex: 6;
    transform: none;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 2em;
    margin-bottom: 5em;
}

.banner1__title {
    font-size: 1.5em;
    font-weight: 500;
    color: var(--neutral-100);
    margin-bottom: 0.5em;
}

.banner1__stats {
    display: flex;
    position: relative;
    justify-content: center;
    align-items: center;
    margin-bottom: 6em;
    flex-direction: column;
}

.line-1 {
    transform: translateX(-12.5em);
}

.line-2 {
    position: absolute;
    top: 4.5em;
    transform: translateX(7em);
}


.banner1__stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5em;
}

.banner1__number {
    font-size: 4em;
    font-weight: 800;
    background: linear-gradient(308.18deg, #FFDE00 -22.33%, #009885);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 0.9;
    display: inline;
}

.banner1__label {
    font-size: 2.4em;
    font-weight: 500;
    color: var(--primary-600);
    text-align: center;
    line-height: 1.2;
}

.banner1__desc {
    font-size: 1.5em;
    font-weight: 500;
    color: var(--neutral-100);
    margin-bottom: 1em;
    line-height: 1.35;
}

.banner1__desc strong {
    color: var(--primary-500);
    font-weight: 700;
    font-size: 1.2em;
}

.banner1__source {
    font-size: 0.7em;
    color: var(--neutral-400);
    font-weight: 400;
    line-height: 1.3;
    margin: 0 auto;
}

.banner1__virus {
    position: absolute;
    right: -2em;
    z-index: 1;
    bottom: 0;
    height: 100%;
    aspect-ratio: 506 / 696;
}

.banner2__virus {
    position: absolute;
    left: 0;
    z-index: 1;
    bottom: 0;
    height: 100%;
    aspect-ratio: 1026 / 740;
}

.banner3__virus {
    position: absolute;
    right: 0;
    z-index: 1;
    bottom: 0;
    height: 100%;
    aspect-ratio: 506 / 696;
}

.banner1__virus-img,
.banner2__virus-img,
.banner3__virus-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.9;
}

/* Banner 2 Specific Styles */
.banner2__stats {
    display: flex;
    justify-content: center;
    align-items: center;
}

.banner2__stat-circle {
    background: linear-gradient(210deg, #FFDE00 0%, #0DFBF0 100%);
    border-radius: 50%;
    width: 21em;
    height: 21em;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
}

/* result */

.footer-main-content-mobile {
    display: none;
}

.footer-main-content-male-page.mobile {
    background-color: #05A8AE !important;
    text-align: justify;
    padding: 10px;
    color: white;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.footer-main-content-male-page.mobile .content {
    margin: 2em auto 0 auto;
}

.footer-main-content-male-page.mobile .content b {
    font-size: 1.4em;
}

.footer-main-content-male-page.mobile .content ul {
    padding: 0;
    text-align: left;
    margin: auto;
}

.footer-main-content-male-page.mobile .content ul li {
    margin-bottom: 10px;
    font-size: 1.2em;
    overflow-wrap: break-word;
    display: list-item;
    list-style-type: none;
}

.footer-main-content-male-page.mobile .footer__content img {
    width: 7em;
    height: auto;
    overflow-x: hidden;
}

.footer-main-content-male-page.mobile .footer__content {
    display: none;
}

.footer-main-content-male-page.mobile .paragraph-2-col {
    -moz-column-count: 2;
    column-count: 2;
    -moz-column-gap: 3em;
    column-gap: 3em;
    padding: 0 2em;
}

.footer-main-content-mobile .footer__content {
    font-size: 0.8em;
    background: var(--secondary-500);
}

.footer-main-content-mobile .footer__content img {
    width: 7em;
    height: auto;
}

.footer-main-content-mobile .footer__content h3 {
    color: var(--primary-500);
}

.footer-main-content-mobile .footer__content .direction a {
    color: var(--primary-500);
}

.footer-main-content-mobile .container-footer {
    position: relative;
    max-width: 600px;
}

.footer-main-content-mobile .content-footer {
    max-height: 20px;
    /* Adjust the initial max-height as needed */
    overflow: hidden;
    transition: max-height 0.3s ease;
    /* Add smooth transition effect */
}

.footer-main-content-mobile .show-more {
    font-size: 1.5em;
    position: absolute;
    width: 100%;
    text-align: end;
    height: 20px;
    top: 0;
    right: 0;
    cursor: pointer;
    color: var(--natural-900);
    display: block;
    background-color: linear-gradient(to bottom, rgba(255, 255, 255, 0.4) 0%, rgb(255, 255, 255) 100%);
}

@media (max-width: 1099px) {
    .footer-main-content-male-page .footer__content {
        display: flex;
    }

    .footer-main-content .content ul li {
        margin-bottom: 10px;
        font-size: 1.2em;
    }

    .container-content .main-container {
        overflow-x: hidden;
    }
}

@media (max-width: 767px) {
    .footer-main-content-male-page {
        font-size: 0.7rem;
    }

    .footer-main-content-male-page .content {
        padding: 0;
    }

    .footer-main-content .content ul li {
        margin-bottom: 10px;
        font-size: 1.2em;
    }

    .footer__content .code-footer {
        display: contents;
        white-space: nowrap;
    }
}

@media ((max-width: 575px)) {
    .footer-main-content {
        display: none;
    }

    .footer-main-content-mobile {
        display: block;
    }

    .footer-main-content-male-page.mobile {
        display: block;
    }

    #footer-all {
        display: none;
    }
}



/* Section GOAL */
section.goal {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    font-size: 1rem;
    padding: 5em 1em;
    max-width: 110em;
}

section.goal .title-goal {
    text-align: center;
    font-size: 3em;
    font-weight: 700;
    color: var(--third-500);
    position: absolute;
    left: 2em;
    top: 3em;
}

section.goal .title-slide {
    font-size: 2.5em;
    font-weight: 600;
    text-align: center;
    margin: auto;
    margin-bottom: 2em;
    word-wrap: break-word;
}

section.goal .title-slide br {
    display: none;
}

section.goal .swiper-container {
    width: 100%;
    height: 100%;
}

section.goal .swiper-pagination-bullet {
    width: 1.2em;
    height: 1.2em;
    background: var(--neutral-900);
    border: 1px solid var(--neutral-900);
    opacity: 1;
}

section.goal .swiper-pagination-bullet-active {
    width: 4em;
    transition: width 0.5s;
    border-radius: 5px;
    background: var(--primary-700);
    border: 1px solid transparent;
}

section.goal .boder-bg-slide {
    display: flex;
    width: 95%;
    height: 33%;
    position: absolute;
    background: #ffffff54;
    color: var(--white);
    bottom: 6em;
    box-shadow: #0571C140 0px 5px 20px -6px inset, #0571C140 0px 4px 6px -3px inset;
    border-radius: 6em;
}

section.goal .boder-bg-slide .slide-to-show {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 2em;
    margin: auto;
    z-index: 10;
    font-size: 1.6em;
    font-weight: 600;
    padding: 1em 2em;
    background: linear-gradient(270deg, rgba(255, 255, 255, 0.74) 0%, rgba(164, 233, 255, 0.737) 40%);
    border-radius: 1em;
    display: flex;
    align-items: center;
    gap: 1em;
    height: 3.5em;
    width: fit-content;
}

.swiper-container-goal {
    max-width: 100%;
    position: relative;
    overflow-y: hidden;
    padding-bottom: 6em;
}

.swiper-container-goal .swiper-wrapper {
    align-items: flex-end;
}

.swiper-container-goal .swiper-slide {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    height: 100%;
    justify-content: flex-end;
}

.swiper-container-goal .swiper-slide .content-think-area {
    width: 100%;
    height: fit-content;
    position: absolute;
    display: flex;
    justify-content: center;
    z-index: 2;
    top: 30%;
}


.swiper-container-goal .swiper-slide .content-think-area .pop-up-think {
    width: fit-content;
    font-weight: 600;
    font-size: 2.4em;
    line-height: 1.4;
    height: fit-content;
    text-align: center;
    text-wrap: nowrap;
    border: 1px solid #ffffff;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.88) 0%, rgba(194, 248, 255, 0.27) 100%);
    border-radius: 1.2em;
    backdrop-filter: blur(8px);
    padding: 0.6em 1.5em;
    margin-left: 5%;
}

section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.left {
    margin-right: auto;
    margin-left: 2%;
    margin-top: 6%;
}

section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.right {
    margin-right: 0%;
    margin-left: auto;
}

section.goal .swiper-container-goal .swiper-slide.slide-2 .pop-up-think.right,
section.goal .swiper-container-goal .swiper-slide.slide-3 .pop-up-think.right,
section.goal .swiper-container-goal .swiper-slide.slide-4 .pop-up-think.right,
section.goal .swiper-container-goal .swiper-slide.slide-5 .pop-up-think.right {
    margin-right: 11%;
}

section.goal .swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right {
    margin-top: -9%;
}

.img-swip-area {
    position: relative;
    width: 60%;
    left: 0;
    transform: none;
    /* margin: auto; */
    max-width: 100%;
}

.slide-1 .img-swip-area {
    left: 2%;
}

.swiper-container-goal .swiper-slide.slide-1 .content-think-area,
.swiper-container-goal .swiper-slide.slide-6 .content-think-area {
    top: 22%;
}

.swiper-container-goal .swiper-slide.slide-2 .content-think-area,
.swiper-container-goal .swiper-slide.slide-3 .content-think-area,
.swiper-container-goal .swiper-slide.slide-4 .content-think-area {
    right: 8%;
}

.swiper-container-goal .swiper-slide.slide-5 .content-think-area {
    right: 12%;
}

.swiper-container-goal .swiper-slide.slide-6 .content-think-area {
    padding-top: 0em;
    top: 27%;
}

.swiper-container-goal .swiper-slide.slide-6 .pop-up-think.left {
    font-size: 1.8em;
    margin-top: 4%;
}

.swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right {
    font-size: 2em;
    display: flex;
    flex-direction: column;
    padding: 1em;
    margin-right: 2%;
}

.swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right .note {
    color: var(--third-500);
    font-size: 1.6em;
}

.swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right .now-btn {
    background-color: #05A8AE;
    color: white;
    border-radius: 50px;
    text-align: center;
    font-size: 0.8em;
    font-weight: 700;
    padding: 0.6em 1.5em;
    box-shadow: 0 4px 4px rgba(14, 55, 54, 0.6);
    cursor: pointer;
    border: 1px solid #FFFFFF;
    margin-top: 1em;
    text-transform: uppercase;
    transition: 0.3s linear;
    width: 90%;
    margin: 0 auto;
}

.swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right .now-btn:hover {
    background-color: var(--primary-900);
    color: white;
    transform: translateY(-3px);
}

.swiper-container-goal .swiper-slide.slide-6 .img-swip-area {
    width: 65%;
    left: 0;
    transform: none;
}

.swiper-container-goal:has(.slide-6) .slide-to-show {
    margin-bottom: 6em;
}

/* 
* <576:     xs
* 576-768:  sm
* 768-992:  md
* 992-1200: lg
* 1200 -1400: xl
* >= 1400:  xxl
*/
@media only screen and (max-width: 1099px) {
    section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.left {
        margin-left: 0;
    }

    section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.right {
        margin-right: 0;
    }

    .note br {
        display: block;
    }
}

@media only screen and (max-width: 768px) {
    section.goal .title-slide {
        margin-bottom: 1em;
    }

    section.goal {
        font-size: 0.6rem;
    }

    section.goal .title-goal {
        width: 100%;
        text-align: center;
        left: 0;
    }

    section.goal .swiper-container-goal .swiper-slide .content-think-area {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1em;
        padding-top: 0;
        margin-bottom: 2em;
        left: 0;
    }

    section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think {
        margin: auto !important;
    }

    section.goal .swiper-container-goal .swiper-slide.slide-6 .content-think-area .pop-up-think.left {
        display: none;
    }

    section.goal .swiper-container-goal .swiper-slide.slide-6 .content-think-area .pop-up-think.right .now-btn {
        margin-top: 0;
    }

    section.goal .swiper-container-goal:has(.slide-6) .slide-to-show {
        display: none;
    }

    section.goal .boder-bg-slide {
        width: 100%;
        height: 28%;
        border-radius: 4em;
        bottom: 11em;
    }

    .swiper-container-goal .swiper-slide.slide-6 .img-swip-area {
        width: 100%;
    }

    .img-swip-area {
        position: relative;
        width: 95%;
        left: 0 !important;
        margin: 0 auto;
    }

    .note br {
        display: block;
    }
}

@media only screen and (max-width: 575px) {
    section.goal {
        padding: 0;
    }

    section.goal .swiper-container-goal {
        font-size: 0.8rem;
    }

    section.goal .swiper-container-goal .swiper-slide .content-think-area {
        font-size: 1em;
    }

    section.goal .swiper-container-goal .swiper-slide {
        margin-top: 4%;
    }

    section.goal .swiper-container-goal .swiper-slide.slide-1 .content-think-area .pop-up-think.right {
        font-size: 3.2em;
    }

    section.goal .swiper-container-goal .swiper-slide.slide-6 .content-think-area .pop-up-think.right {
        white-space: normal;
        max-width: 80%;
        width: 100%;
        font-size: 1.8em;
        gap: 0.4em;
    }

    section.goal .swiper-container-goal .swiper-slide.slide-1 .content-think-area {
        font-size: 0.8em;
    }

    section.goal .title-slide {
        font-size: clamp(1rem, 1.7rem, 2.5rem);
        margin-bottom: 1em !important;
    }

    .swiper-container-goal .swiper-slide.slide-6 .img-swip-area {
        width: 80%;
    }

    .swiper-container-goal .swiper-slide.slide-1 .img-swip-area {
        width: 90%;
    }

    .swiper-container-goal .swiper-slide.slide-5 .img-swip-area,
    .swiper-container-goal .swiper-slide.slide-4 .img-swip-area,
    .swiper-container-goal .swiper-slide.slide-3 .img-swip-area,
    .swiper-container-goal .swiper-slide.slide-2 .img-swip-area {
        width: 90%;
    }

    .note br {
        display: block;
    }

    section.goal .title-slide br {
        display: block;
    }

    section.goal .swiper-container-goal .swiper-slide .content-think-area {
        position: relative;
        display: flex;
        align-items: center;
        flex-direction: row;
        gap: 1em;
        padding-top: 0;
        margin-bottom: 2em;
        left: 0;
    }
}

@media only screen and (max-width: 390px) {
    .swiper-container-goal .swiper-slide.slide-6 .pop-up-think.right .note {
        font-size: 1.4em;
    }

    section.goal .swiper-container-goal .swiper-slide.slide-6 .content-think-area .pop-up-think.right {
        white-space: normal;
        max-width: 80%;
        width: 100%;
        font-size: 1.6em;
        margin-top: 0.5em !important;
        gap: 0.6em;
    }

    section.goal .swiper-container-goal .swiper-slide.slide-6 .content-think-area .pop-up-think.right .now-btn {
        margin-top: 0.5em;
    }

    section.goal .swiper-container-goal .swiper-slide.slide-6 .content-think-area .pop-up-think.right .now-btn {
        width: 90%;
        margin: 0 auto;
    }

    section.goal .title-slide {
        font-size: clamp(1rem, 1.4rem, 2.5rem);
        margin-bottom: 1em !important;
    }

    section.goal .title-slide br {
        display: block;
    }

    section.goal .swiper-container-goal .swiper-slide.slide-1 .content-think-area .pop-up-think.right {
        font-size: 2.5em;
    }

    section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.right,
    section.goal .swiper-container-goal .swiper-slide .content-think-area .pop-up-think.left {
        font-size: 2em;
    }

}

/* custome pagination */
.control-navigate-pagination {
    display: flex;
    position: relative;
    justify-content: center;
    width: fit-content;
    margin: auto;
    gap: 2em;
    margin-top: 1.5em;
}

.control-navigate-pagination .swiper-pagination {
    bottom: -0.8%;
}

.control-navigate-pagination .swiper-pagination.bg-transparent {
    background-color: rgba(128, 128, 128, 0.3);
    border-radius: 2em;
    width: fit-content;
    padding: 1em;
    bottom: -4em !important;
}

.control-navigate-pagination .swiper-pagination-bullet {
    width: 1.5em;
    height: 1.5em;
    background-color: white;
    opacity: 1;
}

.control-navigate-pagination .swiper-pagination-bullet-active {
    width: 4em;
    transition: width .5s;
    border-radius: 2em;
    background: #05A8AE;
    border: 1px solid transparent;
}

.control-navigate-pagination .swiper-pagination,
.control-navigate-pagination .swiper-button-prev,
.control-navigate-pagination .swiper-button-next {
    position: static;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-navigate-pagination .swiper-button-prev,
.control-navigate-pagination .swiper-button-next {
    height: 4.5em;
    width: 4.5em;
    margin: 0;
    background-color: rgba(255, 255, 255, 0.32);
    padding: 1em !important;
    line-height: 2em;
    border-radius: 50%;
    color: #05A8AE;
    flex: none;
}

.control-navigate-pagination .swiper-button-prev::after,
.control-navigate-pagination .swiper-button-next::after {
    font-size: 1.5em !important;
    -webkit-text-stroke-width: .15em;
}

.control-navigate-pagination .swiper-button-prev:hover,
.control-navigate-pagination .swiper-button-next:hover {
    background-color: #05A8AE;
    color: white;
}

.control-navigate-pagination .btn-play,
.control-navigate-pagination .btn-pause {
    height: 2.5em;
    width: 2.5em;
    display: none;
    justify-content: center;
    background-color: rgba(128, 128, 128, 0.3);
    border-radius: 2em;
    align-items: center;
    font-size: 1.5em;
    color: #05A8AE;
}

.control-navigate-pagination .btn-play.active,
.control-navigate-pagination .btn-pause.active {
    display: flex;
}

.control-navigate-pagination .btn-play svg {
    margin-right: -0.2em;
}

.swiper-wrapper {
    height: 100%;
}

.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right {
    background-image: none;
}

/* Section Result */
section .header-section {
    display: flex;
    flex-direction: column;
    gap: 1em;
    margin-bottom: 2em;
}

section .subtitle-section {
    text-align: center;
    font-size: 2.5em;
    font-weight: 700;
    color: var(--third-500);
}

section .main-title-section {
    text-align: center;
    font-size: 4em;
    font-weight: 700;
    font-size: 4em;
    line-height: 1.2;
}

section.result .slide-result {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 4em;
    margin-top: 1em;
}

section.result .card {
    display: flex;
    height: 60%;
    max-width: 100%;
    border-radius: 3em;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    color: black;
    box-shadow: 0 0.7em 2em rgba(0, 0, 0, 0.2);
    transition: 0.3s ease-out;
    background-color: hsl(0, 0%, 100%);
    background: linear-gradient(203.19deg, #FFF7B9 0%, #8BE6BA 100%);
    background-size: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    border: 1px solid var(--neutral-900);
    padding-bottom: 2em;
    flex-direction: column;
    justify-content: space-between;
}

section.result .card .content-top {
    margin: 2em;
    transition: 0.3s;
    font-size: 1.2em;
    text-shadow: 0 2px 5px #ffffff;
    position: relative;
    z-index: 2;
}

section.result .card .content-top p {
    font-size: 1.6em;
    font-weight: 400;
    line-height: 1.2;
    width: 60%;
}

section.result .card .content-top p:first-child {
    font-size: 2em;
}

section.result .card .content-top h4 {
    width: 82%;
    line-height: 1.1;
    font-size: 3em;
    margin: 0.3em 0;
}

section.result .card img {
    position: absolute;
    -o-object-fit: contain;
    object-fit: contain;
    bottom: 0;
    right: 0;
    opacity: 0.9;
    transition: opacity 0.2s ease-out;
}

section.result .card .img-bg-card1,
section.result .card .img-bg-card1 {
    z-index: -1;
}

section.result .card .img-bg-card1 {
    width: auto;
    right: -0.5em;
    height: 75%;
    display: block;
}

section.result .card .img-bg-card2 {
    right: 0;
    height: 80%;
    display: none;
}

section.result .card .card-content {
    position: relative;
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.7607843137);
    width: -moz-fit-content;
    width: fit-content;
    max-width: 90%;
    padding: 1em 2em;
    border-radius: 2em;
    border: 1px solid white;
    margin: 0 2em;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

section.result .card .card-content h2 {
    margin: 0;
    line-height: 1;
    font-size: 2.4em;
    font-weight: 600;
    color: var(--third-500);
    text-transform: uppercase;
}

section.result .card .card-content h2 p {
    display: none;
}

section.result .card .card-content .card-text {
    opacity: 0;
    display: none;
    transition: opacity 0.3s ease-out;
    font-size: 2em;
    margin-top: 1rem;
    text-align: justify;
    line-height: 1.25;
}

section.result .card .card-content .card-text small {
    font-size: 0.7em;
    line-height: 1;
}

section.result .card.swiper-slide-active {
    width: 42em;
    max-height: 100%;
    height: calc(100% - 2em);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

section.result .card.swiper-slide-active .content-top {
    font-size: 1.35em;
}

section.result .card.swiper-slide-active h2 {
    width: 90%;
    border-end-end-radius: 0rem;
    border-bottom-left-radius: 0rem;
    text-transform: none;
}

section.result .card.swiper-slide-active .card-content {
    margin: 0 auto !important;
    padding: 2em;
    font-size: 0.8em;
}

section.result .card.swiper-slide-active .card-content h2 span {
    display: none;
}

section.result .card.swiper-slide-active .card-content h2 p {
    display: inline-block;
}

section.result .card.swiper-slide-active .img-bg-card2 {
    display: block;
}

section.result .card.swiper-slide-active .img-bg-card1 {
    display: none;
}

section.result .card.swiper-slide-active .card-text {
    opacity: 1;
    transition: opacity 0.5s 0.1s ease-in;
    display: block;
}

section.result .card.active img {
    transition: opacity 0.3s ease-in;
    opacity: 1;
}

section.result .card-2 {
    /* background: linear-gradient(192deg, rgba(255, 255, 255, 0.8), rgba(255,0,0,0) 70.71%),
    linear-gradient(70deg, rgba(255, 226, 2, 0.8), rgba(0,255,0,0) 70.71%),
    linear-gradient(336deg, rgba(44, 210, 233, 0.8), rgba(0,0,255,0) 70.71%); */
    background: linear-gradient(161.89deg, #F3FFC1 0.82%, #5FD675 247.31%);
}

section.result .card-3 {
    background: linear-gradient(45.1deg, #BCE86B 0%, #F6F8E5 71.9%);
}

section.result .card-4 {
    background: linear-gradient(225deg, rgba(255, 255, 255, 0.7) 0%, rgba(154, 242, 130, 0.7) 100%);
}

section.result .swiper-3d .swiper-wrapper {
    height: 65em;
    align-items: center;
}

section.result .swiper {
    padding: 0 3.5em;
}

@media (max-width: 1599px) {
    .slide-result {
        font-size: 0.9rem;
    }

    section.result .card .img-bg-card1 {
        height: 70%;
    }
}

@media (max-width: 1399px) {
    .slide-result {
        font-size: 0.8rem;
    }

    section.result .card .img-bg-card1 {
        height: 75%;
    }
}

@media (max-width: 1200px) {
    .slide-result {
        font-size: 0.75rem;
    }

    section.result .card .img-bg-card1 {
        height: 65%;
    }
}

@media (max-width: 991px) {
    .slide-result {
        font-size: 0.9rem;
    }

    section.result .card .img-bg-card1 {
        height: 65%;
    }
}

@media (max-width: 767px) {
    section.result {
        font-size: 0.6rem;
    }

    section.result .card .img-bg-card2 {
        right: -9%;
        top: 14em;
        max-height: 90%;
        height: auto;
    }

    section.result .card-4 .img-bg-card2 {
        right: -7%;
    }

    section.result .card .content-top h4 {
        font-size: 2.3em;
    }

    section.result .card .content-top p {
        width: 65%;
    }

    section.result .swiper-3d .swiper-wrapper {
        height: 80em;
    }

    section.result .card.swiper-slide-active {
        height: calc(100% - 2em);
    }

    section.result .card.swiper-slide-active .card-text {
        font-size: 2.2em;
    }
}

@media (max-width: 391px) {
    section.result {
        font-size: 0.5rem;
    }

    section.result .card .img-bg-card2 {
        right: -9%;
        top: 14em;
        max-height: 90%;
        height: auto;
    }

    section.result .card.swiper-slide-active .content-top {
        font-size: 1.2em;
    }

    section.result .card .content-top h4 {
        font-size: 2.3em;
    }

    section.result .card .content-top p {
        width: 65%;
    }

    section.result .swiper-3d .swiper-wrapper {
        height: 80em;
    }

    section.result .card.swiper-slide-active {
        height: calc(100% - 20em);
    }

    section.result .card.swiper-slide-active .card-text {
        font-size: 1.8em;
    }
}

/* END Section Result */

/* Section - Protection */
.container-protection {
    align-items: center;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding-top: 10rem;
}

.protection {
    width: 100%;
    height: max-content;
}

.protection-body-img {
    max-width: 90%;
    width: 94.5em;
    aspect-ratio: 1800 / 929;
}

.protection-box {
    top: -34em;
    width: 130em;
    margin-left: 1em;
    border-radius: 30px;
    transform: rotate(180deg);
    opacity: 1;
    /* background: linear-gradient(-180deg, #FFFEE4 2.52%, #B7F7D0 98.21%); */
    background: linear-gradient(-180deg, #F7FFE6 24.81%, rgba(209, 255, 218, 0) 100%);
    backdrop-filter: blur(50px);
    position: relative;
    padding: 6em 12em;
}

.protection-box-content {
    transform: rotate(180deg);
}

.protection-img {
    max-width: 120em;
}

.protection-text-box {
    text-align: center;
    display: flex;
    margin-left: auto;
    margin-right: auto;
    justify-content: center;
    gap: 40px;
}

.protection-text-box p {
    font-size: 3.5em;
    color: var(--third-900);
}

.protection-text-box h2 {
    font-size: 3.9em;
    color: var(--primary-500);
    font-weight: 600;
    line-height: 1.2;
    margin: 0.5em;
    position: relative;
    width: fit-content;
    margin: auto;
}

.protection-btn {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-between;
    margin-top: 3.4em;
    gap: 2em;
}

.protection-btn-left,
.protection-btn-right {
    flex: 1;
    box-sizing: border-box;
    background-color: var(--primary-500);
    color: white;
    border-radius: 4em;
    text-align: center;
    font-size: 1.4em;
    line-height: 1.1;
    font-weight: 700;
    padding: 0.8em 4em;
    box-shadow: 0 0.4em 0.4em rgb(14 55 54 / 15%);
    cursor: pointer;
    border: 1px solid #ffffff;
    transition: 0.3s linear;
}

.protection-btn-left:hover,
.protection-btn-right:hover {
    background-color: var(--primary-900);
    color: white;
    transform: translateY(-3px);
}

.quote-symbol-top {
    top: -0.2em;
    left: -0.6em;
}

.quote-symbol-bottom {
    bottom: -0.6em;
    right: -.55em;
}

.quote-symbol-top,
.quote-symbol-bottom {
    font-size: 3em;
    color: rgb(116, 213, 166);
    font-weight: 600;
    position: absolute;
    z-index: -1;
    line-height: 1;
    height: 1em;
}

@media only screen and (max-width: 992px) {
    .container-protection {
        font-size: 0.85rem;
    }

    .container-protection .protection-box {
        width: 90%;
        padding: 3em 6em;
    }
}

@media (max-width: 768px) {
    .container-protection {
        font-size: 0.75rem;
    }

    .container-protection .protection-box {
        width: 100%;
        padding: 3em 4em;
        background: linear-gradient(178deg, #ffffffab 0%, #ffffff00 70%);
        backdrop-filter: blur(20px);
        border: none;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        /* top: -10em; */
    }
}

@media (max-width: 576px) {
    .container-protection {
        font-size: 0.55rem;
    }

    .container-protection .protection-text-box h2 {
        font-size: 3.2em;
        margin: 0.5em 1.5em;
    }

    .container-protection .protection-btn {
        flex-direction: column;
    }

    .container-protection .protection-btn-left,
    .container-protection .protection-btn-right {
        font-size: 2.2em;
        width: fit-content;
        margin: auto;
        padding: 0.5em 4em;
    }

    .protection-text-box {
        display: flex;
        justify-content: center;
    }
}

/* END Section - Protection */

/* Section Article */
section.article {
    font-size: 0.9rem;
}

section.article .header-section {
    font-size: 3.5em;
    font-weight: 700;
    text-align: center;
    margin-bottom: 0;
    margin-top: 5svh;
}

.slide-article {
    padding: 5em 1em;
}

.slide-article .article-item {
    border-radius: 2em;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    text-align: center;
    font-weight: 400;
    position: relative;
    background: linear-gradient(150deg, rgba(252, 254, 244, 1) 0%, rgba(158, 237, 241, 1) 100%);
}

.slide-article .article-item__title {
    text-transform: uppercase;
    font-size: 1.8em;
    padding: 0.8em 1em;
    color: var(--primary-800);
    line-height: 1.3;
    height: 6em;
}

.slide-article .article-item__title span {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.5em;
}

.slide-article .article-item__avatar {
    aspect-ratio: 2.8 / 2;
    overflow: hidden;
}

.slide-article .article-item__avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide-article .article-item__des {
    display: none;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.5em;
}

.slide-article .article-item__action {
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    bottom: 1.5em;
}

.slide-article .article-item__action a {
    color: var(--primary-500);
    display: flex;
    flex-direction: row;
    width: fit-content;
    align-items: center;
    gap: 1em;
    padding: 0.7em 1.2em;
    margin: auto;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
    background-color: var(--primary-100);
    color: var(--primary-800);
    cursor: pointer;
    border-radius: 1em;
    font-size: 1.5em;
    transition: all 0.3s ease-in;
}

.slide-article .article-item__action a:hover {
    background-color: var(--primary-600);
    color: white;
}

@media (max-width: 767px) {
    section.article {
        font-size: 0.6rem;
    }

    .slide-article {
        padding: 2em 0;
    }

    .slide-article .control-navigate-pagination {
        justify-content: center;
        width: 100%;
    }

    .slide-article .control-navigate-pagination .swiper-pagination {
        display: none;
    }
}

/* SECTION - HPV-Diseases */
.common-hpv-diseases {
    position: relative;
    padding: 3rem 0;
    width: 100%;
    font-size: 1.03734439834rem;
    z-index: 0;
}

.common-hpv-diseases__wrapper {
    height: inherit;
    display: flex;
    position: relative;
    flex-direction: column;
    z-index: 0;
}

.common-hpv-diseases::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    mask-image: linear-gradient(180deg, rgba(217, 217, 217, 0) 80%, #737373 112.31%);
    mask-size: 100% 100%;
    mask-repeat: no-repeat;
    width: 100%;
    height: 100%;
    background: linear-gradient(291.33deg, #009585 -25.46%, #00CFA9 46.97%);
    z-index: -1;
}

.common-hpv-diseases__title {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.6em;
    position: relative;
}

.title-main,
.title-highlight {
    font-size: 2.5em;
    font-weight: 600;
}

.title-highlight sup {
    font-size: 0.6em;
}

.title-highlight {
    background: linear-gradient(90deg, #00D6E2 0%, #009585 100%);
    border-radius: 50px;
    padding: 0.2em 1.5em;
    color: white;
}

.title-classify {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 4em;
}

.common-hpv-diseases__contents {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: relative;
    /* overflow: hidden; */
    margin-top: 2vw;
    font-size: 1.3em;
}

.common-hpv-diseases__areas {
    width: 35%;
}

.common-hpv-diseases__areas.area-left,
.common-hpv-diseases__areas.area-right {
    display: flex;
    flex-direction: column;
    gap: 2.5em;
    justify-content: center;
}

.common-hpv-diseases__statistics {
    display: flex;
    flex-direction: column;
    gap: 5vw;
    margin-top: -50%;
}

.common-hpv-diseases__areas:not(.area-left, .area-right) {
    position: relative;
}

.common-hpv-diseases__image {
    filter: grayscale(1) blur(1px);
    /* transition: filter 0.3s ease-in-out, transform 0.2s ease-in-out; */
    transition: 0.15s linear;
    /* cursor: pointer; */
}

.common-hpv-diseases__image.male {
    position: relative;
    z-index: 1;
    aspect-ratio: 798 / 1639;
    width: 100%;
}

.common-hpv-diseases__image.male.active {
    filter: grayscale(0) blur(0);
    z-index: 3;
}

.common-hpv-diseases__image.female.active {
    filter: grayscale(0) blur(0);
    transform: scale(1.02);
    z-index: 4;
}

.common-hpv-diseases__image:not(.active):hover {
    transform: scale(1.02);
}

.common-hpv-diseases__statistic {
    position: relative;
    display: flex;
    flex-direction: column;
    text-align: center;
    padding: 2em;
    overflow: hidden;
    /* border-radius: 0 80px 80px 0; */
    color: #adadad;
    cursor: pointer;
    width: 50%;
    transition: color 0.3s ease;
}

.area-left .common-hpv-diseases__disease-percent {
    z-index: 1;
    position: relative;
}

.area-left .common-hpv-diseases__disease-name {
    text-align: right;
    margin-right: 3em;
    z-index: 1;
    position: relative;
}

.common-hpv-diseases__statistic.active {
    color: #0D867A;
}

.common-hpv-diseases__statistic.statistic-female.active {
    /* color: var(--secondary-500); */
    color: #ffdf1b;
}


.common-hpv-diseases__statistic.statistic-female.active .common-hpv-diseases__disease-name {
    color: #39514D;
}

.common-hpv-diseases__statistic.active .common-hpv-diseases__disease-name {
    color: #39514D;
}

.male-color .common-hpv-diseases__disease-percent {
    color: var(--third-500) !important;
}

/* .common-hpv-diseases__statistic::before {
    content: '';
    position: absolute;
    background: #DDFFA67A;
    transform: rotate(180deg);
    top: -10%;
    width: 100%;
    height: 120%;
    z-index: 0;
} */

.area-right .common-hpv-diseases__statistic {
    padding: 2em;
    /* border-radius: 80px 0 0 80px; */
}

.area-right .common-hpv-diseases__statistic::before {
    transform: rotate(0);
}

.area-right .common-hpv-diseases__disease-name {
    position: relative;
    z-index: 1;
    margin-left: 3em;
}

.area-right .common-hpv-diseases__disease-percent {
    position: relative;
    z-index: 1;
    margin-left: 1em;
}

.common-hpv-diseases__disease-percent {
    font-size: 4.5em;
    line-height: 1.2;
    font-weight: 800;
}

.common-hpv-diseases__disease-name {
    font-size: 1.5em;
    font-weight: 500;
}

.orbit-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 0;
    /* sau hình người */
}


@media (min-width: 319px) and (max-width: 376px) {
    .common-hpv-diseases {
        font-size: 0.45rem !important;
    }

    .common-hpv-diseases__areas.area-right,
    .common-hpv-diseases__areas.area-left {
        width: 35% !important;
    }
}

@media (max-width: 575px) {
    .common-hpv-diseases {
        font-size: 0.55rem;
    }

    .common-hpv-diseases__areas:not(.area-right, .area-left) {
        width: 32%;
        z-index: 1;
    }

    .common-hpv-diseases__areas.area-right,
    .common-hpv-diseases__areas.area-left {
        position: relative;
        justify-content: center;
        width: 40%;
    }

    .common-hpv-diseases__title {
        font-size: 1.2em;
    }

    .common-hpv-diseases__areas.area-right {
        right: 0;
    }

    .btn-cta {
        font-size: 1.4em;
        line-height: 1.4;
        padding: 1.2em 1em;
        min-width: 17em;
    }

    .common-hpv-diseases__contents {
        font-size: 1em;
        margin-top: 5vw;
    }

    .common-hpv-diseases__statistics {
        margin-top: 0;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .common-hpv-diseases {
        font-size: 0.75rem;
    }

    .common-hpv-diseases__areas:not(.area-right, .area-left) {
        width: 45%;
        z-index: 1;
    }

    .common-hpv-diseases__areas.area-right,
    .common-hpv-diseases__areas.area-left {
        position: relative;
        justify-content: center;
        width: 30%;
    }

    .common-hpv-diseases__areas.area-right {
        right: 0;
    }

    .common-hpv-diseases__contents {
        font-size: 1em;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .common-hpv-diseases {
        font-size: 0.88174273858rem;
    }

    .common-hpv-diseases__contents {
        font-size: 1.1em;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .common-hpv-diseases__areas:not(.area-left, .area-right) {
        width: 40%;
    }

    .common-hpv-diseases__contents {
        font-size: 1.1em;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .common-hpv-diseases {
        position: relative;
        padding: 2rem 0;
        width: 100%;
        font-size: 0.82rem;
        z-index: 0;
    }

    .common-hpv-diseases__areas:not(.area-right, .area-left) {
        width: 30%;
    }

    .common-hpv-diseases__areas.area-right,
    .common-hpv-diseases__areas.area-left {
        position: relative;
        justify-content: center;
        width: 30%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .common-hpv-diseases {
        position: relative;
        padding: 2rem 0;
        width: 100%;
        font-size: 0.9rem;
        z-index: 0;
    }
}

.banner2__stat-circle::before {
    content: '';
    position: absolute;
    inset: 0.3em;
    background: white;
    border-radius: 50%;
    z-index: 1;
}

.banner2__number {
    font-size: 6.4em;
    font-weight: 800;
    background: linear-gradient(308.18deg, #FFDE00 -22.33%, #009885);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 0.9;
    position: relative;
    z-index: 2;
}

.banner2__label1 {
    color: var(--neutral-100);
    z-index: 2;
    font-weight: 500;
    font-size: 1.6em;
}

.banner2__label {
    font-size: 1.6em;
    font-weight: 500;
    color: var(--neutral-100);
    position: relative;
    z-index: 2;
}

.banner2__text {
    text-align: left;
}

.banner2__desc {
    font-size: 1.5em;
    font-weight: 500;
    color: var(--neutral-100);
    line-height: 1.35;
}

.banner2__desc strong {
    color: var(--primary-500);
    font-weight: 700;
    font-size: 1.2em;
}

/* Banner 3 Specific Styles */
.banner3__stats {
    margin-bottom: 2em;
}

.banner3__stat-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2em;
}

.banner3__prefix {
    font-size: 1.8em;
    font-weight: 500;
    color: var(--primary-500);
}

.banner3__number {
    font-size: 6em;
    font-weight: 700;
    background: linear-gradient(135deg, #00A651 0%, #4CAF50 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 0.9;
    position: relative;
    z-index: 2;
}

.banner3__suffix {
    font-size: 1.8em;
    font-weight: 500;
    color: var(--primary-500);
}

.banner3__text {
    text-align: left;
}

.banner3__desc {
    font-size: 1.5em;
    font-weight: 500;
    color: var(--neutral-100);
    line-height: 1.35;
    margin-bottom: 1em;
}

.banner3__desc strong {
    color: var(--primary-500);
    font-weight: 700;
    font-size: 1.2em;
}

.banner3__note {
    font-size: 1em;
    color: var(--neutral-80);
    line-height: 1.4;
    font-style: italic;
}

/* Banner Responsive */
@media (max-width: 768px) {

    .banner1,
    .banner2,
    .banner3 {
        padding: 2em 0;
    }

    .container__banner {
        padding: 0 1em;
    }

    .banner1__content,
    .banner2__content,
    .banner3__content {
        grid-template-columns: 1fr;
        gap: 1.5em;
        text-align: center;
        min-height: auto;
    }

    .banner1__woman,
    .banner2__woman,
    .banner3__woman {
        order: 1;
    }

    .banner1__info,
    .banner2__info,
    .banner3__info {
        order: 2;
        transform: none;
    }

    .banner1__virus,
    .banner2__virus,
    .banner3__virus {
        position: static;
        right: auto;
        top: auto;
        transform: none;
        order: 3;
        text-align: center;
    }

    .banner1__title {
        font-size: 1em;
        margin-bottom: 1em;
    }

    .banner1__stats {
        gap: 1.5em;
        margin-bottom: 1em;
    }

    .banner1__number {
        font-size: 3em;
    }

    .banner1__label {
        font-size: 0.9em;
    }

    .banner1__desc {
        font-size: 0.9em;
    }

    .banner1__source {
        font-size: 0.7em;
    }

    .banner1__woman-img,
    .banner1__virus-img,
    .banner2__woman-img,
    .banner2__virus-img,
    .banner3__woman-img,
    .banner3__virus-img {
        max-width: 200px;
    }

    /* Banner 2 Mobile Specific */
    .banner2__stat-circle {
        width: 8em;
        height: 8em;
    }

    .banner2__number {
        font-size: 3em;
    }

    .banner2__label {
        font-size: 1.2em;
    }

    .banner2__desc {
        font-size: 0.9em;
    }

    /* Banner 3 Mobile Specific */
    .banner3__prefix,
    .banner3__suffix {
        font-size: 1.2em;
    }

    .banner3__number {
        font-size: 4em;
    }

    .banner3__desc {
        font-size: 0.9em;
    }

    .banner3__note {
        font-size: 0.8em;
    }
}

.way-protection-list {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 5px;
    margin-top: 4rem;
}

.way-protection-list__item {
    display: flex;
    align-items: center;
    gap: 2em;
    text-align: left;
}

.way-protection-list__item img {
    width: 8.5em;
    height: 8.5em;
}

.way-protection-list__item span {
    font-weight: 700;
    font-style: Bold;
    font-size: 20px;
    line-height: 130%;
    letter-spacing: 0px;

}

.protection-bg-img {
    max-width: 90%;
    width: 90%;
    height: auto;
    object-fit: cover;
    border-radius: 2rem;
}

/* .box-100-millions {
    width: 158px;
    height: 158px;
    opacity: 1;
    background: #FFFFFF66;
    border: 4px solid transparent;
    border-radius: 50%;
    padding: 15px;
    border-image-slice: 30;
    border-image-source: linear-gradient(223.3deg, #FFDE00 13.84%, #84F38D 50.05%, #0DFBF0 86.25%);
} */

.box-100-millions {
    width: 158px;
    height: 158px;
    background-color: white;
    position: relative;
    border-radius: 50%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;

    display: flex;
    flex-direction: column;
    gap: 0px;
}

.box-100-millions:before {
    position: absolute;
    content: "";
    height: 120%;
    width: 120%;
    background: linear-gradient(223.3deg, #FFDE00 13.84%, #84F38D 50.05%, #0DFBF0 86.25%);
    left: -10%;
    top: -10%;
    z-index: -1;
    border-radius: 50%;
}

.box-100-millions span {
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
}

.box-100-millions span:nth-of-type(1) {
    margin-top: 30px;
}

.box-100-millions span:nth-of-type(2) {
    margin-top: -10px;
}

.box-100-millions span:nth-of-type(3) {
    margin-top: -10px;
}


.box-100-millions .text-100-mil {
    font-size: 48px;
    font-weight: 800;
}

.text-100-mil.gradient-text {
    background: linear-gradient(313.73deg, #FFDE00 -21.76%, #009885 74.4%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    /* Hỗ trợ Firefox */
    background-clip: text;
    color: transparent;
}

.header-text-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px
}

.header-text-box-title {
    font-weight: 500;
    font-style: Medium;
    font-size: 15px;
    line-height: 135%;
    letter-spacing: 0px;
    text-transform: uppercase;
}

.action-text-box {
    background: #FEE003;
    opacity: 1;
    border-radius: 33px;
    padding-top: 6px;
    padding-right: 14px;
    padding-bottom: 6px;
    padding-left: 14px;

    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    line-height: 135%;
    letter-spacing: 0px;
    vertical-align: middle;
    text-transform: uppercase;
}

.content-text-box {
    opacity: 1;
    gap: 4px;
    border-radius: 33px;
    padding-top: 6px;
    padding-right: 14px;
    padding-bottom: 6px;
    padding-left: 14px;
    background: #018381;
    color: white;
    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    line-height: 135%;
    letter-spacing: 0px;
    vertical-align: middle;
    text-transform: uppercase;
}

.footer-text-box {
    font-weight: 300;
    font-style: Light;
    font-size: 7px;
    line-height: 135%;
    letter-spacing: 0px;
    text-align: center;
    text-transform: uppercase;
}

.protection-text-box-content {
    padding: 40px 0 10px 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}
