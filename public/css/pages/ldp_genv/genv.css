@media (min-width: 1100px) {
    html {
      font-size: 62.5%;
    }
}
@media (max-width: 1099px) {
    body {
        max-height: 100%;
        min-height: 100svh;
        overflow-y: auto;
    }
    .root-container {
        max-height: 100%;
        min-height: 100svh;
    }
    .fast-action-controls {
        display: none;
    }
}
.root-container {
    height: 100%;
    background: url('../../../images/ldp-genv/masthead-bg.webp') no-repeat top center scroll, #9f3d0e;
    background-size: contain;
}
.ldp-genv {
    height: 100%;
    /* background-color:#9f3d0e; */
}
.section-masthead {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    width: 100%;
    height: calc(100svh - 90px)
}
.section-masthead__left {
    aspect-ratio: 1280 / 1245;
    height: 86svh;
}
.section-masthead__right {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.section-masthead__left img{
    width: 100%;
    object-fit: cover;
}
.masthead__content {
    display: flex;
    flex-direction: column;
    gap: 3svh;
    position: relative;
    top: 45%;
    transform: translateY(-50%);
}
.masthead__content h1{
    font-weight: 800;
    font-size: 5.1em;
    line-height: 1.2;
    color:  #FFDD6F;
}
.masthead__content p{
    font-weight: 400;
    font-size: 1.5em;
    line-height: 1.6;
    color: var(--neutral-900);
}
.masthead__btn {
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    background-color: #052A25;
    color: var(--neutral-900);
    padding: 1.5em 5em;
    border-radius: 3.2em;
    text-align: center;
    cursor: pointer;
}
.masthead__btn:hover {
    transform: translateY(-3px);
    background-color: var(--neutral-900);
    color: #052A25;
}
.masthead__btn span{
    font-family: 'Invention', sans-serif;
    font-weight: bold;
    font-size: 2em;
    line-height: 1.2;
}
.masthead__noted {
    font-weight: 400;
    font-size: 1.2em;
    line-height: 1.5;
    color: var(--neutral-900);
}
.masthead__noted b {
    font-weight: 700;
}
.masthead__noted sup{
    font-weight: 400;
    font-size: 0.5em;
    line-height: 1.5;
    color: var(--neutral-900);
}
.scroll-btn {
    position: absolute;
    bottom: 5svh;
    display: flex;
    flex-direction: column;
    justify-content: center;  
    align-items: center;
    gap: 1em;
    cursor: pointer;
    animation: animation-arrow-y 1s linear 0s infinite alternate;
    -webkit-animation: animation-arrow-y 2s linear 0s infinite alternate;
}
.scroll-btn span {
    font-weight: 400;
    font-size: 2em;
    line-height: 1.3;
    color: var(--neutral-900);
}
.scroll-btn img {
    width: 1.2em;
    filter: brightness(0) saturate(100%) invert(95%) sepia(0%) saturate(7500%) hue-rotate(112deg) brightness(107%) contrast(107%);
}
@-webkit-keyframes animation-arrow-y {
    0% {
        -webkit-transform: translate(0px, 5px);
    }

    100% {
        -webkit-transform: translate(0px, -10px);
    }
}

@-moz-keyframes animation-arrow-y {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: 0 600%;
    }
}
.masthead__noted br:last-of-type{
    display: none;
}

@media (max-width: 575px) {
    .section-masthead {
        position: relative;
        height: 100%;
        font-size: 0.7rem;
        flex-direction: column-reverse;
        align-items: center;
        gap: 2svh;
        overflow: hidden;
    }
    .root-container {
        background: url('../../../images/ldp-genv/masthead-bg-mobile.webp') no-repeat top center scroll, #9f3d0e;
        background-size: contain;
    }
    .section-masthead__right {
        width: 100%;
    }
    .section-masthead__left {
        height: auto;
    }
    .section-masthead__left {
        width: 102%;
    }
    .masthead__content {
        display: flex;
        flex-direction: column;
        gap: 2svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
    }
    .masthead__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .masthead__noted {
        position: absolute;
        bottom: 1svh;
        font-size: 1.3em;
        margin: 0 auto;
        width: 90%;
        left: 50%;
        transform: translateX(-50%);
    }
    .masthead__content h1 {
        line-height: 1.3;
        font-size: clamp(4em, 5vw, 5.1em);
    }
    .masthead__content p {
        font-size: clamp(1.2em, 2.5vw, 1.8em);
    }
    .scroll-btn {
        display: none;
    }
    .hidden-mobile {
        display: none;
    }
    .masthead__noted br:last-of-type{
        display: block;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .section-masthead {
        position: relative;
        height: 100%;
        font-size: 0.8rem;
        flex-direction: column-reverse;
        align-items: center;
        gap: 2svh;
    }
    .root-container {
        background: url('../../../images/ldp-genv/masthead-bg-mobile.webp') no-repeat top center scroll, #9f3d0e;
        background-size: contain;
    }
    .section-masthead__left {
        height: auto;
    }
    .section-masthead__left,
    .section-masthead__right {
        width: 100%;
    }
    .masthead__content {
        display: flex;
        flex-direction: column;
        gap: 2svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 5svh;
    }
    .masthead__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .masthead__noted {
        position: absolute;
        bottom: 5svh;
        left: 50%;
        transform: translateX(-50%);
    }
    .masthead__content h1 {
        line-height: 1.4;
    }
    .scroll-btn {
        display: none;
    }
    .hidden-mobile {
        display: none;
    }
    .masthead__noted br:last-of-type{
        display: block;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .section-masthead {
        position: relative;
        height: 100%;
        font-size: 1rem;
        flex-direction: column-reverse;
        align-items: center;
        gap: 5svh;
    }
    .root-container {
        background: url('../../../images/ldp-genv/masthead-bg-mobile.webp') no-repeat top center scroll, #9f3d0e;
        background-size: cover;
    }
    .section-masthead__left {
        height: auto;
    }
    .section-masthead__left,
    .section-masthead__right {
        width: 90%;
    }
    .masthead__content {
        display: flex;
        flex-direction: column;
        gap: 3svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 10svh;
    }
    .masthead__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .masthead__noted {
        position: absolute;
        bottom: 5svh;
        left: 50%;
        transform: translateX(-50%);
    }
    .scroll-btn {
        display: none;
    }
    .hidden-mobile {
        display: none;
    }
    .masthead__noted br:last-of-type{
        display: block;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .section-masthead {
        position: relative;
        height: 100%;
        font-size: 1rem;
        flex-direction: column-reverse;
        align-items: center;
        gap: 5svh;
    }
    .root-container {
        background-size: cover;
    }
    .section-masthead__left {
        height: auto;
    }
    .section-masthead__left,
    .section-masthead__right {
        width: 90%;
    }
    .masthead__content {
        display: flex;
        flex-direction: column;
        gap: 3svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 10svh;
    }
    .masthead__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .masthead__noted {
        position: absolute;
        bottom: 5svh;
        left: 50%;
        transform: translateX(-50%);
    }
    .scroll-btn {
        display: none;
    }
    .masthead__noted br:last-of-type{
        display: block;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .section-masthead {
        font-size: 0.7rem;
    }
    .section-masthead__left {
        height: 83svh;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .section-masthead {
        font-size: 0.9rem;
    }
    .section-masthead__left {
        height: 86svh;
    }
}

/* SECTION 2 - Reason */
.section-reason {
    position: relative;
    font-size: 1rem;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    width: 100%;
    height: 100svh;
    background: #FDF5E0;
}
.group-reason {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
}
.bg-reason {
    position: absolute;
    width: 65vw;
    height: 100%;
    top: 0;
    right: 0;
    z-index: 0;
}
.bg-reason img{
    width: 100%;
    height: inherit;
    object-fit: cover;
    object-position: top left;
}
.section-reason__right {
    width: auto;
    aspect-ratio: 1420 / 2012;
    height: 110svh;
    z-index: 3;
}
.section-reason__left {
    width: 40vw;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.section-reason__right img{
    width: 100%;
    object-fit: cover;
}
.reason__content {
    display: flex;
    flex-direction: column;
    gap: 3svh;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}
.reason__content h2{
    font-weight: 900;
    font-size: 4.3em;
    line-height: 1.1;
    color:  #835C92;
}
.reason__content p{
    font-weight: 400;
    font-size: 1.6em;
    line-height: 1.6;
    color: var(--neutral-100);
}
.nowrap {
    white-space: nowrap;
}
.reason__btn {
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    color: #052A25;
    border: 2px solid #052A25;
    padding: 1.5em 5em;
    border-radius: 3.2em;
    text-align: center;
    cursor: pointer;
}
.reason__btn:hover {
    transform: translateY(-3px);
    background-color: #052A25;
    color: #FDF5E0;
}
.reason__btn span{
    font-weight: bold;
    font-size: 1.8em;
    line-height: 1.2;
    font-weight: 700;
}
.reason__noted {
    font-weight: 400;
    font-size: 1.2em;
    line-height: 1.5;
    color: var(--neutral-100);
}
.reason__noted b {
    font-weight: 700;
}
.reason__noted sup{
    font-weight: 400;
    font-size: 0.5em;
    line-height: 1.5;
    color: var(--neutral-100);
}
.reason__noted br:last-of-type{
    display: none;
}

@media (max-width: 575px) {
    .section-reason {
        position: relative;
        height: 100%;
        font-size: 0.7rem;
    }
    .bg-reason {
        position: absolute;
        width: 200vw;
        height: 100%;
        top: 14%;
        left: auto;
        right: 0;
        z-index: 0;
    }
    .section-reason__left,
    .section-reason__right {
        width: 90%;
    }
    .section-reason__right {
        height: auto;
    }
    .group-reason {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2svh;
    }
    .reason__content {
        display: flex;
        flex-direction: column;
        gap: 2svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 5svh;
    }
        .reason__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .reason__content p {
        font-size: 1.8em;
    }
    .reason__noted br:last-of-type{
        display: block;
    }
    .reason__noted {
        font-size: 1.3em;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .section-reason {
        position: relative;
        height: 100%;
        font-size: 0.8rem;
    }
    .section-reason__left,
    .section-reason__right {
        width: 90%;
    }
    .section-reason__right {
        height: auto;
    }
    .group-reason {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 5svh;
    }
    .reason__content {
        display: flex;
        flex-direction: column;
        gap: 3svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 5svh;
    }
    .reason__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .reason__noted br:last-of-type{
        display: block;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .section-reason {
        position: relative;
        height: 100%;
        font-size: 1rem;
    }
    .root-container {
        background-size: cover;
    }
    .section-reason__left,
    .section-reason__right {
        width: 80%;
    }
    .section-reason__right {
        height: auto;
    }
    .group-reason {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 5svh;
    }
    .reason__content {
        display: flex;
        flex-direction: column;
        gap: 3svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 10svh;
    }
    .reason__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .reason__noted br:last-of-type{
        display: block;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .section-reason {
        position: relative;
        height: 100%;
        font-size: 1rem;
    }
    .root-container {
        background-size: cover;
    }
    .section-reason__left,
    .section-reason__right {
        width: 70%;
    }
    .section-reason__right {
        height: auto;
    }
    .group-reason {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 5svh;
    }
    .reason__content {
        display: flex;
        flex-direction: column;
        gap: 3svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 10svh;
    }
    .reason__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .reason__noted br:last-of-type{
        display: block;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .section-reason {
        font-size: 0.8rem;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .section-reason {
        font-size: 0.9rem;
    }
    .section-reason__left {
        width: 43vw;
    }
}

/* Section 3 - Consultation */
.section-consultation {
    position: relative;
    font-size: 1rem;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    width: 100%;
    height: 100svh;
    background: #9E77A5;
}
.group-consultation {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5vw;
}

.section-consultation__left {
    width: 50%;
    aspect-ratio: 662 / 508;
    height: auto;
    z-index: 3;
}
.section-consultation__right {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.section-consultation__left img{
    width: 100%;
    object-fit: cover;
}
.consultation__content {
    display: flex;
    flex-direction: column;
    gap: 3svh;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}
.consultation__content h2{
    font-weight: 900;
    font-size: 4.3em;
    line-height: 1.1;
    color:  #FFDD6F;
}

.consultation__content ul {
    color: var(--neutral-900);
    padding-left: 2em;
}

.consultation__content li {
    font-weight: 400;
    font-size: 1.7em;
    line-height: 1.7;
}
.consultation__btn {
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    color: var(--neutral-900);
    border: 2px solid var(--neutral-900);
    padding: 1.5em 5em;
    border-radius: 3.2em;
    text-align: center;
    cursor: pointer;
}
.consultation__btn:hover {
    transform: translateY(-3px);
    background: var(--neutral-900);
    color: #9E77A5;
}
.consultation__btn span{
    font-weight: bold;
    font-size: 1.8em;
    line-height: 1.2;
    font-weight: 700;
}
@media (max-width: 575px) {
    .section-consultation {
        position: relative;
        height: 100%;
        font-size: 0.7rem;
    }
    .group-consultation {
        display: flex;
        flex-direction: column;
        gap: 5svh;
        padding: 5svh 0;
    }
    .section-consultation__left,
    .section-consultation__right {
        width: 90%;
    }
    .consultation__content {
        display: flex;
        flex-direction: column;
        gap: 3svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 0;
    }
    .consultation__content ul {
        list-style-position: inside;
        padding: 0;
        font-size: 1.2em;
    }
    .consultation__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .consultation__noted {
        position: absolute;
        bottom: 5svh;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .section-consultation {
        position: relative;
        height: 100%;
        font-size: 0.8rem;
    }
    .group-consultation {
        display: flex;
        flex-direction: column;
        gap: 5svh;
        padding: 5svh 0;
    }
    .section-consultation__left,
    .section-consultation__right {
        width: 90%;
    }
    .consultation__content {
        display: flex;
        flex-direction: column;
        gap: 3svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 0;
    }
    .consultation__content ul {
        list-style-position: inside;
    }
    .consultation__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .consultation__noted {
        position: absolute;
        bottom: 5svh;
    }
}

@media only screen and (min-width: 769px) and (max-width: 992px) {
    .section-consultation {
        position: relative;
        height: 100%;
        font-size: 0.8rem;
    }
    .group-consultation {
        display: flex;
        flex-direction: column;
        gap: 5svh;
        padding: 5svh 0;
    }
    .section-consultation__left,
    .section-consultation__right {
        width: 90%;
    }
    .consultation__content {
        display: flex;
        flex-direction: column;
        gap: 3svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 0;
    }
    .consultation__content ul {
        list-style-position: inside;
    }
    .consultation__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .consultation__noted {
        position: absolute;
        bottom: 5svh;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .section-consultation {
        position: relative;
        height: 100%;
        font-size: 1rem;
    }
    .group-consultation {
        display: flex;
        flex-direction: column;
        gap: 5svh;
        padding: 10svh 0;
    }
    .section-consultation__left,
    .section-consultation__right {
        width: 90%;
    }
    .consultation__content {
        display: flex;
        flex-direction: column;
        gap: 3svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 0;
    }
    .consultation__content ul {
        list-style-position: inside;
    }
    .consultation__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .consultation__noted {
        position: absolute;
        bottom: 5svh;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .section-consultation {
        font-size: 0.8rem;
    }
    .group-consultation {
        width: 90%;
        gap: 2vw;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .section-consultation {
        font-size: 0.9rem;
    }
    .group-consultation {
        width: 90%;
        gap: 2vw;
    }
}

/* Section 4 - Locations */
.section-locations {
    position: relative;
    z-index: 1;
    transition: all 200ms ease-in-out;
    display: block;
}

.section-locations .locations {
    visibility: visible;
    transform: scale(1);
    opacity: 1;
    transform-origin: top bottom;
    transition: all 200ms ease-in-out;
    background-color: white;
    position: absolute;
    overflow: hidden;
    gap: 1em;
    flex-direction: column;
    display: flex;
    position: relative;
    z-index: 1;
    width: 100%;
    height: 100%;
    font-size: 1rem;
    padding: 5em 0;
    box-shadow: 1em 0 4em rgba(51, 48, 0, 0.1882352941);
    background: var(--primary-500);
}

.section-locations .locations .number-code {
    text-align: center;
    width: 100%;
    position: relative;
    bottom: 1.2em;
    left: 0;
    right: 0;
    color: var(--neutral-900);
    font-size: 1.2em;
    margin-top: 2svh;
}

.section-locations .header-title {
    position: relative;
    width: fit-content;
    color: white;
    padding: 4em 3em;
    margin: 0 auto;
    background-image: none;
    padding-bottom: 7.5em;
    -webkit-clip-path:none;
    clip-path: none;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}
.section-locations .header-title .title {
    width: auto;
    aspect-ratio: unset;
    text-align: center;
}
.section-locations .header-title .title__main {
    font-size: 4.8em;
    font-weight: bold;
    text-shadow: 0.1em 0.1em 0.2em var(--primary-800);
}

.section-locations .header-title .title__sub {
    font-size: 3.6em;
    font-weight: 600;
    text-shadow: 0.1em 0.1em 0.2em var(--primary-800);
}

.section-locations .header-title .key-visual {
    position: absolute;
    top: -12%;
    right: 6%;
    width: 90%;
    z-index: -1;
    transform: rotate(4deg);
}

.section-locations .header-title .btn-close-modal {
    position: absolute;
    top: 0.5em;
    right: 0.5em;
    width: 1.5em;
    height: 1.5em;
    border-radius: 50%;
    background-color: transparent;
    text-align: center;
    line-height: 1.3em;
    font-weight: 400;
    cursor: pointer;
    font-size: 2.2em;
    color: var(--primary-500);
    transition: all 250ms ease-in-out;
    border: 2px solid;
}

.section-locations .header-title .btn-close-modal:hover {
    color: white;
    border-color: var(--primary-800);
    background-color: var(--primary-800);
}

.section-locations .location-list {
    display: flex;
    gap: 2em;
    padding: 2.5em;
    width: 90%;
    left: 50%;
    transform: translateX(-50%);
    position: relative;
    justify-content: center;
    align-items: center;
    margin-top: -4%;
}

.section-locations .location-item {
    cursor: pointer;
    transition: all 200ms ease-in-out;
    background-color: white;
    padding: 1.5em 1em;
    transform: scale(1);
    border-radius: var(--radius-sm);
    text-align: center;
    border: 1px solid transparent;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
    aspect-ratio: 240 / 140;
    width: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.section-locations .location-item img {
    width: auto;
    height: auto;
    margin: unset;
}
.section-locations .location-item .location-genv-img {
    width: 70%;
    height: auto;
    margin: auto;
}

.section-locations .location-item:hover {
    box-shadow: 2px 1px 6px rgba(51, 48, 0, 0.1254901961);
    transform: scale(1.05);
    border-color: var(--primary-200);
}

.section-locations .location-item:hover img {
    filter: grayscale(0);
}

/* Define the animation */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 1;
    }

    to {
        transform: translateX(0);
        opacity: 0;
    }
}
@media (max-width: 575px) {
    .section-locations .locations{
        font-size: 0.55rem;
    }
    .section-locations .location-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 3em;
        padding: 2.5em;
        padding-bottom: 4.5em;
        width: 100%;
    }
    .section-locations .location-item {
        width: 100%;
    }
    .section-locations .locations .number-code {
        font-size: 2.2em;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .section-locations .locations{
        font-size: 0.75rem;
    }
    .section-locations .location-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 5em;
        padding: 2.5em;
        padding-bottom: 4.5em;
        width: 75%;
    }
    .section-locations .location-item {
        width: 100%;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .section-locations .locations{
        font-size: 0.65rem;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .section-locations .locations{
        font-size: 0.7rem;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .section-locations .locations{
        font-size: 0.75rem;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .section-locations .locations{
        font-size: 0.8rem;
    }
}

/* Section 5 - Policy */
.policy-main-content-genv-page.mobile {
    background-color: #00535E;
    text-align: justify;
    padding: 15svh  10px;
    color: #FDF5E0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.policy-main-content-genv-page.mobile .content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.policy-main-content-genv-page.mobile .content b {
    font-size: 1.4em;
}

.policy-main-content-genv-page.mobile .content ul {
    padding: 0;
    text-align: left;
    margin: auto;
}

.policy-main-content-genv-page.mobile .content ul li {
    margin-bottom: 10px;
    font-size: 1.6em;
    overflow-wrap: break-word;
    display: list-item;
    list-style-type: decimal;
}
.policy-main-content-genv-page.mobile .content ul li a {
    text-decoration: underline;
}

.policy-main-content-genv-page.mobile .policy__content img {
    width: 7em;
    height: auto;
}

.policy-main-content-genv-page.mobile .policy__content {
    display: none;
}

.policy-main-content-genv-page.mobile .paragraph-2-col {
    /* -moz-column-count: 2;
    column-count: 2;
    -moz-column-gap: 3em;
    column-gap: 3em; */
    background: #FDF5E0;
    color: var(--neutral-100);
    padding: 5em;
}

.policy-main-content .policy__content {
    font-size: 0.8em;
    background: var(--secondary-500);
}

.policy-main-content .policy__content img {
    width: 7em;
    height: auto;
}

.policy-main-content .policy__content h3 {
    color: var(--primary-500);
}

.policy-main-content .policy__content .direction a {
    color: var(--primary-500);
}

.policy-main-content .container-policy {
    position: relative;
    max-width: 70%;
    border-bottom: 1px solid;
}
.noted_policy {
    width: 70%;
    font-weight: 400;
    font-size: 1.6em;
    line-height: 1.5;
    margin-bottom: 5svh;
}
.noted_policy span{
    width: 56%;
    position: relative;
    display: inline-block;
}

.policy-main-content .content-policy {
    max-height: 30px;
    /* Adjust the initial max-height as needed */
    overflow: hidden;
    transition: max-height 0.3s ease;
    /* Add smooth transition effect */
}

.policy-main-content .show-more {
    font-size: 1.5em;
    position: absolute;
    width: 100%;
    text-align: end;
    height: 30px;
    top: 5px;
    right: 0;
    cursor: pointer;
    color: var(--natural-900);
    display: block;
    background-color: linear-gradient(to bottom, rgba(255, 255, 255, 0.4) 0%, rgb(255, 255, 255) 100%);
}

@media (max-width: 575px) {
    .policy-main-content{
        font-size: 0.65rem;
    }
    .policy-main-content-genv-page.mobile {
        padding: 7svh 10px;
    }
    .noted_policy {
        width: 85%;
        margin-bottom: 3svh;
        text-align: left;
    }
    .noted_policy span{
        width: 100%;
    }
    .policy-main-content .container-policy {
        max-width: 85%;
    }
    .policy-main-content .content-policy {
        max-height: 20px;
    }
    .policy-main-content .show-more {
        height: 20px;
        top: 5px;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .policy-main-content{
        font-size: 0.65rem;
    }
    .policy-main-content-genv-page.mobile {
        padding: 7svh 10px;
    }
    .noted_policy {
        width: 70%;
        margin-bottom: 3svh;
        text-align: left;
    }
    .noted_policy span{
        width: 100%;
    }
    .policy-main-content .content-policy {
        max-height: 20px;
    }
    .policy-main-content .show-more {
        height: 20px;
        top: 5px;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .policy-main-content{
        font-size: 0.7rem;
    }
    .policy-main-content-genv-page.mobile {
        padding: 5svh 10px;
    }
    .noted_policy {
        width: 70%;
        margin-bottom: 3svh;
        text-align: left;
    }
    .noted_policy span{
        width: 100%;
    }
    .policy-main-content .content-policy {
        max-height: 20px;
    }
    .policy-main-content .show-more {
        height: 20px;
        top: 5px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .policy-main-content{
        font-size: 0.7rem;
    }
    .policy-main-content .content-policy {
        max-height: 20px;
    }
    .policy-main-content .show-more {
        height: 20px;
        top: 5px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .policy-main-content{
        font-size: 0.75rem;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .policy-main-content{
        font-size: 0.8rem;
    }
}
/* Section 6 - Footer GenV */
.footer-section {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    background: #052A25;
    padding: 10svh 0;
    justify-content: center;
    align-items: center;
}
.footer-section .container{
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.list-footer {
    display: flex;
    justify-content: flex-start;
    flex-wrap: nowrap;
    gap: 8vw;
    list-style: none;
    padding: 0;
}
.list-footer li {
    font-weight: 700;
    font-size: 1.3em;
    line-height: 1.2;
    text-decoration: underline;
    text-decoration-style: solid;
    cursor: pointer;
}
.list-footer.list-1 {
    color: #C28B00;
}
.list-footer.list-2 {
    color: #FDF5E0;
}
.footer-content__top ,
.footer-content__bottom {
    position: relative;
    display: flex;
    flex-direction: row;
    width: 90%;
}
.footer-content__bottom,
.footer-section .container {
    gap: 6svh;
}
.footer-content__top {
    gap: 8vw;
}

.acc-logo {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
}
.list-logo {
    display: flex;
    gap: 2em;
    align-items: center;
}
.icon-fb {
    width: 1.4em;
    aspect-ratio: 28 / 51;
}
.icon-ytb {
    width: 2.3em;
    aspect-ratio: 47 / 33;
}
.icon-insta {
    width: 2.1em;
    aspect-ratio: 42 / 42;
}
.footer-content__bottom span {
    font-weight: 400;
    font-size: 1.3em;
    line-height: 1.3;
    color: #FDF5E0;
}
.logo-msd{
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    width: 14.3em;
    aspect-ratio: 298 / 124;
    filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(1%) hue-rotate(207deg) brightness(103%) contrast(101%);
}
.footer-content__bottom span br{
    display: none;
}
@media (max-width: 575px) {
    .footer-section {
        width: 100%;
        font-size: 0.75rem;
        padding-bottom: 100px;
        padding-top: 5svh;
    }
    .list-footer {
        justify-content: flex-start;
        gap: 2svh;
        flex-direction: column;
    }
    .footer-content__top,
    .footer-content__bottom {
        gap: 2svh;
    }
    .footer-content__bottom span {
        width: 75%;
    }
    .footer-content__bottom span br{
        display: block;
    }
    .footer-content__top, .footer-content__bottom {
        flex-direction: column;
    }
    .acc-logo {
        position: absolute;
        top: 0;
        right: 0;
        transform: none;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .footer-section {
        width: 100%;
        font-size: 0.85rem;
        padding-bottom: 100px;
    }
    .list-footer {
        justify-content: flex-start;
        gap: 3svh;
        flex-direction: column;
    }
    .footer-content__top,
    .footer-content__bottom {
        gap: 3svh;
    }
    .logo-msd {
        top: 0;
        transform: none;
        right: 0;
    }
    .footer-content__bottom span {
        width: 80%;
    }
    .footer-content__bottom span br{
        display: block;
    }
    .footer-content__top, .footer-content__bottom {
        flex-direction: column;
    }
    .acc-logo {
        position: absolute;
        top: 0;
        right: 0;
        transform: none;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .footer-section {
        width: 100%;
        font-size: 0.85rem;
        padding-bottom: 100px;
    }
    .list-footer {
        justify-content: flex-start;
        gap: 3svh;
        flex-direction: column;
    }
    .footer-content__top,
    .footer-content__bottom {
        gap: 3svh;
    }
    .logo-msd {
        top: 0;
        transform: none;
        right: 0;
    }
    .footer-content__top, .footer-content__bottom {
        flex-direction: column;
    }
    .acc-logo {
        position: absolute;
        top: 0;
        right: 0;
        transform: none;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .footer-section {
        width: 100%;
        font-size: 0.8rem;
        padding-bottom: 100px;
    }
    .footer-content__top,
    .list-footer {
        gap: 4vw;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .footer-section {
        width: 100%;
        font-size: 0.85rem;
    }
    .footer-content__top,
    .list-footer {
        gap: 6vw;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .footer-section {
        width: 100%;
        font-size: 0.9rem;
    }
    .footer-content__top,
    .list-footer {
        gap: 6vw;
    }
}