.root-container {
    height: 100%;
    background: #9f3d0e;
}
.video-gif-genv {
  position: absolute;
  width: 100%;
  height: 100svh; /* hỗ trợ iOS tốt hơn */
  overflow: hidden;
}

.video-gif-genv video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}
.section-masthead-launching {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    width: 100%;
    height: calc(100svh - 90px);
    gap: 5vw;
}
.section-masthead-launching__left {
    aspect-ratio: none;
    width: 50%;
    height: 100%;
}
.stamp-genv {
    width: 19.5em;
    height: 19.5em;
    position: relative;
    left: -4%;
    top: 10%;
}
.stamp-genv img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.section-masthead-launching__right {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.section-masthead-launching__left img{
    width: 100%;
    object-fit: cover;
}
.masthead-launching__content {
    display: flex;
    flex-direction: column;
    gap: 3svh;
    position: relative;
    top: 45%;
    transform: translateY(-50%);
}
.masthead-launching__content h1{
    font-weight: 800;
    font-size: 2.28em;
    line-height: 1.2;
    color:  #FFDD6F;
}
.masthead-launching__content h1 span{
    font-size: 3.85em;
}
.masthead-launching__content p{
    font-weight: 400;
    font-size: 1.5em;
    line-height: 1.2;
    color: var(--neutral-900);
}
.masthead-launching__btn {
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    background-color: #052A25;
    color: var(--neutral-900);
    padding: 1.5em 5em;
    border-radius: 3.2em;
    text-align: center;
    cursor: pointer;
}
.masthead-launching__btn:hover {
    transform: translateY(-3px);
    background-color: var(--neutral-900);
    color: #052A25;
}
.masthead-launching__btn span{
    font-family: 'Invention', sans-serif;
    font-weight: bold;
    font-size: 2em;
    line-height: 1.2;
}
.masthead-launching__noted {
    font-weight: 400;
    font-size: 1.2em;
    line-height: 1.5;
    color: var(--neutral-900);
}
.masthead-launching__noted b {
    font-weight: 700;
}
.masthead-launching__noted sup{
    font-weight: 400;
    font-size: 0.5em;
    line-height: 1.5;
    color: var(--neutral-900);
}
.scroll-btn {
    position: absolute;
    bottom: 5svh;
    display: flex;
    flex-direction: column;
    justify-content: center;  
    align-items: center;
    gap: 1em;
    cursor: pointer;
    animation: animation-arrow-y 1s linear 0s infinite alternate;
    -webkit-animation: animation-arrow-y 2s linear 0s infinite alternate;
}
.scroll-btn span {
    font-weight: 400;
    font-size: 2em;
    line-height: 1.3;
    color: var(--neutral-900);
}
.scroll-btn img {
    width: 1.2em;
    filter: brightness(0) saturate(100%) invert(95%) sepia(0%) saturate(7500%) hue-rotate(112deg) brightness(107%) contrast(107%);
}
@-webkit-keyframes animation-arrow-y {
    0% {
        -webkit-transform: translate(0px, 5px);
    }

    100% {
        -webkit-transform: translate(0px, -10px);
    }
}

@-moz-keyframes animation-arrow-y {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: 0 600%;
    }
}
.masthead-launching__noted br:last-of-type{
    display: none;
}

@media (max-width: 575px) {
    .video-gif-genv {
        width: 100%;
        left: 0;
    }
    .menu-header {
        background: #ffffff;
    }
    .section-masthead-launching {
        position: relative;
        height: calc(100svh - 110px);
        font-size: 0.55rem;
        flex-direction: column;
        align-items: center;
        gap: 5svh;
    }
    .root-container {
        background-size: cover;
    }
    .section-masthead-launching__left {
        position: absolute;
        width: 100%;
        height: 100%;
    }
    .stamp-genv {
        width: 22.5em;
        height: 22.5em;
        position: relative;
        left: -3%;
        top: -4%;
    }
    .section-masthead-launching__right {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 10svh;
    }
    .masthead-launching__content {
        display: flex;
        flex-direction: column;
        gap: 4vw;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
    }
    .masthead-launching__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .masthead-launching__noted {
        /* position: absolute; */
        bottom: 25vw;
        font-size: 1.3em;
        margin: 0 auto;
        margin-top: 10vw;
        /* text-align: center; */
        width: 90%;
        left: 50%;
        /* transform: translateX(-50%); */
    }
    .masthead-launching__content h1 {
        line-height: 1.3;
        font-size: 4vw;
    }
    
    .masthead-launching__content p {
        font-size: clamp(2.5em, 3vw, 1.8em);
    }
    .masthead-launching__btn span {
        font-size: 3em;
    }
    .scroll-btn {
        display: none;
    }
    .hidden-mobile {
        display: none;
    }
    .masthead-launching__noted br:last-of-type{
        display: block;
    }

}

@media (min-width: 576px) and (max-width: 768px) {
    .video-gif-genv {
        width: 100%;
        left: 0;
    }
    
    .menu-header {
        background: #ffffff;
    }

    .section-masthead-launching {
        position: relative;
        height: calc(100svh - 90px);
        font-size: 0.9rem;
        flex-direction: column;
        align-items: center;
        gap: 5svh;
    }
    .root-container {
        background-size: cover;
    }
    .section-masthead-launching__left {
        position: absolute;
        width: 100%;
        height: 100%;
    }
    .stamp-genv {
        width: 19.5em;
        height: 19.5em;
        position: relative;
        left: -2%;
        top: 4%;
    }
    .section-masthead-launching__right {
        width: 100%;
    }
    .masthead-launching__content {
        display: flex;
        flex-direction: column;
        gap: 2svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 5svh;
    }
    .masthead-launching__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .masthead-launching__noted {
        width: 90%;
        position: absolute;
        bottom: 5svh;
        left: 50%;
        transform: translateX(-50%);
    }
    .masthead-launching__content h1 {
        line-height: 1.4;
    }
    .scroll-btn {
        display: none;
    }
    .hidden-mobile {
        display: none;
    }
    .masthead-launching__noted br:last-of-type{
        display: block;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .menu-header {
        background: #ffffff;
    }
    .section-masthead-launching {
        position: relative;
        height: calc(100svh - 90px);
        font-size: 1rem;
        flex-direction: column;
        align-items: center;
        gap: 5svh;
    }
    .root-container {
        background-size: cover;
    }
    .section-masthead-launching__left {
        position: absolute;
        width: 100%;
        height: 100%;
    }
    .stamp-genv {
        width: 19.5em;
        height: 19.5em;
        position: relative;
        left: -2%;
        top: 4%;
    }
    .section-masthead-launching__right {
        width: 90%;
    }
    .masthead-launching__content {
        display: flex;
        flex-direction: column;
        gap: 3svh;
        position: unset;
        top: auto;
        transform: none;
        text-align: center;
        margin-top: 10svh;
    }
    .masthead-launching__btn {
        margin: 0 auto;
        position: relative;
        font-size: 1.5em;
    }
    .masthead-launching__noted {
        position: absolute;
        bottom: 5svh;
        left: 50%;
        transform: translateX(-50%);
    }
    .scroll-btn {
        display: none;
    }
    .hidden-mobile {
        display: none;
    }
    .masthead-launching__noted br:last-of-type{
        display: block;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .menu-header {
        background: #ffffff;
    }
    .section-masthead-launching {
        position: relative;
        height: calc(100svh - 90px);
        font-size: 0.76rem;
        flex-direction: row;
        align-items: center;
        gap: 7vw;
    }
    .section-masthead-launching__left {
        height: 83svh;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .section-masthead-launching {
        font-size: 0.7rem;
        gap: 3vw;
    }
    .section-masthead-launching__left {
        height: 83svh;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .section-masthead-launching {
        font-size: 0.9rem;
    }
    .section-masthead-launching__left {
        height: 86svh;
    }
}

@media (min-width: 1599px) {
    .stamp-genv {
    width: 19.5em;
    height: 19.5em;
    position: relative;
    left: -4%;
    top: 10%;
    }

    .container {
        max-width: 100%;
    }
}
    