.banner-kv-moh {
    display: flex;
    width: 100%;
    height: 100svh;
    justify-content: center;
    font-size: 1rem;
}

.main-menu .nav-menu>ul {
    justify-content: center;
}
@media (min-width: 1100px) {
    .main-menu {
        justify-content: center;
    }
    .main-menu .nav-menu {
        flex-grow: initial;
    }
}

.bg-moh-ldp {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.bg-moh-ldp img {
    width: 100%;
    height: inherit;
    object-fit: cover;
}

.bg-moh-ldp picture {
    width: 100%;
    height: inherit;
    object-fit: cover;
}

.top-banner-moh {
    position: absolute;
    top: 18%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 0.9em;
    gap: 1em;
}

.title-moh-ldp {
    display: flex;
    flex-direction: column;
    text-align: center;
    align-items: center;
    margin-top: 2em;
    font-size: 1em;
    z-index: 1;
}

.title-moh__sub {
    font-weight: 700;
    font-size: 3.2em;
    line-height: 1.1;
}

.title-moh__main {
    font-weight: 400;
    font-size: 2.6em;
    line-height: 1.1;
    margin-top: 0.5em;
}

.logo-byt-moh {
    width: 26em;
    aspect-ratio: 1;
}

.logo-byt-moh img {
    width: 100%;
}

.tagline-moh {
    width: 136.2em;
    margin-top: 0;
    aspect-ratio: 1362 / 194;
}

.tagline-moh img {
    width: 100%;
    object-fit: cover;
}

.bottom-kv-moh {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
}

.bottom-kv-moh img {
    width: 100%;
    object-fit: cover;
}

@media (max-width: 1099px) {
    body {
        max-height: none;
        min-height: 100svh;
        overflow-y: auto;
    }
}

@media (max-width: 575px) {
    .banner-kv-moh {
        font-size: 0.5rem;
        height: calc(100svh - 100px);
        position: relative;
    }

    .top-banner-moh {
        top: 45%;
        transform: translateY(-50%);
    }

    .fast-action-controls {
        display: none;
    }

    .tagline-moh {
        width: 90%;
        margin-top: 3em;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .banner-kv-moh {
        font-size: 0.8rem;
        height: calc(100svh - 23em);
        position: relative;
    }

    .top-banner-moh {
        top: 45%;
        transform: translateY(-50%);
    }

    .fast-action-controls {
        display: none;
    }

    .tagline-moh {
        width: 85%;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .banner-kv-moh {
        font-size: 0.85rem;
        height: calc(100svh - 20em);
        position: relative;
    }

    .top-banner-moh {
        top: 45%;
        transform: translateY(-50%);
        font-size: 0.75em;
    }

    .fast-action-controls {
        display: none;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .banner-kv-moh {
        font-size: 0.75rem;
    }

    .top-banner-moh {
        top: 40%;
        transform: translateY(-50%);
    }

    .fast-action-controls {
        display: none;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .banner-kv-moh {
        font-size: 0.6rem;
    }
    .top-banner-moh {
        top: 20%;
        font-size: 0.85em;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .banner-kv-moh {
        font-size: 0.65rem;
    }
}

/* Section - Question */
.section-question-moh {
    position: relative;
    width: 100%;
    height: auto;
    margin: 10svh auto;
    font-size: 1rem;
}
.section-question-moh.section-2 {
    margin-bottom: 5svh;
}

.group-question {
    position: relative;
    width: 100%;
    height: 25em;
    border-radius: 20px;
    overflow: hidden;
}

.group-question__left {
    width: 56%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 2em 15em 2em 2em;
    background: linear-gradient(90deg, #029986 0%, #3ABAA8 72.16%, rgba(74, 194, 179, 0) 100%);
    z-index: 1;
    position: relative;
}

.group-question__btn {
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    text-transform: uppercase;
    background-color: var(--secondary-500);
    color: var(--neutral-100);
    margin-top: 1em;
    padding: 1em 2em;
    border-radius: 3.2em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.group-question__btn:hover {
    background-color: var(--secondary-700);
    transform: translateY(-2px);
}

.group-question__disc {
    font-weight: 700;
    font-size: 3em;
    line-height: 1.1;
    color: var(--neutral-900);
    text-align: center;
}
.group-question__fact {
    font-weight: 300;
    font-size: 2.4em;
    line-height: 1.2;
    text-align: center;
    color: var(--neutral-900);
}

.group-question__btn span {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.4;
}

.group-question__btn img {
    width: 2.4em;
    margin-left: 10px;
    transform: translateX(-5px);
    filter: brightness(0) saturate(100%) invert(0%) sepia(87%) saturate(7449%) hue-rotate(242deg) brightness(111%) contrast(90%);
}
.hpv-data {
    display: flex;
    gap: 3em;
}
.hpv-data__female,
.hpv-data__male {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2em;
}
.hpv-data__male {
    padding-left: 2em;
}
.hpv-data__male::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 90%;
    aspect-ratio: 1;
    background: var(--neutral-900);
    border-radius: 50%;
    z-index: 0;
}
.hpv-data__percent {
    position: relative;
    width: 12.7em;
    aspect-ratio: 1;
    background: linear-gradient(223.3deg, #FFDE00 13.84%, #84F38D 50.05%, #0DFBF0 86.25%);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}
.hpv-data__percent::before {
    content: "";
    position: absolute;
    width: 90%;
    height: 90%;
    aspect-ratio: 1;
    background: var(--primary-700);
    border-radius: 50%;
    z-index: 0;
}
.hpv-data__percent span{
    position: relative;
    font-weight: 900;
    font-size: 5.8em;
    line-height: 1.2;
    color: var(--neutral-900);
    z-index: 1;
}
.hpv-data__percent::after{
    content: "%";
    position: absolute;
    aspect-ratio: 1;
    font-size: 2em;
    font-weight: 700;
    color: var(--neutral-100);
    background: var(--neutral-900);
    border-radius: 50%;
    padding: 0.2em;
    line-height: 1.3;
    right: -30%;
    top: 30%;
    transform: translate(-50%, -50%);
}
.icon-female {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
}
.icon-female svg {
    width: auto;
    height: 3.4em;
}
.icon-female span {
    font-weight: 700;
    font-size: 2em;
    line-height: 1.3;
    text-align: center;
    color: var(--neutral-900);  
}

.group-question__right {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: fit-content;
}

.group-question__content {
    display: flex;
    flex-direction: column;
    gap: 2em;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    position: absolute;
    width: 100%;
}

.group-question-bg {
    height: inherit;
    width: 100%;
}

.group-question-bg img {
    height: 100%;
    object-fit: cover;
}

.group-question-bg picture {
    height: 100%;
    object-fit: cover;
}

.group-tick {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1em;
    cursor: pointer;
}

.btn-tick-prevent {
    position: relative;
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    text-transform: uppercase;
    background-color: #ffffffb5;
    color: var(--primary-500);
    padding: 1em 2em;
    border: 2px solid var(--primary-500);
    border-radius: 2em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
    overflow: hidden;
    animation: scaleAnimation 1.5s infinite ease-in-out;
    animation-delay: 1s; /* Initial delay */
}
@keyframes scaleAnimation {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.03);
    }
    75% {
        transform: scale(1.03);
    }
}
.btn-tick-prevent::before {
    content: "";
    position: absolute;
    width: 100px;
    height: 100%;
    background-image: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 30%,
      rgba(255, 255, 255, 0.9),
      rgba(255, 255, 255, 0) 70%
    );
    top: 0;
    left: -100px;
    opacity: 0.6;
    animation: shine 2s ease-out infinite;
}
@keyframes shine {
    0% {
      left: -100px;
    }
  
    60% {
      left: 100%;
    }
  
    to {
      left: 100%;
    }
  }
  

.btn-tick-prevent:hover {
    background-color: var(--primary-500);
    color: var(--neutral-900);
}

.btn-tick-prevent.active {
    background-color: var(--primary-500);
    color: var(--neutral-900);
    border: 2px solid var(--primary-500);
    animation: none;
    transform: scale(1.03);
}

.btn-tick-prevent span {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.2;
}

.title-moh-banner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: var(--neutral-100);
    gap: 0.8em;
}

.title-moh-banner .logo-byt-moh {
    width: 4.8em;
    aspect-ratio: 1;
}

.title-moh-banner__sub {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.3;
    margin-top: 0.3em;
    text-transform: uppercase;
    font-family: Unbounded;
}
.tagline-moh-banner {
    width: 45.1em;
    aspect-ratio: 451 / 91;
}
.tagline-moh-banner img{
    width: 100%;
    object-fit: cover;
}

.title-moh-banner__main {
    font-weight: 700;
    font-size: 2.8em;
    line-height: 1.1;
    padding: 0.3em 0.8em;
    background-color: var(--secondary-500);
    border-radius: 2em;
    text-transform: uppercase;
}

.tick-border {
    width: 7em;
    aspect-ratio: 181 / 179;
}

.line-tick {
    position: absolute;
    z-index: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
}

.line-tick .line-moh {
    width: 114em;
    /* aspect-ratio: 1676 / 438; */
    aspect-ratio: 1986 / 464;
    left: 1em;
    position: relative;
    top: -10em;
    clip-path: inset(0 100% 0 0);
    transition: clip-path 1s ease-in-out;
}

.line-tick .hand-img {
    position: relative;
    top: -4.9em;
    left: -8em;
    width: 24.9em;
    aspect-ratio: 290 / 188;
    opacity: 0;
    transition: opacity 0.5s ease-in-out 0.7s;
}
.section-5 .group-question__left {
    padding: 5em 15em 5em 2em;
}

@media (max-width: 374px) {
    .section-question-moh {
        font-size: 0.6rem !important;
    }
}

@media (max-width: 575px) {
    .section-question-moh {
        font-size: 0.7rem;
        margin: 0 auto 2svh;
    }
    .section-question-moh.section-2 {
        padding: 0;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        /* aspect-ratio: 416 / 442; */
        width: 100%;
        margin: 0 auto;
        border-radius: 20px;

    }

    .group-question__left {
        font-size: 0.85em;
        width: 100%;
        height: 30svh;
        padding: 1em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        gap: 2svh;
        padding-top: 5svh;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: -15%;

    }
    .section-5 .group-question__left {
        width: 100%;
        padding: 1em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 3svh;
        padding-top: 7svh;
    }

    .group-question__right {
        position: relative;
        width: 100%;
        height: 50svh;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .group-question-bg picture {
        height: inherit;
        object-fit: cover;
        width: 100%;
    }

    .group-tick {
        font-size: 1.1em;
    }

    .line-tick .line-moh {
        width: 80em;
        aspect-ratio: 1676 / 438;
        left: -3em;
        position: relative;
        top: -8em;
    }

    .line-tick .hand-img {
        position: relative;
        top: -3em;
        left: -14em;
        width: 21em;
        aspect-ratio: 290 / 188;
    }

    .group-question__content {
        gap: 5svh;
        top: 45%;
    }

    .group-question__btn {
        margin-top: 0;
        font-size: 1.2em;
    }
}

@media (max-width: 374px) {
    .section-question-moh {
        font-size: 0.6rem !important;
    }

    .line-tick .hand-img {
        position: relative;
        top: -2em;
        left: -17em;
        width: 22em;
        aspect-ratio: 290 / 188;
    }

    .group-tick {
        font-size: 1.1em;
    }
}

@media (min-width: 575px) and (max-width: 769px) {

    .group-question__content {
        gap: 5svh;
        font-size: 1.2em;
    }

    .bottom-kv-moh {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
    }

    .tagline-moh {
        width: 83.2em;
        margin-top: -0;
        aspect-ratio: 2144 / 868;
    }

    .section-question-moh {
        font-size: 0.9rem;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        aspect-ratio: 416 / 442;
        width: 90%;
        margin: 0 auto;
    }

    .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        font-size: 1em;
        padding-top: 10svh;
    }
    .section-5 .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .group-question__right {
        position: relative;
        margin-bottom: -14%;
    }

    .group-tick {
        font-size: 1em;
    }
    .line-tick .line-moh {
        width: 114em;
        aspect-ratio: 1986 / 464;
        left: 3em;
        position: relative;
        top: -11em;
        clip-path: inset(0 100% 0 0);
    }
    .line-tick .hand-img {
        position: relative;
        top: -3.5em;
        width: 26em;
        left: -24em;
        aspect-ratio: 290 / 188;
        opacity: 0;
        transition: opacity 0.5s ease -in-out 0.8s;
    }

    .line-tick {
        width: max-content;
    }

    .group-question__content {
        gap: 5svh;
    }
}

@media only screen and (min-width: 769px) and (max-width: 992px) {
    .section-question-moh {
        font-size: 1rem;
    }

    .group-question {
        display: flex;
        flex-direction:column-reverse;
        height: auto;
        aspect-ratio: 416 / 442;
        width: 80%;
        margin: 0 auto;
    }

    .group-question__disc {
        font-weight: 500;
        line-height: 1.1;
        color: var(--neutral-900);
        text-align: center;
    }

    .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }
    .section-5 .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .group-question__right {
        position: relative;
        margin-bottom: -14%;
    }

    .group-tick {
        margin-top: 3em;
        font-size: 0.9em;
    }
    .line-tick .line-moh {
        width: 114em;
        aspect-ratio: 1986 / 464;
        left: -1em;
        position: relative;
        top: -11em;
        clip-path: inset(0 100% 0 0);
    }
    .line-tick .hand-img {
        position: relative;
        top: -4.5em;
        width: 29em;
        left: -20em;
        aspect-ratio: 290 / 188;
        opacity: 0;
        transition: opacity 0.5s ease -in-out 0.8s;
    }

    .line-tick {
        width: max-content;
        font-size: 0.8em;
    }

    .group-question__content {
        gap: 5svh;
    }
}

@media only screen and (min-width: 991px) and (max-width: 1199px) {
    .section-question-moh {
        font-size: 0.7rem;
    }
    /* .group-question__left {
        width: 100%;
    } */
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .section-question-moh {
        font-size: 0.75rem;
    }
    .group-question__left {
        width: 53%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .section-question-moh {
        font-size: 0.9rem;
    }
    .group-question__left {
        width: 55%;
    }
}

/* SECTION - 3 - Information */
.container-section-3 {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    background-repeat: no-repeat;
    padding-left: 5vw;
    padding-right: 5vw;
    padding-top: 15svh;
    font-size: 1rem;
    background: linear-gradient(170.14deg, #FFFFFF 20.09%, #E9FFEB 75%);
}

.wrapper-section-3 {
    display: flex;
    justify-content: center;
    gap: 2vw;
}

.wrapper-textbox {
    flex: 4;
    display: flex;
    flex-direction: column;
    /* justify-content: center; */
    padding-left: 10vw;
    padding-top: 2em;
}

.container-section-3 .text-box {
    width: 1%;
    height: fit-content;
    position: relative;
    display: table;
}

.container-section-3 .title {
    display: block;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    overflow: hidden;
    position: relative;
    white-space: nowrap;
    color: #009885;
    font-weight: 700;
    font-size: 3.6em;
    line-height: 1.1;  
}

.container-section-3 .description {
    color: #333;
    margin: 4svh 0 4svh;
    font-weight: 400;
    font-size: 1.8em;
    line-height: 1.2;
    text-align: justify;
}

.wrapper-section-3 .detail-campain__cta {
    margin: unset;
    text-transform: none;
}

.container-section-3 .detail-image-banner {
    position: relative;
    flex: 6;
    right: 0;
    bottom: 0;
    aspect-ratio: 1623 / 1101;;
}

.container-section-3 .img-people {
    width: inherit;
    object-fit: cover;
}

.container-section-3 .arrow-vector {
    transform: scale(0.4);
}

.container-section-3 .border-arrow {
    width: 22px;
    height: 22px;
    background-color: #ffffff32;
    border-radius: 50%;
    display: flex;
    justify-content: center;
}



@media (max-width: 576px) {
    .wrapper-section-3 {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .wrapper-textbox {
        display: flex;
        font-size: 0.8em;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    .container-section-3 .cta-btn {
        font-size: 0.8em;
        max-width: 15em;
        position: absolute;
        bottom: -3.2em;
        padding: 0.3em 1em;
        z-index: 2;
    }

    .container-section-3 .detail-image-banner {
        height: auto;
    }

    .container-section-3 {
        margin-top: 10svh;
    }
}

@media (max-width: 375px) {
    .wrapper-textbox {
        display: flex;
        font-size: 0.6em;
        align-items: center;
        justify-content: center;
        padding: 0;
    }
}

@media (max-width: 375px) {
    .wrapper-textbox {
        display: flex;
        font-size: 0.6em;
        align-items: center;
        justify-content: center;
        padding: 0;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .wrapper-section-3 {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .wrapper-textbox {
        display: flex;
        font-size: 1.1em;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    .container-section-3 .cta-btn {
        max-width: 13em;
        position: absolute;
        bottom: -17.5em;
        z-index: 2;
    }

}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .wrapper-section-3 {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .wrapper-textbox {
        display: flex;
        font-size: 1.1em;
        align-items: center;
        justify-content: center;
        padding-left: 0;
    }

    .container-section-3 .cta-btn {
        max-width: 13em;
        position: absolute;
        bottom: -17.5em;
        z-index: 2;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .container-section-3  {
        font-size: 0.75em;
    }
    .container-section-3 .description {
        margin: 2svh 0 2svh;
    }
    .wrapper-textbox {
        flex: 4;
        padding-left: 5vw;
        padding-top: 2em;
    }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .container-section-3  {
        font-size: 0.85em;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .container-section-3  {
        font-size: 0.95em;
    }
}


/* SECTION - 4 - Detail- Campain */
.section-detail-campain {
    width: 100%;
    height: 100svh;
    font-size: 1rem;
    overflow: hidden;
}

.detail-campain__group {
    width: inherit;
    height: inherit;
    position: relative;
    display: flex;
}

.detail-campain__left {
    position: relative;
    flex: 8;
    height: 100%;
    background-image: url(../../../images/ldp-moh/bg-campain.webp);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.detail-campain__right {
    flex: 4;
    background: linear-gradient(322.87deg, #E3FFFC 0%, #F6FFF6 100%);
    display: flex;
    align-items: center;
}

.detail-campain__left .top-banner-moh {
    position: relative;
    top: 20%;
}

.detail-campain__left .tagline-moh {
    margin-top: 2svh;
    width: 101.9em;
    aspect-ratio: 997 / 216;
}

.detail-campain__left .start-day {
    margin-top: 2svh;
    width: 41.4em;
    aspect-ratio: 414 / 80;
}

.group-logos-moh {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2%;
    margin-bottom: 2%;
}

.logo-hpv {
    width: 22.2em;
    aspect-ratio: 222 / 70;
}

.logo-msd {
    width: 14.85em;
    aspect-ratio: 1485 / 620;
}

.group-logos-moh img {
    width: inherit;
    object-fit: cover;
}

.detail-campain__content {
    position: relative;
    width: 20vw;
    left: 42%;
    transform: translateX(-50%);
    font-size: 0.92em;
}

.detail-campain__cta {
    display: block;
    width: fit-content;
    transition: all 250ms ease-in-out;
    text-transform: uppercase;
    background-color: var(--primary-500);
    color: var(--neutral-900);
    margin: 5svh auto 0;
    padding: 1em 2em;
    border-radius: 3.2em;
    font-weight: 500;
    line-height: 1.1;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.detail-campain__cta:hover {
    background-color: var(--primary-700);
    transform: translateY(-2px);
}

.group-question__disc {
    font-weight: 500;
    font-size: 3em;
    line-height: 1.1;
    color: var(--neutral-900);
    text-align: center;
}

.detail-campain__cta span {
    font-weight: 700;
    font-size: 1.6em;
    line-height: 1.4;
}

.detail-campain__cta img {
    width: 2.4em;
    margin-left: 10px;
    transform: translateX(-5px);
}

.detail-campain__title {
    font-weight: 700;
    font-size: 3.2em;
    color: var(--primary-500);
}

.detail-campain__content p {
    font-weight: 400;
    font-size: 2em;
    line-height: 1.3;
    text-align: justify;
    color: #2F2F2F;
    margin-top: 3svh;
}
    /* Question section 5 */
.section-question-moh.section-5 {
    margin: 0 auto;
    padding-top: 10svh;
    padding-bottom: 2svh;
    padding-left: 2svh;
    background: linear-gradient(180deg, #FFFFFF 0%, #EBFFED 80%);
}
.moh-policy {
    font-weight: 400;
    font-size: 0.8em;
    line-height: 1.5;
    margin-top: 5em;
}

@media (max-width: 575px) {
   

    .detail-campain__group {
        font-size: 0.55em;
        display: flex;
        flex-direction: column;
    }

    .detail-campain__content {
        position: relative;
        width: 80%;
        left: auto;
        transform: none;
    }

    .detail-campain__left .top-banner-moh {
        position: relative;
        top: 45%;
    }

    .detail-campain__left .tagline-moh {
        width: 90%;
    }

    .detail-campain__left .start-day {
        width: 75%;
    }

    .detail-campain__left {
        position: relative;
        flex: auto;
        height: auto;
        z-index: 2;
    }

    .line-tick {
        width: max-content;
    }

    .detail-campain__right {
        margin-top: -5svh;
        padding: 5em 0 2em;
        flex: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(347.07deg, #E3FFFC 2.82%, #FFFFFF 85.77%);
        z-index: 1;
    }
    .section-detail-campain .detail-campain__cta {
        font-size: 1.5em;
    }
    .detail-campain__content p {
        font-size: 2.5em;
    }

    .moh-policy {
        font-weight: 400;
        font-size: 1.1em;
        margin-left: 2svh;
        margin-top: 2em;
    }
}

@media (min-width: 576px) and (max-width: 768px) {
    .detail-campain__group {
        font-size: 0.75em;
        display: flex;
        flex-direction: column;
    }

    .detail-campain__content {
        position: relative;
        width: 80%;
        left: auto;
        transform: none;
    }

    .detail-campain__left .top-banner-moh {
        position: relative;
        top: 45%;
    }

    .detail-campain__left .start-day {
        font-size: 1.5em;
    }

    .detail-campain__left {
        position: relative;
        flex: auto;
        height: auto;
        z-index: 2;
    }

    .detail-campain__right {
        margin-top: -5svh;
        flex: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(347.07deg, #E3FFFC 2.82%, #FFFFFF 85.77%);
        z-index: 1;
    }

    .detail-campain__left .tagline-moh {
        width: 90%;
    }

    .detail-campain__left .start-day {
        width: 65%;
    }

    .group-question__disc {
        font-weight: 500;
        font-size: 2em;
        line-height: 1.1;
        color: var(--neutral-900);
        text-align: center;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .detail-campain__group {
        font-size: 0.85em;
        display: flex;
        flex-direction: column;
    }

    .detail-campain__content {
        position: relative;
        width: 80%;
        left: auto;
        transform: none;
    }

    .detail-campain__left .top-banner-moh {
        position: relative;
        top: 45%;
    }

    .detail-campain__left .start-day {
        font-size: 1.5em;
    }

    .detail-campain__left {
        position: relative;
        flex: auto;
        height: auto;
        z-index: 2;
    }

    .detail-campain__right {
        margin-top: -5svh;
        flex: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(347.07deg, #E3FFFC 2.82%, #FFFFFF 85.77%);
        z-index: 1;
    }

}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .detail-campain__group {
        font-size: 0.9em;
        display: flex;
        flex-direction: column;
    }

    .detail-campain__content {
        position: relative;
        width: 80%;
        left: auto;
        transform: none;
    }

    .detail-campain__left .top-banner-moh {
        position: relative;
        top: 45%;
    }

    .detail-campain__left .start-day {
        font-size: 1.5em;
    }

    .detail-campain__left {
        position: relative;
        flex: auto;
        height: auto;
        z-index: 2;
    }

    .detail-campain__right {
        margin-top: -2svh;
        flex: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(347.07deg, #E3FFFC 2.82%, #FFFFFF 85.77%);
        z-index: 1;
    }
    .moh-policy {
        font-size: 1em;
        line-height: 1.5;
        margin-top: 5em;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .detail-campain__group {
        font-size: 0.7em;
    }
    .moh-policy {
        font-size: 1.2em;
        line-height: 1.5;
        margin-top: 5em;
    }
    .detail-campain__content {
        width: 21.5vw;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .detail-campain__group {
        font-size: 0.76em;
    }

    .container {
        padding-right: 0.75rem;
        padding-left: 0.75rem;
    }
}

    .section-question-moh.section-5 {
        margin: 0 auto;
        padding-top: 10svh;
        padding-bottom: 2svh;
        padding-left: 2svh;
        background: linear-gradient(180deg, #FFFFFF 0%, #EBFFED 80%);
    }

@media (max-width: 575px) {
    .section-question-moh.section-5 {
        padding: 0;
        padding-bottom: 2svh;
    }
}

/* Footer MOH */
.footer-moh {
    font-size: 1rem;
    color: #373737;
}

.footer-moh__content {
    padding: 5em 0 5em;
    background-color: var(--primary-500);
}

.footer-moh__content .footer-moh__line-2 {
    display: block;
    font-weight: 300;
    font-size: 1.2em;
    line-height: 1.1;
    text-align: center;
}
.footer-moh__content .footer-moh__line-1 {
    display: block;
    font-weight: 700;
    font-size: 1.4em;
    line-height: 2;
    text-align: center;    
}

.footer-moh__content .direction {
    margin-top: 1em;
    font-weight: 600;
    font-size: 1em;
    line-height: 1.1;
    text-align: center;
}
.nowrap {
    white-space: nowrap;
}

@media (max-width: 575px) {
    .footer-moh {
        font-size: 0.7rem;
        padding-bottom: 60px;
    }
    .footer-moh__content .footer-moh__line-1 {
        font-size: 2.4em;
    }
    .footer-moh__content {
        padding: 3em 5em 5em;
    }
    .footer-moh__content .direction {
        font-size: 1.5em;
    }
    
    
}

@media (min-width: 576px) and (max-width: 768px) {
    .footer-moh {
        font-size: 0.8rem;
        padding-bottom: 60px;
    }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
    .footer-moh {
        font-size: 0.85rem;
        padding-bottom: 60px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .footer-moh {
        font-size: 0.8rem;
        padding-bottom: 60px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .footer-moh {
        font-size: 0.85rem;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .footer-moh {
        font-size: 0.9rem;
    }
}