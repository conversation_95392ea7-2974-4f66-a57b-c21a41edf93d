.root-container {
  /* background: linear-gradient(0deg, #FCEA6F -11.65%, #00B4CF 88.35%); */
  background-image: url(../../../images/male/background.webp);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.container-content-male {
  /* width: 100%;
  height: 75vh;
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 2.5em; */
  width: 100%;
  max-height: 75vh;
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 2.5em;
  aspect-ratio: 16 / 6.5;
}

.footer-main-content-mobile {
  display: none;
}

.content__left {
  flex: 6;
  position: relative;
  width: 0;
  border-radius: 2em;
  overflow: hidden;
}

.content__center {
  flex: 3;
}

.content__right {
  flex: 3;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(194, 248, 255, 0) 100%), radial-gradient(181.75% 160.16% at 113.2% -65.7%, #FEE000 0%, #FDFFDC 100%) /* warning: gradient uses a rotation that is not supported by CSS and may not behave as expected */;
  border-radius: 2em;
  overflow: hidden;
}

.content__left .swiper-pagination-bullet {
  width: 1.2em;
  height: 1.2em;
  background: #EEEEEE;
  border: 1px solid #EEEEEE;
  opacity: 1;
}

.content__left .swiper-pagination-bullet-active {
  width: 4em;
  transition: width .5s;
  border-radius: 5px;
  background: var(--primary-700);
  border: 1px solid transparent;
}

.mySwiper-left .swiper-pagination-custom-new {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  width: fit-content;
  bottom: 3em;
  z-index: 1;
  display: flex;
  gap: 1em;
}

.mySwiper-left.swiper {
  width: 100%;
  height: 100%;
}

.mySwiper-left .swiper-slide {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: auto;
}

.mySwiper-left .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mySwiper-left .swiper-slide picture {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content__left .title-img {
  position: absolute;
  top: 5em;
  left: 2em;
  z-index: 2;
}

.content__left .title-img img {
  width: 67.5em;
}

.content__left .description-banner {
  position: absolute;
  bottom: 3em;
  left: 2em;
  width: 90%;
  z-index: 2;
}

.content__center {
  display: flex;
  flex-direction: column;
  gap: 1em;
  border-radius: 2em;
  overflow: hidden;
  background: linear-gradient(207.71deg, #FFFFFF -36.69%, rgba(255, 255, 255, 0.3) 105.14%);
}

.group-box-male {
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(3, 1fr);
  grid-column-gap: 0px;
  grid-row-gap: 1em;
  padding: 0 1em 1em;
}

.male-box {
  position: relative;
  display: flex;
  justify-content: flex-end;
  flex-direction: column;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 200ms ease-in;
  flex: auto;
  gap: 0.5em;
  background: var(--neutral-900);
  padding: 1em 2em;
}

.male-box .bg-overlay {
  background: linear-gradient(203.11deg, rgba(102, 102, 102, 0) 43.61%, rgba(21, 43, 81, 0.2) 85.22%);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: var(--radius-sm);
  z-index: 1;
}

.male-box__content {
  color: var(--neutral-900);
  font-size: 2.4em;
  font-weight: 800;
  line-height: 1.2;
  text-shadow: 0px 4px 3px rgba(0, 0, 0, 0.4),
    0px 8px 13px rgba(0, 0, 0, 0.1),
    0px 18px 23px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.male-box.box-1,
.male-box.box-2,
.male-box.box-3 {
  background: linear-gradient(70.44deg, #00BED1 -0.01%, #B4E6E2 90.61%);
}

/* .male-box.box-2 {
  background: linear-gradient(to right, #8cd3f8, #d5efff);
}

.male-box.box-3 {
  background: linear-gradient(to right, #8cd3f8, #d5efff);
} */

.male-box__img {
  position: absolute;
  right: 2em;
  bottom: 0;
  transition: transform 0.3s ease, transform-origin 0.3s ease;
  transform-origin: bottom center;
}

.male-box:hover .male-box__img {
  transform: scale(1.1);
  z-index: 2;
}

.male-box__img img {
  width: 27.1em;
  aspect-ratio: 271/200;
}

.male-box__cta {
  width: fit-content;
  position: relative;
  border-radius: 5em;
  overflow: hidden;
  padding: 0.4em 1.6em;
  left: -1.6em;
  transition: all 0.2s ease;
  border: none;
  background: none;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.male-box__cta:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  border-radius: 50px;
  background: var(--secondary-500);
  width: 0;
  height: 100%;
  transition: all 0.3s ease;
}

.male-box__cta span {
  position: relative;
  font-family: "Ubuntu", sans-serif;
  font-size: 1.8em;
  font-weight: 700;
  letter-spacing: 0.05em;
  color: var(--neutral-900);
}

.male-box__cta img {
  width: 2.4em;
  position: relative;
  top: 0;
  margin-left: 10px;
  transform: translateX(-5px);
  transition: all 0.3s ease;
}

.male-box__cta:hover:before {
  width: 100%;
  background: var(--secondary-500);
  border: 1px solid var(--neutral-900);
}

.male-box__cta:hover span {
  color: var(--primary-700);
}

.male-box__cta:hover img {
  transform: translateX(0);
  filter: invert(30%) sepia(63%) saturate(1800%) hue-rotate(147deg) brightness(88%) contrast(101%);
}

.male-box__cta:active {
  transform: scale(0.95);
}

.content-center__title {
  width: 100%;
  font-size: 2em;
  font-weight: 700;
  line-height: 1.2;
  text-align: center;
  padding: 0.5em 0;
  margin: 0 auto;
  background: var(--primary-900);
  color: var(--neutral-900);
}

.content-right__title {
  font-size: 2.6em;
  font-weight: 500;
  line-height: 1.1;
  text-align: center;
  color: var(--neutral-100);
}

.content-right__title span {
  font-family: Unbounded;
  font-size: 1.3em;
  font-weight: 700;
  line-height: 1.2;
  text-align: center;
}

.content-right__top {
  display: flex;
  flex-direction: column;
  gap: 2em;
  margin-top: 5em;
}

.group-cta-male {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1em;
}

.content-right__cta {
  display: block;
  width: fit-content;
  transition: all 250ms ease-in-out;
  text-transform: uppercase;
  background-color: var(--primary-500);
  color: var(--neutral-900);
  font-family: Unbounded;
  font-size: 1.2em;
  padding: 1em 2em;
  border: 1px solid var(--neutral-900);
  border-radius: 3.2em;
  font-weight: 500;
  line-height: 1.2;
  text-align: center;
  cursor: pointer;
}

.content-right__cta:hover {
  background-color: var(--secondary-500);
  color: #000000;
  background-position: 102% 0;
  transform: translate3d(0, -2px, 0);
  transition: all 0.4s ease-in-out;
}
.content-right__img {
  bottom: 0;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70%;
}

.content-right__img img {
  height: 100%;
  object-fit: cover;
}

@media (max-width: 575px){
  .main-menu{
    background-color: var(--neutral-900);
    border-radius: unset;
  }

  .container-content-male .main-container {
    background-image: radial-gradient(25% 70% at -1% 91%, #38ab9f 1%, #38ab9f 1%, rgba(7, 58, 255, 0) 91%), radial-gradient(30% 53% at 62% 163%, #73f2ff 0%, rgba(7, 58, 255, 0) 100%), linear-gradient(125deg, white 8%, #d3e9d6 44%, #ace0c8 93%);
  }

  .root-container {
    overflow: auto !important;
    gap: 0;
    padding-bottom: 13em;
  }

  .container-content-male {
    font-size: 0.8em;
    flex-direction: column;
    height: 100%;
    padding: 2em;
    max-height: max-content;
    aspect-ratio: auto;
}

  .content__left {
    width: 100%;
  }
  .mySwiper-left.swiper {
    height: 50em;
  }
  .swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet, .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 2px);
  }
  .content__left .title-img {
    width: 90%;
  }
  .content__left .description-banner {
    bottom: 2em;
    left: 0;
    width: 100%;
    z-index: 2;
    padding: 0 2em;
    font-size: 0.5em;
    color: var(--neutral-900);
  }
  .mySwiper-left .swiper-pagination-custom-new {
    bottom: 6em;
  }
  .footer__content {
    font-size: 0.7em;
  }

  .male-box {
    height: 20em;
  }

  .content-right__img {
    height: 70%;
    width: 70%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 5em;
  }
  .footer__content {
    padding: 3em 2em;
  }
  .footer__content h3{
    color: var(--neutral-100);
  }
}
@media (min-width: 576px) and (max-width: 768px){
  .root-container {
    overflow: auto !important;
    gap: 0;
    padding-bottom: 13em;
  }

  .container-content-male {
    font-size: 0.8em;
    flex-direction: column;
    height: 100%;
    padding: 2em;
    max-height: max-content;
    aspect-ratio: auto;
  }

  .content__left {
    width: 100%;
  }

  .footer__content {
    font-size: 1em;
  }

  .male-box {
    height: 15vh;
  }

  .content-right__img {
    height: 70%;
    width: 70%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 5em;
  }

  .footer__content {
    padding: 3em 2em;
  }
  .footer__content h3{
    color: var(--neutral-100);
  }
}
@media only screen and (min-width: 769px) and (max-width: 991px){
  .root-container {
    overflow: auto !important;
    gap: 0;
    padding-bottom: 13em;
  }

  .container-content-male {
    font-size: 0.9em;
    flex-direction: column;
    height: 100%;
    padding: 2em;
    max-height: max-content;
    aspect-ratio: auto;
  }

  .content__left {
    width: 100%;
  }
  .content__left .description-banner {
    position: absolute;
    bottom: 2em;
    left: 2em;
    width: 40%;
    z-index: 2;
}

  .footer__content {
    font-size: 1em;
  }

  .male-box {
    height: 15vh;
  }

  .content-right__img {
    height: 70%;
    width: 70%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 5em;
  }

  .footer__content {
    padding: 3em 2em;
  }
  .footer__content h3{
    color: var(--neutral-100);
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px){
  .root-container {
    overflow: hidden;
    gap: 1em;
    justify-content: flex-start;
  }

  .container-content-male {
    font-size: 0.6em;
    gap: 1.5em;
    max-height: 70vh;
  }

  .footer__content {
    font-size: 0.7em;
  }
  .male-box__img{
    font-size: 0.85em;
  }
  .footer__content {
    padding: 0 0 1em 0;
    font-size: 0.8em;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px){
  .root-container {
    overflow: hidden;
    gap: 0;
  }
  .container-content-male {
    height: 77vh;
    font-size: 0.85em;
  }
  .male-box__img{
    font-size: 0.88em;
  }
  .footer__content {
    padding: 0 0 1em 0;
    font-size: 0.8em;
  }
}
@media only screen and (min-width: 1400px) and (max-width: 1599px){
  .root-container {
    overflow: hidden;
    gap: 0;
    padding: 0 1em;
  }
  .container-content-male {
    height: 78vh;
    font-size: 0.83em;
  }
  .male-box__img{
    font-size: 0.92em;
  }
  .footer__content {
    padding: 0 0 1em 0;
    font-size: 0.8em;
  }
}
