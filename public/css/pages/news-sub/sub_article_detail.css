.ana-col {
    margin-top: 0;
}

.call-cta {
    background: linear-gradient(180deg, rgba(253, 239, 110, 1) 0%, rgba(253, 255, 220, 1) 90%);
}

.call-cta-text p {
    color: var(--black);
}

.call-cta-text h3 {
    color: var(--black);
}

.btn-primary {
    background: rgba(45, 157, 162, 1);
    color: #ffffff;
}

.nitem__support-icon {
    width: 1.368em;
    aspect-ratio: 1;
}

.nitem__support-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ana-main {
    margin-top: 3vw;
}

.ana-articles {
    gap: 1.2vw;
}

.ana-main__wrapper {
    gap: 1vw;
}