.txt-hover-red:hover{
    color: var(--red);
    translate: 1s;
}
.mb-3{
    margin-bottom: 3em;
}
:root {
    --bg-black: 0,0,0;
    --gray: 129,129,129;
    --bg-gray: 246, 248, 250;
    --bg1: #005750;
    --yellow:#FEE000;
    --blue:#007aff;
    --black: #000000;
    --white: #ffffff;
}
.m-show {
    display: none;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #0000006a;
    z-index: 999;
}

.header-wrapper {
    margin: 2em auto 0 auto;
    width: 95vw;
    height: 5.7em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1em 2em;
    font-size: 0.7vw;
    border-radius: 3em;
    background-color: white;
    -webkit-box-shadow: -2px 0px 12px 1px rgba(0,0,0,0.2);
    -moz-box-shadow: -2px 0px 12px 1px rgba(0,0,0,0.2);
    box-shadow: -2px 0px 12px 1px rgba(0,0,0,0.2); 
}

.hpv-logo__img {
    width: 100%;
    height: 100%;
}

.hpv-logo {
    height: 100%;
}

.bg-home {
    background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px;
    background-image: radial-gradient(25% 70% at -1% 91%, #38ab9f 1%, #38ab9f 1%, #073AFF00 91%), radial-gradient(30% 53% at 62% 163%, #73F2FFFF 0%, #073AFF00 100%), linear-gradient(125deg, var(--neutral-900) 8%, #d3e9d6 44%, #ACE0C8FF 93%);
    background-repeat: no-repeat;
}

.navbar {
    gap: 1em;
    display: flex;
    justify-content: center;
    align-items: center;
}

.navbar-search, .m-menubar__icon {
    border-radius: 50%;
    width: 3em;
    height: 3em;
    padding: 2px;
    cursor: pointer;
    transition: all 0.25s ease-in-out;
}

.navbar-search__icon {
    width: 100%;
    height: 100%;
}

.menus {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1em;
    font-size: 0.7vw;
}

.menus__item {
    position: relative;
    padding: 0.5em 1em;
    border-radius: 2em;
    border: 1px solid var(--primary-500);
    list-style-type: none;
}

.menus__item__sub {
    font-size: 1.1em;
    font-weight: 400;
}

.main-menu .nav-menu__item.is-female>a:hover img {
    filter: brightness(0) saturate(100%) invert(0%) sepia(71%) saturate(7500%) hue-rotate(171deg) brightness(109%) contrast(98%);
}

.c-default:hover, .c-default.active {
    background-color: var(--primary-500);
    color: var(--neutral-900);
}

.c-yellow:hover, .c-yellow.active {
    background-color: var(--secondary-500);
    border: 1px solid var(--secondary-500);
}

.c-blue .menus-sub-wrapper a {
    color: var(--neutral-100);
}

.c-blue:hover, .c-blue.active {
    background-color: #2e56f6;
    border: 1px solid #2e56f6;
    color: var(--neutral-900);
}

.menus-sub {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
}

.menus__item:hover .menus-sub-wrapper {
    visibility: visible;
    opacity: 1;
    clip: rect(0px, 100vw, 200vh, -100vw);
    transition: clip 0.6s linear, opacity 0.4s linear;
}

.menus-sub__icon {
    font-size: 1.5em;
    line-height: 1;
}

.menus-sub-wrapper {
    position: absolute;
    top: 0;  
    background-color: #ffffff;
    font-size: 0.7vw;
    width: 17vw;
    padding: 1.5em;
    left: 50%;
    transform: translate(-50%, 30%);
    opacity: 0;
    visibility: hidden;
    border-radius: 1.5em;
    display: flex;
    flex-direction: column;
    gap: 0.5em;
    -webkit-box-shadow: -2px 0px 12px 1px rgba(0,0,0,0.2);
    -moz-box-shadow: -2px 0px 12px 1px rgba(0,0,0,0.2);
    box-shadow: -2px 0px 12px 1px rgba(0,0,0,0.2);
    clip: rect(0px, 200vw, 0, 0px);
    transition: clip 0.6s linear, all 0.4s linear;
}

.menus-sub-item {
    font-size: 1.1em;
    font-weight: 400;
    height: 3em;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 0.7em;
    padding-right: 0.7em;
    border-radius: 2em;
}

.menus-sub-item:hover {
    /* background: linear-gradient(269.45deg, rgba(255, 229, 15, 0.11) -10.77%, #FFE400 77.84%); */
    background: linear-gradient(269.45deg, #B3F3FD -10.77%, #05A8AE 122.08%);
    color: var(--neutral-100);
    cursor: pointer;
    padding-left: 0.9em;
    backdrop-filter: blur(4px);
}
.menus-sub-item.menus-sub-item-yellow:hover {
    background: linear-gradient(269.45deg, rgba(255, 229, 15, 0.11) -10.77%, #FFE400 77.84%);
}

/* navbar mobile */

.mobile-navbar-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    height: fit-content;
    background-color: #ffffff;
    width: 30em;
    padding: 2em;
    display: flex;
    gap: 1em;
    border-radius: 1em;
    -webkit-box-shadow: -2px 0px 12px 1px rgba(0,0,0,0.2);
    -moz-box-shadow: -2px 0px 12px 1px rgba(0,0,0,0.2);
    box-shadow: -2px 0px 12px 1px rgba(0,0,0,0.2);
    z-index: 9999;
}

.navbar-mobile {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menus-mobile {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1em;
    font-size: 0.7vw;
}

.menus-mobile__item {
    padding: 0.5em 1em;
    border-radius: 2em;
    border: 1px solid var(--primary-500);
    font-size: 1.5rem;
    width: 100%;
    display: flex;
    justify-content: space-between;
}

/* 
* <576:     xs
* 576-768:  sm
* 768-992:  md
* 992-1200: lg
* 1200 -1400: xl
* >= 1400:  xxl
*/

/**
 * mobile menu
 */
@media (max-width: 575px){   
    .nitem{
        flex-direction: column;
        gap: 1em;
    }

    .nitem__thumb{
        width: 100%;
        aspect-ratio: 16/9;
    }

    .nitem__thumb-img{
        border-top-left-radius: 2rem;
        border-top-right-radius: 2rem;
        border-bottom-left-radius: 0;
    }

    .nitem__content{
        width: 100%;
        padding: 10px;
        margin-bottom: 2em;
    }
   
}
@media (min-width: 576px) and (max-width: 768px){
    .nitem__content{
        font-size: 7px;
    }

    .row-grid{
        grid-template-columns: 1fr 1fr;
    }
}
@media (max-width: 768px){  

    .m-hiden{
        display: none;
    }
    .m-show{
        display: block;
    }

    .header-wrapper{
        font-size: 1rem;
        margin: 0;
        width: 100%;
        border-radius: unset;
    }   
}
@media only screen and (min-width: 769px) and (max-width: 991px) {
    html {      
        font-size: 50%;
    }
}
@media only screen and (min-width: 769px) and (max-width: 1024px) {
}
@media only screen and (min-width: 992px) and (max-width: 1199px){ 
}
@media only screen and (min-width: 1200px) and (max-width: 1399px){
}




