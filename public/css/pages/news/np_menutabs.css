.menubrc-wrapper {
    padding-bottom: 1.2em;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.menubrc {
    display: flex;
    gap: 2em;
    /* padding-bottom: 0.5em; */
    justify-content: center;
    font-size: 1vw;
}
.menubrc-item {
    text-decoration: none;
    color: #000;
    font-weight: 500;
    padding: 0.2em 1.6em;
    border-radius: 100px;
    transition: background-color 0.3s ease;
    cursor: pointer;
}
.menubrc-item:hover {
    background: rgba(0, 149, 133, 1);
    color: #fff;
}

.menubrc-item.active {
    background-color: #009688;
    color: #fff;
    font-weight: bold;
}
.menu-line-brc {
    height: 1px;
    background-color: rgba(208, 208, 208, 1);
    width: 80%;
    margin-top: 1.5vw;
}
.nav-btn {
    display: none;
}

/* Responsive */
@media (max-width: 768px) {
    .menubrc {
        gap: 3em;
        padding-bottom: 1em;
        font-size: 1.25vw;
    }
    .nav-btn {
        display: none;
    }
}
@media (max-width: 575px) {
    .menubrc {
        gap: 0.5em;
        padding-bottom: 1em;
        font-size: 2.5vw;
        flex-wrap: nowrap;
        min-width: max-content;
        padding: 8px 5px;
    }
    .menubrc-item {
        text-decoration: none;
        color: var(--neutral-100);
        font-weight: 500;
        padding: 0.6em 2;
        width: 45%;
        display: flex;
        justify-content: center;
        align-items: center;
        white-space: nowrap;
    }
    .menubrc-wrapper.container{
        overflow: hidden;
        position: relative;
        display: block;
    }
    .menubrc-con {
        display: flex;
        flex-direction: row;
    }
    .menubrc-scroll {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    .menubrc-wrapper .menu-line {
        display: none;
    }
     .nav-btn {
        position: absolute;
        top: 33%;
        transform: translateY(-50%);
        background-color: #fff;
        border: 2px solid #009688;
        border-radius: 50%;
        width: 2.5em;
        height: 2.5em;
        font-size: 2.5vw;
        font-weight: bold;
        color: #009688;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        touch-action: manipulation;
    }

    .prev-btn {
        left: 0;
    }

    .next-btn {
        right: 0;
    }
}
  