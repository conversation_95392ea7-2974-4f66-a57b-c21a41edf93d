.ana-articles-2 {
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.nitem-2 {
    display: flex;
    gap: 1em;
    transition: all 0.3s ease-in-out;
    background: rgba(246, 248, 250, 1);
    border-radius: 1em;
    overflow: hidden;
    border-top-left-radius: 1em;
    border-bottom-left-radius: 1em;
    overflow: hidden;
}

.nitem-2:hover .ana__thumb-img {
    transform: scale(1.05);
    transition: transform 0.3s linear;
}

.call-cta-minah {
    width: 100%;
    height: auto;
    aspect-ratio: 284 / 641;
    position: relative;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: linear-gradient(180deg, rgba(253, 239, 110, 1) 0%, rgba(253, 255, 220, 1) 90%);
    overflow: hidden;
    border-radius: 1em;
}

.call-cta-minah__img {
    position: absolute;
    bottom: 0;
    width: 100%;
    aspect-ratio: 384 / 587;
}

.call-cta-minah__img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.call-cta-minah-text h3 {
    font-size: 1.5em;
    color: rgb(0, 0, 0);
}

.call-cta__btn {
    display: flex;
    align-items: center;
    justify-content: center;
}

.call-cta-minah__btn:hover {
    transform: translateY(-2px);
    transition: all 250ms ease-in-out;
}

.btn-primary-minah {
    background: rgba(0, 149, 133, 1);
    color: #ffffff;
    border: 1px solid white;
    transition: all 250ms ease-in-out;
    padding: 0.5em 1.3em;
    border-radius: 3em;
    font-weight: 500;
    font-size: 0.7em;
    min-width: 58%;
}

.btn-primary-minah:hover {
    background-color: #4279c4;
    transform: translateY(-2px);
}

.menu-line-2 {
    position: relative;
    height: 1px;
    background-color: #D0D0D0;
    width: 70%;
    margin-top: 1em;
    bottom: 0;
    left: 0;
}

.btn-show-more-2 {
    background: none;
    border: none;
    cursor: pointer;
    transition: transform 0.3s;
    width: 3.6em;
    aspect-ratio: 1;
    margin-top: 1em;
}

/* Responsive */
@media (max-width: 768px) {
    .ana-main {
        margin-top: 7vw;
    }

    .ana-main__wrapper {
        margin-bottom: 3em;
        gap: 3em;
        display: flex;
        flex-direction: column-reverse;
    }

    .ana-main__wrapper .wrap__right {
        width: 100%;
        height: fit-content;
        position: relative;
        margin-top: 2vw;
    }

    .call-cta-minah {
        width: 100%;
        height: 22vw;
        aspect-ratio: unset;
        position: relative;
        border-radius: 2em;
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        align-items: flex-end;
        overflow: visible;
    }

    .call-cta-minah__img {
        position: relative;
        transform: scale(1);
        transform-origin: bottom center;
        bottom: 0;
        width: 42%;
        aspect-ratio: 370 / 243;
    }

    .call-cta-minah .call-cta__wrapper {
        font-size: 2vw;
    }
}

@media (max-width: 575px) {
    .call-cta-minah .call-cta__wrapper {
        font-size: 2.5vw;
    }

    .call-cta-minah__btn {
        font-size: 1.2em;
    }
}