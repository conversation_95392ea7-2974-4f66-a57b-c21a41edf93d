@media (max-width: 767px) {   
    .post-detail-header {
        padding: 0 1.3rem;
    }
    .container-detail{
        font-size: 0.925rem;
    }
    .container, .container-fluid {
        width: 100%;
        padding-right: 1.25rem;
        padding-left: 1.25rem;
        margin-right: auto;
        margin-left: auto;
    }
    .breadcrumb-item{
        font-size: 1em;
    }
    .title-detail {
        font-size: 4.5vw;
    }

    .detail-note{
        font-size:  clamp(6px, 1.37vw, 8px);        
    }
    .detail-des{
        font-size: clamp(12px, 2.72vw, 13px);   
        font-weight: 300;     
        margin-bottom: 1.5rem;
    }

    .content-detail{   
        font-size:0.92rem;   
        padding: 0;
    }

    .container-detail > ul, .container-detail >p, .container-detail > h1, .container-detail > h2, .container-detail > h3{
        padding: 0 1.3rem;
    }

    .content-detail ul, .content-detail ol {
        padding-left: 3em;
        margin-bottom: 1em;
    }

    .table-of-conent-wrapper{
        padding: 2em;
    }

    .table-of-conent-list{
        font-size: 0.925em;
    }

    .article-meta {
        gap: 1em;
    }

    .article-meta-item {
        font-size: 1em;
    }
    .article-meta-icon {
        width: 2em;
        height: 2em;
    }

    .detail-mores {
        font-size: 0.925rem;
        padding: 0;
    }
    .detail-mores-title {
        font-size: 1.3em;
    }

    .detail-mores-list > li{
        font-size: clamp(10px, 2.6vw, 12px);
    }

    .banner-mino{
        font-size: 2.4vw;
        margin:0;
        padding: 2em 1em 0 2em;
    }
    .banner-mino::before{
        top: 7vw;
    }

    .banner-mino-title--small{
        font-size: 2.3vw;
    }

    .banner-mino-title{
        font-size: 3.8vw;
    }

    .banner-mino-content{       
        padding-bottom: 1.7vw;
    }   
    .banner-mino-btns{
        gap:0.5em;
    }
    .banner-mino-btn{
        font-size: clamp(6px, 1.7vw, 10px);
        padding: unset;
        width: clamp(100px, 25vw, 300px);
        padding-top: 1vw;
        padding-bottom: 1vw;
    }

    .banner-mino-btn span{
        font-size: clamp(5px, 1.7vw, 11px);
    }

    .posts-related-list--grid4 {
        grid-template-columns: 1fr;
        margin-top: 2em;
        gap:1em;
    }

    .detail-reference{
        font-size: 0.925rem;
        padding: 0;
    }
    .detail-reference-list{
        padding-left: 1.7em;
    }

    /** posts related */
    .posts-related{
        padding: 0;
    }

    .posts-related-item{
        flex-direction: row;      
        gap:0;
    }
    .posts-related-item-img{
        width: 42%;
    }
    .posts-related-item-header{
        width: 68%;
        padding: 0.5em;
        gap:0.4em;
    }
    .posts-related-item-img img{
        border-top-right-radius:0;
        border-bottom-left-radius: 0.5em;
        border-top-left-radius: 0.5em;
    }
    .posts-related-item-title{
        font-size: clamp(10px, 2.72vw, 13px);
        font-weight: 500;
    }
    .posts-related-item-meta{
        display: none;
    }

    .posts-related-item-des{
        display: block;;
        font-size: clamp(8px, 2.28vw, 12px);
        height: 4.9em;

        display: -webkit-box;
        -webkit-line-clamp: 4;
        line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;    
    }
    .footer-hpv{
        padding-left: 0;
        padding-right: 0;
    }
    
}

@media (min-width: 768px) and (max-width: 991px){
    .banner-mino-title--small{
        font-size: 2vw;
    }

    .banner-mino-title{
        font-size: 3.5vw;
    }

    .banner-mino-content{       
        padding-bottom: 1.2vw;
    }

    .banner-mino-btn{
        font-size: 1.3vw;
        padding: unset;
        width: clamp(100px, 25vw, 300px);
        padding-top: 1vw;
        padding-bottom: 1vw;
    }

    .posts-related-list--grid4{
        gap:1em;
    }

    .posts-related-item-title {
        height: 3.5em;
        font-size: 1.1em;
    }
}

@media (max-width: 991px){
    .footer-content-text,.footer-policy-list,.footer-policy-list li +li::before{
        color: var(--color-white);
    }
    .footer-content-text{
        font-size: clamp(5px, 1.35vw, 12px);
        text-align: center;
    }
    .footer-policy-list{
        font-size: clamp(8px, 1.8vw, 12px);
    }
}

@media (max-width: 1099px){
    .fast-action-controls {
        display: none;
    }    

    .footer-hpv{
        padding: 2em 2em 80px 2em;
    }
}

/* Reponsive Section Question MOH */
@media (max-width: 575px) {
    .section-question-moh {
        font-size: 1.45vw;
        margin: 0 auto 2svh;
        padding: 0;
    }
    .section-question-moh.section-2 {
        padding: 0;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        /* aspect-ratio: 416 / 442; */
        width: 100%;
        margin: 0 auto;
        border-radius: 20px;

    }

    .group-question__left {
        font-size: 0.85em;
        width: 100%;
        height: 28svh;
        padding: 1em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        gap: 2svh;
        padding-top: 5svh;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: -15%;

    }
    .section-5 .group-question__left {
        width: 100%;
        padding: 1em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 3svh;
        padding-top: 7svh;
    }

    .group-question__right {
        position: relative;
        width: 100%;
        height: 40svh;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .group-question-bg picture {
        height: inherit;
        object-fit: cover;
        width: 100%;
    }

    .group-tick {
        font-size: 1.1em;
    }

    .line-tick .line-moh {
        width: 80em;
        aspect-ratio: 1676 / 438;
        left: -5em;
        position: relative;
        top: -7em;
    }

    .line-tick .hand-img {
        position: relative;
        top: -4em;
        left: -6em;
        width: 20em;
        aspect-ratio: 290 / 188;
    }

    .group-question__content {
        gap: 5svh;
        top: 45%;
    }

    .group-question__btn {
        margin-top: 0;
        font-size: 1.2em;
    }
}

@media (max-width: 374px) {
    .section-question-moh {
        font-size: 0.6rem !important;
    }

    .line-tick .hand-img {
        position: relative;
        top: -2em;
        left: -17em;
        width: 22em;
        aspect-ratio: 290 / 188;
    }

    .group-tick {
        font-size: 1.1em;
    }
}

@media (min-width: 575px) and (max-width: 769px) {

    .group-question__content {
        gap: 5svh;
        font-size: 1.2em;
    }

    .bottom-kv-moh {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
    }

    .tagline-moh {
        width: 83.2em;
        margin-top: -0;
        aspect-ratio: 2144 / 868;
    }

    .section-question-moh {
        font-size: 1.15vw;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        aspect-ratio: 416 / 442;
        width: 90%;
        margin: 0 auto;
    }

    .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        font-size: 1em;
        padding-top: 10svh;
    }
    .section-5 .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .group-question__right {
        position: relative;
        margin-bottom: -14%;
    }

    .group-tick {
        font-size: 1em;
    }
    .line-tick .line-moh {
        width: 114em;
        aspect-ratio: 1986 / 464;
        left: 3em;
        position: relative;
        top: -11em;
        clip-path: inset(0 100% 0 0);
    }
    .line-tick .hand-img {
        position: relative;
        top: -3.5em;
        width: 26em;
        left: -24em;
        aspect-ratio: 290 / 188;
        opacity: 0;
        transition: opacity 0.5s ease -in-out 0.8s;
    }

    .line-tick {
        width: max-content;
    }

    .group-question__content {
        gap: 5svh;
    }
}

@media only screen and (min-width: 769px) and (max-width: 992px) {
    .section-question-moh {
        font-size: 1rem;
    }

    .group-question {
        display: flex;
        flex-direction:column-reverse;
        height: auto;
        aspect-ratio: 416 / 442;
        width: 80%;
        margin: 0 auto;
    }

    .group-question__disc {
        font-weight: 500;
        line-height: 1.1;
        color: var(--neutral-900);
        text-align: center;
    }

    .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }
    .section-5 .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .group-question__right {
        position: relative;
        margin-bottom: -14%;
    }

    .group-tick {
        margin-top: 3em;
        font-size: 0.9em;
    }
    .line-tick .line-moh {
        width: 114em;
        aspect-ratio: 1986 / 464;
        left: -1em;
        position: relative;
        top: -11em;
        clip-path: inset(0 100% 0 0);
    }
    .line-tick .hand-img {
        position: relative;
        top: -4.5em;
        width: 29em;
        left: -20em;
        aspect-ratio: 290 / 188;
        opacity: 0;
        transition: opacity 0.5s ease -in-out 0.8s;
    }

    .line-tick {
        width: max-content;
        font-size: 0.8em;
    }

    .group-question__content {
        gap: 5svh;
    }
}

@media only screen and (min-width: 991px) and (max-width: 1199px) {
    .section-question-moh {
        font-size: 0.45vw;
    }
    .group-question__left {
        width: 65%;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .section-question-moh {
        font-size: 0.37vw;
    }
    .group-question__left {
        width: 60%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .section-question-moh {
        font-size: 0.35vw;
    }
    .group-question__left {
        width: 61%;
    }
}
/* END - Reponsive Section Question MOH */