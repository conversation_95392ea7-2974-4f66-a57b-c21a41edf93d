function getCookie(name) {
    let cookieValue = document.cookie.replace(
        /(?:(?:^|.*;\s*)username\s*\=\s*([^;]*).*$)|^.*$/,
        "$1"
    );

    if (document.cookie && document.cookie !== "") {
        var cookies = document.cookie.split(";");
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === name + "=") {
                cookieValue = decodeURIComponent(
                    cookie.substring(name.length + 1)
                );
                break;
            }
        }
    }
    return cookieValue;
}
function getUser() {
    if (getCookie("_token") !== "") {
        return getCookie("_token");
    } else if (getCookie("anonymous_id") !== "") {
        return getCookie("anonymous_id");
    } else {
        return "";
    }

    // return localStorage.getItem('token');
}
async function trackingButton(type) {
    const formData = new FormData();
    const token = getUser();
    if (!token) return;
    formData.append("token", token);
    formData.append("type", type);
    try {
        fetch(`${window.location.origin}/api/WEB/trackingButton.php`, {
            method: "POST",
            headers: {
                Authorization: `Bearer ${token}`,
            },
            body: formData,
        });
    } catch (e) {}
}

function btnChatWithUs() {
    const btn = document.querySelector(".btn-chat-with-us");
    btn.addEventListener("click", () => {
        // Your code here
    });
}

const actionsData = [
    {
        id: 1,
        key: "chat-with-us",
        path: ".fast-action-controls #btn-mess-id",
        methodEvent() {
            trackingButton(2);
        },
    },
    {
        id: 2,
        key: "consulting-point",
        path: ".fast-action-controls #location-id",
        methodEvent() {
            trackingButton(1);
        },
    },
    {
        id: 3,
        key: "chat-with-us",
        path: ".fast-action-controls #btn-location-round-id",
        methodEvent(event) {

            try {
                trackingButton(3);
            } catch (e) {}
            const popover = document.querySelector(
                ".modal-locations#modal-locations"
            );

            if (!popover) {
                // createModalLocations();
            } else {
                event.stopPropagation();
                popover.style.display = "flex";
                setTimeout(() => {
                    document.addEventListener(
                        "click",
                        handleCloseModalLocations
                    );
                    popover.classList.add("active");
                }, 350);
            }
        },
    },
];
function addEventClickForFastActionControls() {
    actionsData.forEach((action) => {
        const btn = document.querySelector(action.path);
        if (btn) btn.addEventListener("click", action.methodEvent);
    });
    // Add more event listeners here as needed.
}

//----------------------------------------------------------------
// *** CREATE POPOVER LOCATIONS ***
//----------------------------------------------------------------
var timerCountDown = null;
function clearTimerCountDown() {
    clearInterval(timerCountDown);
    timerCountDown = null;
}

// Event click location item
const handleClickLocation = (event, link) => {
    handleCloseModalLocations(event);

    const popoverDirect = createPopoverModalDelayChangePage();
    const elClickToHere = popoverDirect.querySelector("a#link-redirect");

    if (elClickToHere) elClickToHere.href = link;

    let timeRemaining = 4;
    const eleNumberRemaining = document.querySelector(
        "#popover-modal-delay-change-page #count-down"
    );
    if (!eleNumberRemaining) return;

    timerCountDown = setInterval(() => {
        eleNumberRemaining.textContent = timeRemaining--;
        if (timeRemaining < 0) {
            // clearTimerCountDown();
            clearInterval(timerCountDown);
            window.open(link, "_blank"); // Open in a new tab
        }
    }, 1000);
};

// Add event click outside popover
const handleCloseModalLocations = (event) => {
    if (event) event.stopPropagation();
    const elPopoverLocations = document.querySelector(
        ".modal-locations#modal-locations"
    );

    if (!elPopoverLocations) return;

    elPopoverLocations.classList.remove("active");
    setTimeout(() => {
        elPopoverLocations.style.display = "none";
    }, 350);

    // Remove the event listener
    document.removeEventListener("click", handleCloseModalLocations);

    // Handle timerCountDown if applicable
    if (timerCountDown) {
        clearInterval(timerCountDown); // Use clearInterval instead of setInterval
    }
};

function addEventClickLocationItem() {
    const modalLocations = document.querySelector(
        ".modal-locations#modal-locations"
    );

    if (modalLocations) {
        const btnCloseModal = modalLocations.querySelector(".btn-close-modal");
        btnCloseModal.onclick = handleCloseModalLocations;

        const locationItems = modalLocations.querySelectorAll(".location-item");
        for (let i = 0; i < locationItems.length; i++) {
            locationItems[i].addEventListener("click", (event) => {
                const link = locationItems[i].dataset.link;
                handleClickLocation(event, link);
            });
        }
    }
}

window.addEventListener("DOMContentLoaded", () => {
    addEventClickLocationItem();
    addEventClickForFastActionControls();
});

//----------------------------------------------------------------
// *** CREATE POPOVER MODAL CHANGE PAGE ***
//----------------------------------------------------------------

function createPopoverModalDelayChangePage() {
    const popoverLocations = document.querySelector(
        ".popover-modal-delay-change-page#popover-modal-delay-change-page"
    );

    const elPopoverContent = popoverLocations.querySelector(".popover-content");

    if (!elPopoverContent) return console.log("Not found element!");

    popoverLocations.style.display = "block";
    // Delay run animate
    setTimeout(() => {
        popoverLocations.classList.add("active");
    }, 240);

    // Add event click outside popover
    const handleClosePopover = (event) => {
        if (elPopoverContent.contains(event.target)) return;
        popoverLocations.classList.remove("active");
        setTimeout(() => {
            popoverLocations.style.display = "none";
            // document.body.removeChild(elPopover);
        }, 240);
        clearTimerCountDown();
        document.removeEventListener("click", handleClosePopover);
    };

    document.addEventListener("click", handleClosePopover);

    return popoverLocations;
}
