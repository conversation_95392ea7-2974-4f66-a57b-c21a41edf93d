var posts = [
    {
        id: 1,
        title: "CÁC CON ĐƯỜNG LÂY NHIỄM CỦA HPV Ở CẢ NAM VÀ NỮ",
        des: "HPV (Human Papillomavirus) là một loại vi rút gây u nhú....",
        avatar: "./asset/images/common/avatar-post.png",
        link: "#",
    },
    {
        id: 2,
        title: "CÁC CON ĐƯỜNG LÂY NHIỄM CỦA HPV Ở CẢ NAM VÀ NỮ",
        des: "HPV (Human Papillomavirus) là một loại vi rút gây u nhú....",
        avatar: "./asset/images/common/avatar-post.png",
        link: "#",
    },
    {
        id: 3,
        title: "CÁC CON ĐƯỜNG LÂY NHIỄM CỦA HPV Ở CẢ NAM VÀ NỮ",
        des: "HPV (Human Papillomavirus) là một loại vi rút gây u nhú....",
        avatar: "./asset/images/common/avatar-post.png",
        link: "#",
    },
    {
        id: 4,
        title: "CÁC CON ĐƯỜNG LÂY NHIỄM CỦA HPV Ở CẢ NAM VÀ NỮ",
        des: "HPV (Human Papillomavirus) là một loại vi rút gây u nhú....",
        avatar: "./asset/images/common/avatar-post.png",
        link: "#",
    },
    {
        id: 5,
        title: "CÁC CON ĐƯỜNG LÂY NHIỄM CỦA HPV Ở CẢ NAM VÀ NỮ",
        des: "HPV (Human Papillomavirus) là một loại vi rút gây u nhú....",
        avatar: "./asset/images/common/avatar-post.png",
        link: "#",
    },
];

function renderListPost() {
    const postListEl = document.querySelector(".slide-posts .posts#post-list .swiper-wrapper");
    if (!postListEl) return;

    posts.forEach((post) => {
        const item = document.createElement("div");
        item.className = "swiper-slide post-item box-shadow-sm";
        const contentCardItem = `
               <div class="post-item__title bg-secondary">
                  <span> ${post.title}</span>
               </div>
               <div class="post-item__avatar">
                  <img src="${post.avatar}" alt="  ${post.title}">
               </div>
               <div class="post-item__des">
                    ${post.des}
               </div>
               <div class="post-item__action">
                  <a href="${post.link}" target="_blank" rel="noopener noreferrer">Tìm hiểu thêm</a>
               </div>
            `;
        item.innerHTML = contentCardItem;
        console.log("🚀 ~ posts.forEach ~ item:", item);
        postListEl.appendChild(item);
    });
}

window.addEventListener("DOMContentLoaded", () => {
    renderListPost();
});
