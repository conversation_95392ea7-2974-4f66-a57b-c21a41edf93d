//----------------------------------------------------------------
// *** MENU DESKTOP ***
//----------------------------------------------------------------

function getLink(type, value) {
    switch (type) {
        case "image":
            return `${window.location.protocol}//cms.${window.location.hostname}/api/CMS/image/folder/${value}`;

        case "link-post":
            return `${window.location.origin}/${
                value.url_category ? `${value.url_category}/` : ""
            }${value.url}`;
    }
}

function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);

    if (parts.length === 2) {
        return parts.pop().split(";").shift();
    }

    return null;
}

//----------------------------------------------------------------
// *** SEARCH CONTROL ***
//----------------------------------------------------------------
function addEventForButtonSearch() {
    const elBtn = document.querySelector(".nav-menu #search-control");

    if (!elBtn) return console.log("Not found element!");

    elBtn.addEventListener("click", (e) => {
        e.stopPropagation();
        activeModalSearch();
        addEventForBtnSearchControl();
    });

    document.addEventListener("click", (event) => {
        if (elBtn.contains(event.target)) return;

        const containerSearch = document.querySelector(
            "#modal-search.modal-search .search-control"
        );
    });
}

function addEventForBtnSearchControl() {
    const containerSearch = document.querySelector(
        "#modal-search.modal-search"
    );

    if (!containerSearch) return console.log("Not found element!");

    const btnSearch = containerSearch.querySelector(".search-control .search");
    const input = containerSearch.querySelector(".input-control");

    if (!btnSearch || !input) return;

    //Render 6 most viewed articles default
    handleSearch("", containerSearch);

    if (!input.dataset.keyDownEnterEventAdded) {
        input.addEventListener("keydown", (event) => {
            if (event.key === "Enter" || event.keyCode === 13) {
                event.preventDefault();
                handleSearch(input.value, containerSearch);
            }
        });
        input.dataset.keyDownEnterEventAdded = true;
    }
    if (!btnSearch.dataset.clickEventAdded) {
        btnSearch.addEventListener("click", (event) => {
            event.preventDefault();
            handleSearch(input.value, containerSearch);
        });
        btnSearch.dataset.clickEventAdded = true;
    }
}

function activeModalSearch() {
    const popupSearchEle = document.querySelector(".modal-search#modal-search");
    if (!popupSearchEle) return;

    popupSearchEle.classList.add("active");

    const searchContent = popupSearchEle.querySelector(".search-content");

    function eventClickOutSide(event) {
        if (searchContent.contains(event.target)) return;

        popupSearchEle.classList.remove("active");
        document.removeEventListener("click", eventClickOutSide);
    }

    document.addEventListener("click", eventClickOutSide);

    const elBtnCloseModal = popupSearchEle.querySelector(".close-modal");
    if (elBtnCloseModal) {
        elBtnCloseModal.addEventListener("click", () => {
            popupSearchEle.style.display = "block";
            popupSearchEle.classList.remove("active");
            setTimeout(() => {
                popupSearchEle.style.display = "none";
                document.removeEventListener("click", eventClickOutSide);
            }, 300);
        });
    }
}

async function handleSearch(inputValue, containerSearch) {
    const titleResultBox = containerSearch.querySelector(
        ".results-search .title"
    );
    const resultsContent = containerSearch.querySelector(
        ".results-search .list-results"
    );
    const searchControl = containerSearch.querySelector(".search-control");

    if (
        !titleResultBox ||
        !resultsContent ||
        searchControl.dataset.isSearching
    ) {
        return;
    }

    searchControl.dataset.isSearching = "loading";
    titleResultBox.textContent = "Kết quả tìm kiếm";
    resultsContent.innerHTML = `<div class="msd-loader-container">
               <div class="msd-loader"></div>
               <div class="msd-loader-text">Tìm kiếm...</div>
            </div>`;
    const resultData = await requestSearchArticles(inputValue);
    searchControl.dataset.isSearching = "";
    if (!resultData) {
        resultsContent.innerHTML = `<div class="not-found-post">Không tìm thấy bài viết!</div>`;
    } else {
        const listPostHTML = resultData.map((post) => {
            const { title, img, img_mobile, url, url_category } = post;
            return `
            <article class="result-item" title="${title}">
                <a class="result-item__avatar" href="javascript:void(0)" title="${title}">
                    <picture alt="${title}">
                        <source srcset="${getLink(
                            "image",
                            img_mobile
                        )}" media="(max-width: 992px)">      
                        <source srcset="${getLink(
                            "image",
                            img
                        )}" media="(min-width: 991px)">
                        <img src="${getLink("image", img)}" alt="${title}">
                    </picture>
                </a>
                <p class="result-item__des">
                    <a href="javascript:void(0)" title="${title}">
                        ${title}
                    </a>
                </p>
            </article>
           `;
        });
        resultsContent.innerHTML = listPostHTML.join("");
    }
}

function requestSearchArticles(value) {
    return fetch(`${window.location.origin}/apis/v1/news`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-Token": getCookie("csrf_"),
        },
        body: JSON.stringify({ search: value }),
    })
        .then((response) => response.json())
        .then((data) => data?.data || [])
        .catch(() => null);
}

function removePopupSearch() {
    const popupSearchEle = document.querySelector("#popup-search");
    if (popupSearchEle) document.body.removeChild(popupSearchEle);
}

setTimeout(() => {
    addEventForButtonSearch();
}, 1000);

//----------------------------------------------------------------
// *** MENU MOBILE ***
//----------------------------------------------------------------

//Menu mobile
function addEventToggleMenuMobile() {
    const elBtn = document.querySelector(".btn-open-menu-mobile");
    const elModalMenu = document.querySelector(
        ".popup-menu-mobile#popup-menu-mobile"
    );

    if (!elBtn) return console.log("Not found element!");

    elBtn.addEventListener("click", (e) => {
        // createMenuMobile();
        elModalMenu.style.display = "block";
    });

    document.addEventListener("click", (event) => {
        if (elBtn.contains(event.target)) return;

        const containerSearch = document.querySelector(
            "#popup-menu-mobile #container-menu-mobile"
        );
        if (!containerSearch?.contains(event.target)) {
            elModalMenu.style.display = "none";
            // removePopupMenuMobile();
        }
    });
}

//----------------------------------------------------------------
// *** DETECT FIXED MENU SCROLL ***
//----------------------------------------------------------------

function addEventDetectMenuStuck() {
    document.addEventListener("scroll", () => {
        const stickyElement = document.querySelector("header.menu-header#menu");

        if (!stickyElement) {
            return;
        }
        
        const container = stickyElement.parentElement;

        const isStuck =
            stickyElement.getBoundingClientRect().top <= 20 &&
            container.getBoundingClientRect().top < 0;

        if (isStuck) {
            stickyElement.classList.add("is-sticky");
        } else {
            stickyElement.classList.remove("is-sticky");
        }
    });
}

function addTheTradeDeskTrackingTagForButtonFindClinic() {
    const btnFindClinicDesktopEl = document.querySelector(
        "#btn-location-id-desktop"
    );
    const btnFindClinicMobileEl = document.querySelector("#btn-location-id");
    const btnFindClinicMenuMobileEl = document.querySelector(
        "#menu-mobile-btn-location-id"
    );
    const btnFindClinicFastControlEl = document.querySelector("#location-id");

    function fireTTDPixel() {
        (function () {
            var ttdPixel = document.createElement("script");
            ttdPixel.src =
                "https://insight.adsrvr.org/track/pxl/?adv=3dc1drw&ct=0:kqbhzhm&fmt=3";             // "https://insight.adsrvr.org/track/pxl/?adv=3dc1drw&ct=0:kqbhzhm&fmt=3";
            document.body.appendChild(ttdPixel);
        })();
    }

    [
        btnFindClinicDesktopEl,
        btnFindClinicMobileEl,
        btnFindClinicMenuMobileEl,
        btnFindClinicFastControlEl,
    ].forEach((btn) => {
        if (btn) {
            btn.addEventListener("click", fireTTDPixel);
        }
    });

    // if (btnFindClinicDesktopEl) {
    //     btnFindClinicDesktopEl.addEventListener("click", fireTTDPixel);
    // }
    // if (btnFindClinicDesktopEl) {
    //     btnFindClinicDesktopEl.addEventListener("click", fireTTDPixel);
    // }
    // if (btnFindClinicMobileEl) {
    //     btnFindClinicMobileEl.addEventListener("click", fireTTDPixel);
    // }
    // if (btnFindClinicFastControlEl) {
    //     btnFindClinicFastControlEl.addEventListener("click", fireTTDPixel);
    // }
}
document.addEventListener("DOMContentLoaded", () => {
  addEventDetectMenuStuck();
  addEventToggleMenuMobile();
  addTheTradeDeskTrackingTagForButtonFindClinic();

  // Open tab menu for mobile and tablet
  const menuItems = document.querySelectorAll(".click-mobile");

  menuItems.forEach((menuItem) => {
    const submenu = menuItem.querySelector(".child-menu-mobile");

    menuItem.addEventListener("click", function (event) {
      if (event.target.tagName === "A") {
        return;
      }
      event.preventDefault();

      menuItems.forEach((item) => {
        if (item !== menuItem) {
          const otherSubmenu = item.querySelector(".child-menu-mobile");
          if (otherSubmenu && otherSubmenu.classList.contains("open")) {
            otherSubmenu.classList.remove("open");
          }
        }
      });

      if (submenu) {
        submenu.classList.toggle("open");
      }
    });
  });
});