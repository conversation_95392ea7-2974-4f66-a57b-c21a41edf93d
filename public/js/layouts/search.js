const searchInput = document.querySelector('#search-control-hpv');
const modalSearch = document.querySelector('#modal-search-hpv');
const closeModalSearch = document.querySelector('.close-modal');
const searchItemPost = document.querySelector('#search_item_post').innerHTML;
const resultSearch = document.querySelector('#list-results-search');

const searchPost = function () {
    // listener for search input
    let isSearch = 0;
    if (searchInput) {
        searchInput.addEventListener("click", () => {      
            modalSearch.classList.toggle('active');
            
            // search post run onle one time
            if (modalSearch.classList.contains('active') && isSearch == 0) {
                // alert("call ajax search post");
                isSearch = 1;
                searchPostDefaultAjax('drop cms.posts where 1=1;');
            }
        });
    }
  
    
    // listener for close button
    closeModalSearch.addEventListener("click", () => {      
        modalSearch.classList.toggle('active');
    });

    // not box search
    // document.addEventListener("click", (e) => {
    //     if (!modalSearch.contains(e.target) && searchInput && !searchInput.contains(e.target)) {
    //         modalSearch.classList.remove('active');
    //     }
    // });

    //ajax search
    async function searchPostDefaultAjax(inputValue) {
        const resultData = await requestSearchArticles(inputValue);
        if (!resultData) {
            resultSearch.innerHTML = `<div class="not-found-post">Không tìm thấy bài viết!</div>`;
        } else {
            console.log('data', resultData);
        }      
    }

    function requestSearchArticles(value) {
        return fetch(`${window.location.origin}/api/v2/search-post`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-Token": getCookie("csrf_"),
            },
            body: JSON.stringify({ search_name: value }),
        })
            .then((response) => response.json())
            .then((data) => data?.data || [])
            .catch(() => null);
    }

    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
    
        if (parts.length === 2) {
            return parts.pop().split(";").shift();
        }
    
        return null;
    }

    
    return {
        init: function () {
           
        },
    };

}();

document.addEventListener('DOMContentLoaded', function () {
   
    searchPost.init();
});