const baseApiUrl = `${window.location.origin}/apis/v1/`;
// const baseApiUrl = "http://127.0.0.1:4005/apis/v1/";
const baseImgUrl = "https://cms.hpv.vn/api/CMS/image/folder/";

const apiRequest = {
    async getDistrict(idProvince) {
        return fetch(`${baseApiUrl}district`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-Token": getCookie("csrf_"),
            },
            body: JSON.stringify({ province_id: idProvince }),
        })
            .then((response) => {
                return response.json();
            })
            .catch(() => {
                return null;
            });
    },
    async getProvinces() {
        return fetch(`${baseApiUrl}province`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-Token": getCookie("csrf_"),
            },
            body: JSON.stringify({}),
        })
            .then((response) => {
                return response.json();
            })
            .then((data) => data.data)
            .catch(() => {
                return null;
            });
    },
    async getListConsultantLocation(query) {
        return fetch(`${baseApiUrl}consultant-locations`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-Token": getCookie("csrf_"),
            },
            body: JSON.stringify(query),
        })
            .then((response) => {
                return response.json();
            })
            .then((data) => data)
            .catch(() => {
                apiRequest.isGetFullLocation = true;
                return null;
            });
    },
    async getConsultantLocationDetails(idLocation) {
        return fetch(`${baseApiUrl}consultant-location`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-Token": getCookie("csrf_"),
            },
            body: JSON.stringify({ consult_location_id: idLocation }),
        })
            .then((response) => {
                return response.json();
            })
            .then((data) => data)
            .catch(() => {
                return null;
            });
    },
    loading: {
        searchConsultantLocation: false,
        findConsultantLocation: false,
    },
    isGetFullLocation: false,
};

const domElements = {
    formSearch: {
        form() {
            return document.querySelector("form.form-input");
        },
        provinceControl() {
            return this.form().querySelector("#city");
        },
        provinceSelectedValue() {
            return this.form().querySelector("#city .select-value");
        },
        provinceInput() {
            return this.form().querySelector("#city input");
        },
        provinceDropdownMenu() {
            return this.form().querySelector("#city .dropdown-menu");
        },
        address() {
            return this.form().querySelector("input[name='address']");
        },
    },
    listConsultant: {
        container: function () {
            return document.querySelector(
                ".consultant-locations-list#consultant-locations-list"
            );
        },
        loading() {
            return this.container().querySelector(".loading");
        },
        emptyState() {
            return this.container().querySelector(".empty-state");
        },
        listLocation() {
            return this.container().querySelector(".consultant-locations");
        },
    },
    googleMap: {
        container: function () {
            return document.querySelector(".google-map#google-map");
        },
        iframe() {
            return this.container().querySelector("iframe");
        },
        loading() {
            return this.container().querySelector(".loading");
        },
    },
    infoLocationDetails: {
        container() {
            return document.querySelector(
                ".locations-details#locations-details"
            );
        },
        locationThumb() {
            return this.container().querySelector(".thumb img");
        },
        locationName() {
            return this.container().querySelector(".location-details__name");
        },
        addressText() {
            return this.container().querySelector(".address__text");
        },
        phoneNumber() {
            return this.container().querySelector(".phone-number__text");
        },
        workingHour() {
            return this.container().querySelector(".working-hour__text");
        },
        btnDirection() {
            return this.container().querySelector(".btn-directions");
        },
        btnConsultNow() {
            return this.container().querySelector(".btn-consult-now");
        },
    },
    viewPortChange() {
        if (window.innerWidth < 1100) {
            domElements.infoLocationDetails.container().classList.add("mobile");
        } else {
            domElements.infoLocationDetails
                .container()
                .classList.remove("mobile");
        }
        window.addEventListener("resize", (e) => {
            if (e.innerWidth < 1100) {
                domElements.infoLocationDetails
                    .container()
                    .classList.add("mobile");
            } else {
                domElements.infoLocationDetails
                    .container()
                    .classList.remove("mobile");
            }
        });
    },

    // infoLocationDetailsMobile: {
    //     container() {
    //         return document.querySelector(
    //             ".locations-details#locations-details.mobile"
    //         );
    //     },
    //     locationThumb() {
    //         return this.container().querySelector(".thumb img");
    //     },
    //     locationName() {
    //         return this.container().querySelector(".location-details__name");
    //     },
    //     addressText() {
    //         return this.container().querySelector(".address__text");
    //     },
    //     phoneNumber() {
    //         return this.container().querySelector(".phone-number__text");
    //     },
    //     workingHour() {
    //         return this.container().querySelector(".working-hour__text");
    //     },
    //     btnDirection() {
    //         return this.container().querySelector(".btn-directions");
    //     },
    //     btnConsultNow() {
    //         return this.container().querySelector(".btn-consult-now");
    //     },
    // },
    btnGoBack() {
        return document.querySelector(
            ".locations-details#locations-details .btn-go-back"
        );
    },
    modalConsultantLocationDetails() {
        return document.querySelector(".modal-consultant-location-details");
    },
    btnCloseModal() {
        return document.querySelector(".btn-close-modal");
    },
};

const components = {
    partner: {
        container() {
            return document.querySelector(
                ".consult-location-partner#consult-location-partner"
            );
        },
        addEventClickPartnerItem() {
            const partnerItems = this.container().querySelectorAll(
                ".location-list .location-item"
            );
            for (let i = 0; i < partnerItems.length; i++) {
                partnerItems[i].addEventListener("click", (event) => {
                    const link = partnerItems[i].dataset.link;
                    handleClickLocation(event, link);
                });
            }
        },
        init() {
            this.addEventClickPartnerItem();
        },
    },
};

function eventDropdowns(inputControl, form) {
    const dropdownToggle = inputControl.querySelector(".dropdown-toggle");
    const dropdownMenu = inputControl.querySelector(".dropdown-menu");

    dropdownToggle.addEventListener("click", function (e) {
        e.preventDefault();
        const allDropDownMenu = form.querySelectorAll(".dropdown-menu");
        allDropDownMenu.forEach((menu) => {
            if (menu !== dropdownMenu) {
                menu.style.display = "none";
            }
        });

        dropdownMenu.style.display =
            dropdownMenu.style.display !== "none" ? "none" : "flex";
    });
    document.addEventListener("click", function (e) {
        if (!e.target.closest(".dropdown")) {
            dropdownMenu.style.display = "none";
        }
    });
}

function renderDropDown(form, idDropdown, data, callBack) {
    const inputControl = form.querySelector(`#${idDropdown}`);
    const selectValue = inputControl.querySelector(".select-value");
    const inputCity = inputControl.querySelector(`input[name="${idDropdown}"]`);
    const cityList = inputControl.querySelector(`.dropdown-menu`);
    cityList.innerHTML = null;

    data.forEach((pr) => {
        const li = document.createElement("li");
        li.textContent = pr.title;
        li.dataset.value = pr.title;
        li.classList.add("dropdown-item");
        li.addEventListener("click", () => {
            selectValue.textContent = pr.title;
            inputCity.value = pr.id;
            cityList.style.display = "none";
            if (callBack) {
                callBack(pr.id);
            }
        });
        cityList.appendChild(li);
    });
}

async function updateDistrict(form, idProvince) {
    const districts = await apiRequest.getDistrict(idProvince);

    const selectValue = form.querySelector("#district .select-value");
    const inputCity = form.querySelector(`#district input[name="district"]`);

    selectValue.textContent = "Huyện/Quận";
    inputCity.value = 0;

    if (districts) {
        renderDropDown(form, "district", districts.data);
    } else {
        Toastify({
            text: "Lỗi không thể tải được danh sách Quận/Huyện.",
            className: "error msd",
            duration: 2500,
            close: true,
            gravity: "top", // `top` or `bottom`
            stopOnFocus: true,
        }).showToast();
    }
}

async function onClickLocationItem(idLocation) {
    const locationDetails = await apiRequest.getConsultantLocationDetails(
        idLocation
    );
    if (window.innerWidth <= 1099) {
        const modalConsultantLocationDetails =
            domElements.modalConsultantLocationDetails();
        if (modalConsultantLocationDetails) {
            modalConsultantLocationDetails.style.display = "flex";
            setTimeout(() => {
                modalConsultantLocationDetails.classList.add("active");
            }, 50);
        }
    }
    updateConsultantLocationDetails(locationDetails.data);
}

async function renderListConsultantLocation(locations, isPushMore = false) {
    const listConsultLocation = domElements.listConsultant.container();
    if (listConsultLocation) {
        const listEl = listConsultLocation.querySelector(
            ".consultant-locations"
        );
        if (!isPushMore) listEl.innerHTML = "";

        locations.forEach((l) => {
            const article = document.createElement("article");
            article.classList.add("location");
            article.dataset.id = l.id;
            article.addEventListener("click", () => onClickLocationItem(l.id));
            const html = `
                <div class="location__thumb">
                    <img src="${baseImgUrl}${l.img_mobile}" alt="${l.name}" />
                </div>
                <div class="location__info">
                    <h3 class="location__info__name">
                        ${l.name}
                    </h3>
                    <address class="location__info__address">
                        <svg class="address__icon" aria-hidden="true" viewBox="0 0 24 24">
                        <path fill="currentColor"
                            d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5">
                        </path>
                        </svg>
                        <p class="address__text">${l.address}</p>
                    </address>
                </div>
                `;
            article.innerHTML = html;
            listEl.appendChild(article);
        });
    }
}

function getPageOffsetListLocation() {
    const listConsultLocation = domElements.listConsultant.listLocation();
    if (listConsultLocation) {
        return parseInt(listConsultLocation.dataset.pageOffset);
    }
    return 0;
}
function setPageOffsetListLocation(value) {
    const listConsultLocation = domElements.listConsultant.listLocation();
    if (listConsultLocation) {
        listConsultLocation.dataset.pageOffset = value;
    }
}

async function handleFindConsultantLocation(form, isFindMore = false) {
    apiRequest.isGetFullLocation = false;

    const emptyState = domElements.listConsultant.emptyState();
    emptyState.style.display = "none";

    const fromData = form ? new FormData(form) : null;
    const pageOffset = isFindMore ? getPageOffsetListLocation() + 20 : 0;

    let payload;
    if (fromData) {
        payload = {
            lat: 0,
            lng: 0,
            province: fromData.get("city") ? parseInt(fromData.get("city")) : 0,
            district: fromData.get("district")
                ? parseInt(fromData.get("district"))
                : 0,
            name_or_address: fromData.get("address"),
            limit: 20,
            offset: pageOffset,
        };
    } else {
        payload = {
            lat: 0,
            lng: 0,
            province: 0,
            district: 0,
            name_or_address: "",
            limit: 20,
            offset: 0,
        };
    }

    const loading = domElements.listConsultant.loading();
    loading.style.display = "block";
    apiRequest.loading.findConsultantLocation = true;
    const listLocations = await apiRequest.getListConsultantLocation(payload);
    loading.style.display = "none";
    apiRequest.loading.findConsultantLocation = false;

    if (listLocations) {
        setPageOffsetListLocation(isFindMore ? pageOffset : 0);
        renderListConsultantLocation(listLocations.data, isFindMore);
        if (listLocations.data.length === 0) {
            emptyState.style.display = "block";
        }
    } else {
        if (!pageOffset) {
            renderListConsultantLocation([]);
            emptyState.style.display = "block";
        }
    }
}

function handleScrollListConsultantLocation() {
    if (apiRequest.isGetFullLocation) return;
    const locations = domElements.listConsultant
        .listLocation()
        .querySelectorAll(".location");
    const penultimateLocation = locations[locations.length - 4];
    if (
        penultimateLocation &&
        penultimateLocation.getBoundingClientRect().bottom <= window.innerHeight
    ) {
        handleFindConsultantLocation(domElements.formSearch.form(), true);
    }
}

function updateGoogleMap(locationData) {
    const googleMap = domElements.googleMap.container();
    if (googleMap) {
        if (locationData?.url_embed) {
            googleMap.innerHTML = `<div class="loading" style="display: block;">
                    <div class="msd-loader-container">
                        <div class="msd-loader"></div>
                    </div>
                </div> ${locationData.url_embed}`;
        } else {
            const urlEmbed = `https://maps.google.com/maps?q=${locationData.lat},${locationData.lng}&hl=vn&z=17&amp;output=embed`;
            googleMap.innerHTML = `
                <div class="loading" style="display: block;">
                    <div class="msd-loader-container">
                        <div class="msd-loader"></div>
                    </div>
                </div>
                <iframe
                src="${urlEmbed}"
                width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy"
                referrerpolicy="no-referrer-when-downgrade"></iframe>
            `;
        }

        const iframe = googleMap.querySelector("iframe");
        const loading = domElements.googleMap.loading();

        if (!iframe) {
            return (loading.style.display = "none");
        }

        loading.style.display = "block";
        iframe.addEventListener("load", () => {
            loading.style.display = "none";
        });
    }
}

async function updateConsultantLocationDetails(locationData) {
    const infoLocationDetails = domElements.infoLocationDetails;
    if (window.innerWidth >= 1100) {
        infoLocationDetails.container().style.display = "flex";
    }
    infoLocationDetails.locationThumb().src =
        baseImgUrl + locationData.img_mobile;
    infoLocationDetails.locationName().textContent = locationData.name;
    infoLocationDetails.addressText().textContent = locationData.address;
    infoLocationDetails.phoneNumber().textContent = locationData.phone;
    infoLocationDetails.workingHour().textContent = locationData.operation;
    infoLocationDetails.btnDirection().href =
        locationData?.dir_link ||
        `https://maps.google.com/maps?q=${locationData.lat},${locationData.lng}`;
    infoLocationDetails.btnDirection().title = locationData.name;

    // if (window.innerWidth < 1100) {
    //     const infoLocationDetailsMobile = domElements.infoLocationDetailsMobile;
    //     infoLocationDetailsMobile.locationThumb().src =
    //         baseImgUrl + locationData.img_mobile;
    //     infoLocationDetailsMobile.locationName().textContent =
    //         locationData.name;
    //     infoLocationDetailsMobile.addressText().textContent =
    //         locationData.address;
    //     infoLocationDetailsMobile.phoneNumber().textContent =
    //         locationData.phone;
    //     infoLocationDetailsMobile.workingHour().textContent =
    //         locationData.operation;
    //     infoLocationDetailsMobile.btnDirection().href =
    //         locationData?.dir_link ||
    //         `https://maps.google.com/maps?q=${locationData.lat},${locationData.lng}`;
    //     infoLocationDetailsMobile.btnDirection().title = locationData.name;
    // }

    updateGoogleMap(locationData);
}

function closeModalConsultantDetails() {
    const modalConsultantLocationDetails =
        domElements.modalConsultantLocationDetails();
    if (modalConsultantLocationDetails) {
        modalConsultantLocationDetails.classList.remove("active");
        setTimeout(() => {
            modalConsultantLocationDetails.style.display = "none";
        }, 300);
    }
}

function addEventSelectProvince(form) {
    const provinceDropdownMenu = domElements.formSearch.provinceDropdownMenu();
    const listMenuItem = provinceDropdownMenu.querySelectorAll("li");
    const selectValue = domElements.formSearch.provinceSelectedValue();
    const inputCity = domElements.formSearch.provinceInput();

    listMenuItem.forEach((item) => {
        item.addEventListener("click", () => {
            selectValue.textContent = item.dataset.value;
            inputCity.value = item.dataset.id;
            provinceDropdownMenu.style.display = "none";
            updateDistrict(form, parseInt(item.dataset.id));
        });
    });
}
function initAddEventClickLocation() {
    const locationList = domElements.listConsultant.listLocation();
    const listLocationItem = locationList.querySelectorAll("article");

    listLocationItem.forEach((item) => {
        item.addEventListener("click", () => {
            onClickLocationItem(parseInt(item.dataset.id));
        });
    });
}

async function initForm() {
    const form = domElements.formSearch.form();
    const inputAddress = domElements.formSearch.address();
    if (form) {
        form.addEventListener("submit", (e) => {
            e.preventDefault();
        });

        addEventSelectProvince(form);

        eventDropdowns(form.querySelector("#city"), form);
        eventDropdowns(form.querySelector("#district"), form);

        const btnSearch = form.querySelector("#btn-find-now");
        btnSearch.addEventListener("click", () =>
            handleFindConsultantLocation(form)
        );
    }
    if (inputAddress) {
        inputAddress.addEventListener("keydown", (e) => {
            if (e.key === "Enter" || e.key === 13) {
                handleFindConsultantLocation(form);
                e.preventDefault();
            }
        });
    }
}

function initListConsultantLocation() {
    domElements.viewPortChange();

    const btnGoBack = domElements.btnGoBack();
    const btnClose = domElements.btnCloseModal();
    const btnConsultNow = domElements.infoLocationDetails.btnConsultNow();
    if (btnGoBack && btnClose && btnConsultNow) {
        btnGoBack.addEventListener("click", closeModalConsultantDetails);
        btnClose.addEventListener("click", closeModalConsultantDetails);
        btnConsultNow.addEventListener("click", (e) => {
            e.preventDefault();
            const btnBooking = document.querySelector(
                ".fast-action-controls #btn-location-round-id"
            );
            closeModalConsultantDetails();
            setTimeout(() => {
                btnBooking.click();
            }, 10);
        });
    }

    const consultantLocation = domElements.listConsultant.container();
    consultantLocation.addEventListener("scroll", () => {
        if (apiRequest.loading.findConsultantLocation) return;
        handleScrollListConsultantLocation();
    });
    initAddEventClickLocation();
}

if (document.readyState !== "loading") {
    initForm();
    initListConsultantLocation();
    components.partner.init();
} else {
    document.addEventListener("DOMContentLoaded", function () {
        initForm();
        initListConsultantLocation();
        components.partner.init();
    });
}
