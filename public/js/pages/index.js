var ipDevice = null;
function getCookieVal(offset) {
    var endstr = document.cookie.indexOf(";", offset);
    if (endstr === -1) {
        endstr = document.cookie.length;
    }
    return unescape(document.cookie.substring(offset, endstr));
}
function getCookieTest(name) {
    var arg = name + "=";
    var alen = arg.length;
    var clen = document.cookie.length;
    var i = 0;
    while (i < clen) {
        var j = i + alen;
        if (document.cookie.substring(i, j) === arg) {
            return getCookieVal(j);
        }
        i = document.cookie.indexOf(" ", i) + 1;
        if (i === 0) break;
    }
    return "";
}
function getCookie(name) {
    let cookieValue = document.cookie.replace(
        /(?:(?:^|.*;\s*)username\s*\=\s*([^;]*).*$)|^.*$/,
        "$1"
    );

    if (document.cookie && document.cookie !== "") {
        var cookies = document.cookie.split(";");
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === name + "=") {
                cookieValue = decodeURIComponent(
                    cookie.substring(name.length + 1)
                );
                break;
            }
        }
    }
    return cookieValue;
}
function getUser() {
    if (getCookie("_token") !== "") {
        return getCookie("_token");
    } else if (getCookie("anonymous_id") !== "") {
        return getCookie("anonymous_id");
    } else {
        return "";
    }

    // return localStorage.getItem('token');
}
function randomNumber(length) {
    let result = "";
    const characters = "0123456789";
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
        result += characters.charAt(
            Math.floor(Math.random() * charactersLength)
        );
        counter += 1;
    }
    return result;
}

function randomString(length) {
    let result = "";
    const characters =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
        result += characters.charAt(
            Math.floor(Math.random() * charactersLength)
        );
        counter += 1;
    }
    return result;
}

const insertSession = async (token) => {
    const formData = new FormData();
    formData.append("token", token);
    try {
        return fetch(
            `${window.location.origin}/api/WEB/insertSessionCookie.php`,
            {
                method: "POST",
                body: formData,
            }
        );
    } catch (e) {}
};
async function getIpAddress() {
    return fetch("https://api.ipify.org?format=json")
        .then((response) => response.json())
        .then((data) => data.ip)
        .catch(() => null);
}

async function ProfileGetToken() {
    ipDevice = await getIpAddress();
    const formData = new FormData();
    formData.append("ip", ipDevice + new Date());
    if (!ipDevice) {
        return;
    }
    try {
        return fetch(`${window.location.origin}/api/WEB/token.php`, {
            //   return fetch(`http://localhost:8074/api/WEB/token.php`, {
            method: "POST",
            body: formData,
        })
            .then((response) => response.json())
            .then((data) => data || null)
            .catch(() => null);
    } catch (e) {}
}

async function ProfileCookieReport(params) {
    const formData = new FormData();

    for (const key in params) {
        if (params.hasOwnProperty(key)) {
            formData.append(key, params[key]);
        }
    }

    try {
        return fetch(`${window.location.origin}/api/WEB/cookieReport.php`, {
            method: "POST",
            body: formData,
        })
            .then((response) => response.json())
            .then((data) => data || null)
            .catch(() => null);
    } catch (e) {}
}

const CustomerToken = async () => {
    const result = await ProfileGetToken();
    if (result?.status === 1) {
        const params = {
            token: String(result.data),
            type: 3,
            ip: ipDevice || new Date(),
            country: undefined,
            city: undefined,
            custom: [],
            utmSource: undefined,
            utmMedium: undefined,
            utmCampaign: undefined,
            utmId: undefined,
            utmContent: undefined,
            device: undefined,
        };
        const resultCookie = await ProfileCookieReport(params);
        document.cookie = `anonymous_id=${result.data}; Max-Age=31536000`;

        document.cookie = `WMF-Last-Access=${new Date()}; Max-Age=2592000`;
        document.cookie = `cookietype=all; Max-Age=31536000`;

        document.cookie = `_gid=${randomString(25)}; Max-Age=86400`;
        document.cookie = `_gcl_au=1.1.${randomNumber(10)}.${randomNumber(
            10
        )}; Max-Age=7776000`;
        document.cookie = `_clck=${randomString(25)}; Max-Age=31536000`;
        document.cookie = `_clsk=${randomString(25)}; Max-Age=31536000`;
        document.cookie = `token=${randomString(25)}; Max-Age=31536000`;
        document.cookie = `_token=${result.data}; expires=${new Date()} Secure`;
        localStorage.setItem("token", result.data);
        insertSession(result.data);
    }
};

const getHBS = () => {
    if (getCookieTest("hpvquestion") !== "") {
        return getCookieTest("hpvquestion");
    } else {
        return "";
    }
};

async function requestReportHSB(answers, reportHSB) {
    const formData = new FormData();
    const token = getUser();
    if (!token) {
        const tk = reportHSB.querySelector(".thank-you");
        if (tk) {
            tk.style.display = "block";
            setTimeout(() => {
                tk.classList.add("active");
            }, 100);
        }
        setTimeout(() => {
            reportHSB.style.display = "none";
        }, 2000);
    }
    formData.append("token", token);
    formData.append("known", parseInt(answers.knownHpv));
    formData.append("plan", parseInt(answers.injectHpv));
    try {
        //   fetch(`http://localhost:8074/api/WEB/hbsReport.php`, {
        fetch(`${window.location.origin}/api/WEB/hbsReport.php`, {
            method: "POST",
            headers: {
                Authorization: `Bearer ${token}`,
            },
            body: formData,
        })
            .then(() => {
                document.cookie = `hpvquestion=${
                    String(answers.knownHpv) + String(answers.injectHpv)
                }; Max-Age=15768000`;
            })
            .finally(() => {
                const tk = reportHSB.querySelector(".thank-you");
                if (tk) {
                    tk.style.display = "block";
                    setTimeout(() => {
                        tk.classList.add("active");
                    }, 100);
                }
                setTimeout(() => {
                    reportHSB.style.display = "none";
                }, 2000);
            });
    } catch (e) {}
}

function checkReportHSB() {
    if (!getHBS()) {
        const reportHSB = document.querySelector(
            ".modal-start-hbs#modal-start-hbs"
        );

        if (reportHSB) {
            reportHSB.style.display = "flex";
            setTimeout(() => reportHSB.classList.add("active"), 2000);
            const answers = { knownHpv: undefined, injectHpv: undefined };
            const handleChange = (type, value) => {
                answers[type] = value;
                if (answers.knownHpv && answers.injectHpv) {
                    requestReportHSB(answers, reportHSB);
                }
            };

            reportHSB
                .querySelectorAll(
                    ".question-item#know-hpv input[name='know-hpv']"
                )
                .forEach((item) =>
                    item.addEventListener("change", () =>
                        handleChange("knownHpv", item.value)
                    )
                );

            reportHSB
                .querySelectorAll(
                    ".question-item#injected-hpv input[name='injected-hpv']"
                )
                .forEach((item) =>
                    item.addEventListener("change", () =>
                        handleChange("injectHpv", item.value)
                    )
                );
        }
    }
}

window.addEventListener("DOMContentLoaded", function () {
    let token = getUser();
    if (token.length > 0) {
        insertSession(token);
    } else {
        CustomerToken();
    }
    checkReportHSB();
});
