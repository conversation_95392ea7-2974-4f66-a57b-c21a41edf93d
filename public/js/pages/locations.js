const baseImgUrl = "https://cms.hpv.vn/api/CMS/image/folder/";
const articleEle = document.querySelectorAll(".location");
const provinceElement = document.getElementById("province");
const districtElement = document.getElementById("district");
const addressInput = document.getElementById("address").querySelector("input");
const btnFindNow = document.getElementById("btn-find-now");
const emptyState = document.querySelector(".empty-state");
const loading = document.querySelector(".loading");
const container = document.querySelector(".consultant-locations");

const urlSearchLocation = provinceElement.dataset.url + "/apis/v3/search-locations";

let dataSearch = {};

const locationSearch = function () {
  let isSearch = 0;

  btnFindNow.addEventListener("click", function () {
    if (isSearch === 1) {
      console.log("<PERSON>ang tìm kiếm, vui lòng đợi...");
      return;
    }

    let provinceId = provinceElement.dataset.id || null;
    let districtId = districtElement.dataset.id || null;
    let address = addressInput.value.trim() || null;

    if (!provinceId && !districtId && !address) return;

    isSearch = 1;
    loading.style.display = "block";
    emptyState.style.display = "none";
    container.innerHTML = ""; // Xóa các kết quả cũ

    const requestData = {};
    if (provinceId) requestData.province_id = provinceId;
    if (districtId) requestData.district_id = districtId;
    if (address) requestData.search_address = address;

    fetch(urlSearchLocation, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify(requestData),
    })
      .then((response) => response.json())
      .then((res) => {
        loading.style.display = "none";
        const container = document.querySelector(".consultant-locations");
        container.innerHTML = "";

        if (Array.isArray(res.data) && res.data.length > 0) {
          emptyState.style.display = "none";

          res.data.forEach((location) => {
            const article = document.createElement("article");
            article.className = "location";
            article.setAttribute("data-id", location.id);
            article.setAttribute("data-name", location.name);
            article.setAttribute("data-address", location.address);
            article.setAttribute("data-phone", location.phone || "");
            article.setAttribute("data-work-time", location.work_time || "");
            article.setAttribute("data-link-map", location.link_map || "");
            article.setAttribute(
              "data-link-google",
              `https://maps.google.com/maps?q=${location.lat},${location.lng}`
            );
            article.setAttribute("data-map", location.map || "");

            article.innerHTML = `
              <div class="location__thumb">
                  <img src="media${location.img}" alt="${location.name}">
              </div>
              <div class="location__info">
                  <h3 class="location__info__name">${location.name}</h3>
                  <address class="location__info__address">
                      <svg class="address__icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                          <use xlink:href="#location-solid-icon" />
                      </svg>
                      <p class="address__text">${location.address}</p>
                  </address>
                  <address class="location__info__phone">
                      <svg class="phone__icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                          <use xlink:href="#phone-solid-icon" />
                      </svg>
                      <p class="phone-number__text">${location.phone}</p>
                  </address>
              </div>
            `;
            container.appendChild(article);
          });

          const updatedArticles = container.querySelectorAll(".location");
          showMapDetail(updatedArticles);

          if (updatedArticles.length > 0) {
            updatedArticles[0].click();
          }
        } else {
          container.innerHTML = "";
          emptyState.style.display = "block";
        }
      })
      .catch((error) => {
        console.error("Lỗi khi tìm kiếm địa điểm:", error);
        loading.style.display = "none";
        emptyState.style.display = "block";
      })
      .finally(() => {
        isSearch = 0;
      });
  });

  return {
    init: function () {
      showMapDetail(articleEle); // hàm xử lý khi cần hiển thị bản đồ chi tiết
    },
  };
}();


document.addEventListener('DOMContentLoaded', function () {
  locationSearch.init();
  handleScrollLoadMore();
});

function showMapDetail(articles) {
  articles.forEach((article) => {
    article.addEventListener("click", () => {
      const detailsSection = document.querySelector("#locations-details");

      // Lấy dữ liệu từ article
      const { name, address, phone, workTime, linkMap, linkGoogle, map } =
        article.dataset;
      const imgSrc = article.querySelector(".location__thumb img").src;

      // Đẩy dữ liệu vào khu chi tiết
      detailsSection.querySelector(".thumb img").src = imgSrc;
      detailsSection.querySelector(".location-details__name").textContent = name;
      detailsSection.querySelector(".info__address .address__text").textContent =
        address;
      detailsSection.querySelector(
        ".contact__working-hour .working-hour__text"
      ).textContent = workTime;
      detailsSection.querySelector(
        ".contact__phone-number .phone-number__text"
      ).textContent = phone;

      // Cập nhật link chỉ đường
      const mapLink = detailsSection.querySelector(".btn-directions");
      mapLink.href = linkMap || linkGoogle;
      mapLink.title = `Chỉ đường đến ${name}`;

      // Reset Google Map ưu tiên `data-map` trước
      const googleMapContainer = document.querySelector("#google-map");
      if (map) {
        googleMapContainer.innerHTML = `${map}`;
      } else {
        googleMapContainer.innerHTML = `
                  <iframe 
                      src="${linkGoogle || linkMap}" 
                      width="100%" 
                      height="100%" 
                      style="border:0;" 
                      allowfullscreen="true" 
                      loading="lazy" 
                      referrerpolicy="no-referrer-when-downgrade">
                  </iframe>`;
      }
    });
  });
}

// Tự động load thêm khi scroll xuống trong div .consultant-locations
function handleScrollLoadMore() {
  const container = document.querySelector(".consultant-locations");
  const SCROLL_THRESHOLD = 80; // px cách đáy thì bắt đầu load
  let isLoading = false;
  let isGetFullLocation = false;

  container.addEventListener("scroll", async () => {
    if (isLoading || isGetFullLocation) return;

    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    if (distanceFromBottom <= SCROLL_THRESHOLD) {
      const currentPage = parseInt(container.dataset.pageOffset || "1", 10);
      const nextPage = currentPage + 1;

      isLoading = true;
      loading.style.display = "block";

      try {
        const res = await fetch("/apis/v3/locations/more", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ page: nextPage }),
        });

        const result = await res.json();
        const locations = result.data?.LocationsNew || [];

        if (locations.length > 0) {
          container.dataset.pageOffset = nextPage;
          renderLocations(locations);
          
          const updatedArticles = container.querySelectorAll(".location");
          showMapDetail(updatedArticles);
        } else {
          isGetFullLocation = true;
          console.log("Đã hết dữ liệu.");
        }
      } catch (err) {
        console.error("Lỗi khi load thêm location:", err);
      } finally {
        isLoading = false;
        loading.style.display = "none";
      }
    }
  });
}

function renderLocations(locations) {
  const container = document.querySelector(".consultant-locations");

  locations.forEach((location) => {
    const article = document.createElement("article");
    article.className = "location";
    article.setAttribute("data-id", location.id);
    article.setAttribute("data-name", location.name);
    article.setAttribute("data-address", location.address);
    article.setAttribute("data-phone", location.phone || "");
    article.setAttribute("data-work-time", location.work_time || "");
    article.setAttribute("data-link-map", location.link_map || "");
    article.setAttribute(
      "data-link-google",
      `https://maps.google.com/maps?q=${location.lat},${location.lng}`
    );
    article.setAttribute("data-map", location.map || "");

    article.innerHTML = `
      <div class="location__thumb">
          <img src="media${location.img}" alt="${location.name}">
      </div>
      <div class="location__info">
          <h3 class="location__info__name">${location.name}</h3>
          <address class="location__info__address">
              <svg class="address__icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="#location-solid-icon" />
              </svg>
              <p class="address__text">${location.address}</p>
          </address>
          <address class="location__info__phone">
              <svg class="phone__icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="#phone-solid-icon" />
              </svg>
              <p class="phone-number__text">${location.phone}</p>
          </address>
      </div>
    `;

    container.appendChild(article);
  });

  // Gán lại sự kiện click cho các bài mới
  const updatedArticles = container.querySelectorAll(".location");
  showMapDetail(updatedArticles);
}