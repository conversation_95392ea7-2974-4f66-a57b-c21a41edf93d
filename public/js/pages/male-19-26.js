function initBtnConsultant() {
  const btnOpenModalConsultant = document.querySelector(
    ".fast-action-controls #btn-location-round-id"
  );
  const btn1 = document.querySelector("#find-consultant-location-goal");
  const btn2 = document.querySelector("#find-consultant-location-banner");

  // Kiểm tra elements tồn tại trướ<PERSON> khi set onclick
  if (btn1 && btnOpenModalConsultant) {
    btn1.onclick = () => {
      btnOpenModalConsultant.click();
    };
  }

  if (btn2 && btnOpenModalConsultant) {
    btn2.onclick = () => {
      btnOpenModalConsultant.click();
    };
  }
}

// <!-- Init Swiper -->
function initSwiper2(pathSwiper) {
  var swiper = new Swiper(pathSwiper, {
    autoplay: {
      delay: 4000,
    },
    autoplay: true,
    loop: true,
    slidesPerView: 2,
    spaceBetween: 8,
    breakpoints: {
      992: {
        slidesPerView: 3,
        spaceBetween: 10,
      },
      1200: {
        slidesPerView: 4,
        spaceBetween: 20,
      },
    },
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
  });
}

function swiperGoal() {
  var swiperGoal = new Swiper(".swiper-container-goal", {
    autoplay: {
      delay: 3000, // Thời gian giữa các slide (ms)
      disableOnInteraction: true, // Giữ autoplay sau khi tương tác
    },
    autoplay: true,
    loop: true,
    slidesPerView: 1,
    spaceBetween: 60,
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
  });

  swiperGoal.on("click", function (swiperGoal, event) {
    var realIndex =
      swiperGoal.clickedSlide &&
      swiperGoal.clickedSlide.getAttribute("data-swiper-slide-index");
    if (realIndex !== null) {
      swiperGoal.slideToLoop(realIndex);
    }
  });

  // Check if swiper element exists before querying
  if (swiperGoal && swiperGoal.el && typeof swiperGoal.el.querySelector === 'function') {
    const btnPlay = swiperGoal.el.querySelector(".btn-play");
    const btnPause = swiperGoal.el.querySelector(".btn-pause");

    if (btnPause && btnPlay) {
      btnPause.onclick = () => {
        btnPlay.classList.toggle("active");
        btnPause.classList.toggle("active");
        swiperGoal.autoplay.stop();
      };
    }

    if (btnPlay && btnPause) {
      btnPlay.onclick = () => {
        btnPlay.classList.toggle("active");
        btnPause.classList.toggle("active");
        swiperGoal.autoplay.start();
      };
    }
  } else {
    // Fallback: use document.querySelector
    const btnPlay = document.querySelector(".swiper-container-goal .btn-play");
    const btnPause = document.querySelector(".swiper-container-goal .btn-pause");

    if (btnPause && btnPlay && swiperGoal) {
      btnPause.onclick = () => {
        btnPlay.classList.toggle("active");
        btnPause.classList.toggle("active");
        if (swiperGoal.autoplay) swiperGoal.autoplay.stop();
      };
    }

    if (btnPlay && btnPause && swiperGoal) {
      btnPlay.onclick = () => {
        btnPlay.classList.toggle("active");
        btnPause.classList.toggle("active");
        if (swiperGoal.autoplay) swiperGoal.autoplay.start();
      };
    }
  }
}

document.addEventListener("DOMContentLoaded", () => {
  initBtnConsultant();
  initSwiper2("#male-19-26-slide-article #article-list");
  swiperGoal();

  var swiperResult = new Swiper("#result-list", {
    effect: "coverflow",
    grabCursor: true,
    centeredSlides: true,
    coverflowEffect: {
      rotate: 0,
      scale: 1,
      stretch: 0,
      depth: 2,
      modifier: 1,
      slideShadows: true,
    },
    autoplay: {
      delay: 20000,
      disableOnInteraction: false,
    },
    autoplay: true,
    loop: true,
    slidesPerView: 1.05,
    spaceBetween: 15,

    breakpoints: {
      800: {
        slidesPerView: 2.2,
        spaceBetween: 30,
      },
      1100: {
        slidesPerView: 2.9,
        spaceBetween: 30,
      },
    },
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
  });

  swiperResult.on("click", function (swiperResult, event) {
    const slideClicked = swiperResult.clickedSlide;
    var realIndex =
      slideClicked && slideClicked.getAttribute("data-swiper-slide-index");
    if (
      realIndex !== null &&
      !slideClicked.className.includes("swiper-slide-active")
    ) {
      swiperResult.slideToLoop(realIndex);
    }
  });

  // Check if swiper element exists and has addEventListener method
  if (swiperResult && swiperResult.el && typeof swiperResult.el.addEventListener === 'function') {
    swiperResult.el.addEventListener("mouseenter", () => {
      if (swiperResult.autoplay) swiperResult.autoplay.pause();
    });

    swiperResult.el.addEventListener("mouseleave", () => {
      if (swiperResult.autoplay) swiperResult.autoplay.resume();
    });

    // Check if querySelector method exists
    if (typeof swiperResult.el.querySelector === 'function') {
      const btnPlay = swiperResult.el.querySelector(".btn-play");
      const btnPause = swiperResult.el.querySelector(".btn-pause");

      if (btnPause && btnPlay) {
        btnPause.onclick = () => {
          btnPlay.classList.toggle("active");
          btnPause.classList.toggle("active");
          if (swiperResult.autoplay) swiperResult.autoplay.stop();
        };
      }

      if (btnPlay && btnPause) {
        btnPlay.onclick = () => {
          btnPlay.classList.toggle("active");
          btnPause.classList.toggle("active");
          if (swiperResult.autoplay) swiperResult.autoplay.start();
        };
      }
    } else {
      // Fallback: use document.querySelector
      const btnPlay = document.querySelector("#result-list .btn-play");
      const btnPause = document.querySelector("#result-list .btn-pause");

      if (btnPause && btnPlay) {
        btnPause.onclick = () => {
          btnPlay.classList.toggle("active");
          btnPause.classList.toggle("active");
          if (swiperResult.autoplay) swiperResult.autoplay.stop();
        };
      }

      if (btnPlay && btnPause) {
        btnPlay.onclick = () => {
          btnPlay.classList.toggle("active");
          btnPause.classList.toggle("active");
          if (swiperResult.autoplay) swiperResult.autoplay.start();
        };
      }
    }
  }
});
