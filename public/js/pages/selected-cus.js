// Seclect Choice JS
const form = document.querySelector(".form");
const dropdowns = document.querySelectorAll(".dropdown");

// Check if Dropdowns are Exist
// Loop Dropdowns and Create Custom Dropdown for each Select Element
if (dropdowns.length > 0) {
  dropdowns.forEach((dropdown) => {
    createCustomDropdown(dropdown);
  });
}

// Check if Form Element Exist on Page
if (form !== null) {
  form.addEventListener("submit", (e) => {
    e.preventDefault();
  });
}

// Create Custom Dropdown
function createCustomDropdown(dropdown) {
  // Get All Select Options
  // And Convert them from NodeList to Array
  const options = dropdown.querySelectorAll("option");
  const optionsArr = Array.prototype.slice.call(options);

  // Create Custom Dropdown Element and Add Class Dropdown
  const customDropdown = document.createElement("div");
  customDropdown.classList.add("dropdown");
  dropdown.insertAdjacentElement("afterend", customDropdown);

  // Create Element for Selected Option
  const selected = document.createElement("div");
  selected.classList.add("dropdown-select");
  selected.textContent = optionsArr[0].textContent;
  customDropdown.appendChild(selected);

  // Create Element for Dropdown Menu
  // Add Class and Append it to Custom Dropdown
  const menu = document.createElement("div");
  menu.classList.add("dropdown-menu");
  customDropdown.appendChild(menu);
  selected.addEventListener("click", toggleDropdown.bind(menu));

  // Create Search Input Element
  const search = document.createElement("input");
  search.placeholder = "Search...";
  search.type = "text";
  search.classList.add("dropdown-menu-search");
  menu.appendChild(search);

  // Create Wrapper Element for Menu Items
  // Add Class and Append to Menu Element
  const menuInnerWrapper = document.createElement("div");
  menuInnerWrapper.classList.add("dropdown-menu-inner");
  menu.appendChild(menuInnerWrapper);

  // Loop All Options and Create Custom Option for Each Option
  // And Append it to Inner Wrapper Element
  optionsArr.forEach((option) => {
    const item = document.createElement("div");
    item.classList.add("dropdown-menu-item");
    item.dataset.value = option.value;
    item.textContent = option.textContent;
    menuInnerWrapper.appendChild(item);

    item.addEventListener(
      "click",
      setSelected.bind(item, selected, dropdown, menu)
    );
  });

  // Add Selected Class to First Custom Select Option
  menuInnerWrapper.querySelector("div").classList.add("selected");

  // Add Input Event to Search Input Element to Filter Items
  // Add Click Event to Element to Close Custom Dropdown if Clicked Outside
  // Hide the Original Dropdown(Select)
  search.addEventListener("input", filterItems.bind(search, optionsArr, menu));
  document.addEventListener(
    "click",
    closeIfClickedOutside.bind(customDropdown, menu)
  );
  dropdown.style.display = "none";
}

// Toggle for Display and Hide Dropdown
function toggleDropdown() {
  if (this.offsetParent !== null) {
    this.style.display = "none";
  } else {
    this.style.display = "block";
    this.querySelector("input").focus();
  }
}

// Set Selected Option
function setSelected(selected, dropdown, menu) {
  // Get Value and Label from Clicked Custom Option
  const value = this.dataset.value;
  const label = this.textContent;

  // Change the Text on Selected Element
  // Change the Value on Select Field
  selected.textContent = label;
  dropdown.value = value;

  // Close the Menu
  // Reset Search Input Value
  // Remove Selected Class from Previously Selected Option
  // And Show All Div if they Were Filtered
  // Add Selected Class to Clicked Option
  menu.style.display = "none";
  menu.querySelector("input").value = "";
  menu.querySelectorAll("div").forEach((div) => {
    if (div.classList.contains("is-select")) {
      div.classList.remove("is-select");
    }
    if (div.offsetParent === null) {
      div.style.display = "block";
    }
  });
  this.classList.add("is-select");
}

// Filter the Items
function filterItems(itemsArr, menu) {
  // Get All Custom Select Options
  // Get Value of Search Input
  // Get Filtered Items
  // Get the Indexes of Filtered Items
  const customOptions = menu.querySelectorAll(".dropdown-menu-inner div");
  const value = this.value.toLowerCase();
  const filteredItems = itemsArr.filter((item) =>
    item.value.toLowerCase().includes(value)
  );
  const indexesArr = filteredItems.map((item) => itemsArr.indexOf(item));

  // Check if Option is not Inside Indexes Array
  // And Hide it and if it is Inside Indexes Array and it is Hidden Show it
  itemsArr.forEach((option) => {
    if (!indexesArr.includes(itemsArr.indexOf(option))) {
      customOptions[itemsArr.indexOf(option)].style.display = "none";
    } else {
      if (customOptions[itemsArr.indexOf(option)].offsetParent === null) {
        customOptions[itemsArr.indexOf(option)].style.display = "block";
      }
    }
  });
}

// Close Dropdown if Clicked Outside Dropdown Element
function closeIfClickedOutside(menu, e) {
  if (
    e.target.closest(".dropdown") === null &&
    e.target !== this &&
    menu.offsetParent !== null
  ) {
    menu.style.display = "none";
  }
}

// Load District từ Province
document.addEventListener("DOMContentLoaded", function () {
  const provinceDropdown = document.getElementById("province");
  const districtDropdown = document.getElementById("district");

  if (!provinceDropdown || !districtDropdown) {
    console.error("Không tìm thấy dropdown tỉnh/thành hoặc quận/huyện.");
    return;
  }

  document.addEventListener("click", function (event) {
    const item = event.target.closest(".custom-dropdown__item");

    if (
      item &&
      item.closest("#province + .custom-dropdown .custom-dropdown__menu-inner")
    ) {
      const provinceId = item.dataset.id; // Lấy ID của tỉnh/thành

      if (!provinceId) {
        console.warn(
          "Không tìm thấy ID tỉnh/thành. Reset danh sách quận/huyện."
        );

        // Xóa `data-id` của province
        provinceDropdown.removeAttribute("data-id");

        // Reset dropdown quận/huyện
        resetDistrictDropdown(districtDropdown);
        return;
      }

      // Cập nhật `data-id` mới cho province
      provinceDropdown.setAttribute("data-id", provinceId);

      const urlDistrict = provinceDropdown.dataset.url + "/apis/v3/district";

      fetch(urlDistrict, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ fid: provinceId }),
      })
        .then((response) => response.json())
        .then((res) => {
          if (res && res.data) {
            updateDistrictDropdown(districtDropdown, res.data);
          } else {
            console.warn("Dữ liệu quận/huyện không hợp lệ.");
            resetDistrictDropdown(districtDropdown);
          }
        })
        .catch((error) => {
          console.error("Lỗi khi lấy danh sách quận/huyện:", error);
          resetDistrictDropdown(districtDropdown);
        });
    }
  });
});

// Hàm reset dropdown quận/huyện
function resetDistrictDropdown(dropdown) {
  const wrapper = dropdown.closest(".custom-form__group");
  const menuInner = wrapper.querySelector(".custom-dropdown__menu-inner");
  const selectedDisplay = wrapper.querySelector(".custom-dropdown__select");

  if (!menuInner || !selectedDisplay) {
    console.error("Không tìm thấy phần tử menuInner hoặc selectedDisplay.");
    return;
  }

  // Xóa dữ liệu cũ
  menuInner.innerHTML = "";

  // Thêm option mặc định
  const defaultOption = document.createElement("div");
  defaultOption.className = "custom-dropdown__item";
  defaultOption.dataset.value = "";
  defaultOption.dataset.id = "";
  defaultOption.textContent = "Chọn Quận/Huyện";
  menuInner.appendChild(defaultOption);

  // Cập nhật lại hiển thị dropdown
  selectedDisplay.textContent = "Chọn Quận/Huyện";
  dropdown.value = "";
  dropdown.removeAttribute("data-id"); // Reset data-id của select
}

function updateDistrictDropdown(dropdown, data) {
  const wrapper = dropdown.closest(".custom-form__group");
  const menuInner = wrapper.querySelector(".custom-dropdown__menu-inner");
  const selectedDisplay = wrapper.querySelector(".custom-dropdown__select");

  if (!menuInner || !selectedDisplay) {
    console.error("Không tìm thấy phần tử menuInner hoặc selectedDisplay.");
    return;
  }

  // Xóa dữ liệu cũ
  menuInner.innerHTML = "";

  // Thêm option mặc định
  const defaultOption = document.createElement("div");
  defaultOption.className = "custom-dropdown__item";
  defaultOption.dataset.value = "";
  defaultOption.dataset.id = "";
  defaultOption.textContent = "Chọn Quận/Huyện";
  menuInner.appendChild(defaultOption);

  // Thêm dữ liệu mới từ API
  data.forEach((item) => {
    const option = document.createElement("div");
    option.className = "custom-dropdown__item";
    option.dataset.value = item.label;
    option.dataset.id = item.value || "";
    option.textContent = item.label;

    // Thêm option vào menuInner
    menuInner.appendChild(option);
  });

  // Cập nhật lại hiển thị dropdown
  selectedDisplay.textContent = "Chọn Quận/Huyện";
  dropdown.value = "";
  dropdown.dataset.id = ""; // Reset data-id của select

  // Lắng nghe sự kiện khi chọn quận/huyện
  menuInner.addEventListener("click", function (event) {
    const item = event.target.closest(".custom-dropdown__item");
    if (item) {
      selectedDisplay.textContent = item.textContent;
      dropdown.value = item.dataset.value; // Gán giá trị vào select (data-value)
      dropdown.dataset.id = item.dataset.id; // Gán lại data-id cho select (data-id)

      menuInner.parentElement.classList.remove("custom-dropdown__menu--open");
    }
  });
}
