package components

import "webgo/pkg/gos/templates"

templ BlogBannerHeadCpn() {
    <section class="np-banner-head">
        <div class="np-banner-head__wrapper container">
            <picture>
                <source srcset={templates.AssetURL("/asset/images/newspage/kv-newspage-mobile.webp")} media="(max-width: 768px)">
                <img class="np-banner-head__image" src={templates.AssetURL("/asset/images/newspage/kv-newspage.webp")} alt="Banner Newspage" >
            </picture>
        </div>
    </section>
}