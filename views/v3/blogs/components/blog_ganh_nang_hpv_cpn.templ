package components

import "webgo/pkg/gos/templates"
import "webgo/hpv/blogs/transport/responses"
import "webgo/pkg/gos/utils"

templ BlogGanhNangHPVCpn(posts []responses.BlogItem) {
    <div class="ana-col" id="burden-section">
        <div class="ana-grid-box">
        <span class="ana-grid-box__title">GÁNH NẶNG TỪ HPV</span>
        <a class="txt-hover-red ana-grid-box__seemore" href="#" title="Gánh nặng từ HPV">
            <span>Xem tất cả</span>
            <div class="lib-grid-box__seemore-icon">
                <img src={templates.AssetURL("/asset/images/newspage/icon-see-more.svg")} alt="icon see more">
            </div>
        </a>
        </div>
        <div class="ana-articles">
            for i, post := range posts {
                @blogGanhNangItem(post, i)
            }
        </div>
        <div class="menubrc-wrapper">
            <div class="menu-line"></div>
            <img class="btn-show-more" src={ templates.AssetURL("/asset/images/newspage/icon-load-more.svg") } alt="">
        </div>
    </div>
}

templ blogGanhNangItem(post responses.BlogItem, i int) {
    <article class={ AddClass(i) }>
        <div class="ana-col__thumb">              
            <a href={ templates.SafeURL(post.PagePath) } title={ post.Title } class="nitem__thumb-link">
                <img class="ana__thumb-img" src={templates.ImgURL(post.Img)} alt={ post.Title }>
            </a>    
        </div>
        <div class="ana-col__content">
            <h3 class="ana-col__title">
                <a class="txt-hover-green" href={ templates.SafeURL(post.PagePath) } title={ post.Title }>{ post.Title }</a>
            </h3>
            <div class="ana__desc"> 
                @templ.Raw( post.Description ) 
            </div>
            <div class="ana__bottom">
            <ul class="ana__support">
                <li class="ana__support-item">
                    <div class="nitem__support-icon">
                        <img src={ templates.AssetURL("/asset/images/newspage/icon-like-gray.svg") } alt="like icon">
                    </div>
                    <span>1300</span>
                </li>
                <li class="ana__support-item">
                    <div class="nitem__support-icon">
                        <img src={ templates.AssetURL("/asset/images/newspage/icon-eyes-gray.svg") } alt="eyes icon">
                    </div>
                    <span>{ utils.FormatNumberWithDot(post.Views) }</span>
                </li>
            </ul> 
            <a class="ana-col__support-item txt-hover-red" href="#" title="">
                <span>Xem ngay ></span>
            </a>
        </div>
        </div>
    </article>    
}

templ BlogDocbannerCpn() {
    <div class="call-cta">
        <img src={templates.AssetURL("/asset/images/newspage/doctor.webp")} alt="cta" class="call-cta__img">
        <div class="call-cta__wrapper">
            <div class="call-cta-text">
                <p>Phòng vệ HPV</p>
                <h3>Ngay hôm nay!</h3>
            </div>
            <div class="call-cta__btn">
                <a href="#" title="" class="btn-primary ">THAM VẤN VỚI <br>
                    CHUYÊN GIA Y TẾ</a>
                <a href="#" title="" class="btn-primary ">TÌM ĐỊA ĐIỂM <br> TƯ VẤN GẦN NHẤT</a>
            </div>
        </div>
    </div>
}



func AddClass(i int) string {
    if i > 3 {
        return "nitem hidden"
    }
    return "nitem"
}