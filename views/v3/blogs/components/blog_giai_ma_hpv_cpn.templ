package components

import "webgo/pkg/gos/templates"
import "webgo/hpv/blogs/transport/responses"
import "webgo/pkg/gos/utils"

templ BlogGiaiMaHPVCpn(posts []responses.BlogItem) {
    <div class="ana-grid-wrapper" id="ana-section">
        <div class="ana-grid-box">
            <span class="ana-grid-box__title">GIẢI MÃ HPV</span>            
            <a class="txt-hover-red ana-grid-box__seemore" href={ templates.SafeURL("/giai-ma-hpv") } title="Giải mã HPV">
                <span>Xem tất cả</span>
                <div class="lib-grid-box__seemore-icon">
                    <img src={ templates.AssetURL("/asset/images/newspage/icon-see-more.svg") } alt="icon see more">
                </div>
            </a>
        </div>
        <div class="ana-row-grid">
            if len(posts) > 0 {
                for _, post := range posts {
                    @articleGiaiMaHPV(post)
                }
            }
        </div>
    </div>
}

templ articleGiaiMaHPV(post responses.BlogItem) {
    <article class="nitem ana--row ana__grid--row ">
        <div class="ana__thumb ana__thumb--row">              
            <a href={templates.SafeURL(post.PagePath)} title={ post.Title } class="nitem__thumb-link">
                <img class="ana__thumb-img ana__thumb-img--row" src={ templates.ImgURL(post.Img) } alt={ post.Title }>
            </a>    
        </div>
        <div class="ana__content ana__content--row">
            <h3 class="ana__title ana__title__grid">
                <a class="txt-hover-green" href={templates.SafeURL(post.PagePath)} title={ post.Title }>{ post.Title }</a>
            </h3>
            <div class="ana__bottom">
                <ul class="ana__support">
                    <li class="ana__support-item">
                        <div class="nitem__support-icon">
                            <img src={templates.AssetURL("/asset/images/newspage/icon-like-gray.svg")} alt="icon like">
                        </div>
                        <span>1300</span>
                    </li>
                    <li class="ana__support-item">
                        <div class="nitem__support-icon">
                            <img src={templates.AssetURL("/asset/images/newspage/icon-eyes-gray.svg")} alt="icon eyes">
                        </div>
                        <span>{ utils.FormatNumberWithDot(post.Views) }</span>
                    </li>
                </ul> 
            </div>
        </div>
    </article>
}