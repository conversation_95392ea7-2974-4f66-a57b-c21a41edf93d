package components

import "webgo/pkg/gos/templates"
import "webgo/hpv/blogs/transport/responses"
import "webgo/pkg/gos/utils"

templ BlogNewCpn(posts []responses.BlogItem) {
    <section class="container newest-section">
        <span class="newest-section__title">BÀI MỚI NHẤT</span>
        <div class="newest-section__wrapper"> 
            if len(posts) > 0 {
                @blogNewLeftCpn(posts[0])
            }  
            <div class="newest-section__right">  
            if len(posts) > 1 {
                for _, postRgt := range posts[1:] {
                    @blogNewItemRightCpn(postRgt)
                }   
            }
            </div> 
        </div>
    </section>
}

templ blogNewLeftCpn(post responses.BlogItem){
    <div class="newest-section__left">
        <div class="newest-section__bottom">
            <h3 class="newest-section__bottom-title">
                <a href={templates.SafeURL(post.PagePath)} title="{post.Title}">{post.Title}</a>
            </h3>
            <div class="newest-section__bottom-meta">
                <ul class="bottom-meta_support">
                    <li class="bottom-meta_support-item">   
                        <div class="nitem__support-icon">                  
                        <img src={templates.AssetURL("/asset/images/newspage/icon-like-white.svg")} alt="icon like">
                        </div>
                        <span>1.300</span>
                    </li>
                    <li class="bottom-meta_support-item">
                        <div class="nitem__support-icon"> 
                        <img src={templates.AssetURL("/asset/images/newspage/icon-eyes-white.svg")} alt="icon eyes">
                        </div>
                        <span>{ utils.FormatNumberWithDot(post.Views) }</span>
                    </li>
                </ul>
            </div>
        </div>
        <a href={templates.SafeURL(post.PagePath)} title={post.Title} class="newest-section__bottom-thumb">
            <img class="newest-section__bottom-img" src={templates.ImgURL(post.Img)} alt={post.Title} >
        </a> 
    </div>
}

templ blogNewItemRightCpn(post responses.BlogItem) {
    <article class="newest-right-item hover-gray">
        <div class="newest-right-thumb">              
            <a href={templates.SafeURL(post.PagePath)} title={post.Title} class="newest-right-thumb__link">
                <img class="newest__thumb-img" src={templates.ImgURL(post.Img)} alt={post.Title}>
            </a>    
        </div>
        <div class="newest-right-content">
            <h3 class="newest-right-content__title">
                <a class="txt-hover-green" href={templates.SafeURL(post.PagePath)} title={post.Title}>{post.Title}</a>
            </h3>             
            <div class="newest-right__bottom">
                <ul class="newest-right__support">
                    <li class="newest-right__support-item">
                        <div class="nitem__support-icon">
                            <img src={templates.AssetURL("/asset/images/newspage/icon-like-gray.svg")} alt="icon like">
                        </div>
                        <span>1.300</span>
                    </li>
                    <li class="newest-right__support-item">
                        <div class="nitem__support-icon">
                            <img src={templates.AssetURL("/asset/images/newspage/icon-eyes-gray.svg")} alt="icon eyes">
                        </div>
                        <span>{ utils.FormatNumberWithDot(post.Views) }</span>
                    </li>
                </ul>
            </div>
        </div>
    </article>
}