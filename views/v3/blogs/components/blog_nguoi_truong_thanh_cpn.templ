package components

import "webgo/pkg/gos/templates"
import "webgo/hpv/blogs/transport/responses"
import "webgo/pkg/gos/utils"

templ BlogNguoiTruongThanhCpn(posts []responses.BlogItem) {
    for _, post := range posts {
        @articleNguoiTruongThanh(post)
    }
}

templ articleNguoiTruongThanh(post responses.BlogItem) {
    <article class="gender-article">
        <div class="gender-article-cont-img">
          <img src={templates.ImgURL(post.Img)} alt={ post.Title } alt="người trưởng thành article" class="article-img" />
        </div>
        <div class="gender-article-content">
          <h3 class="gender-article-title txt-hover-green">
            { post.Title }
          </h3>
          <div class="gender-article-desc">
            @templ.Raw( post.Description )
          </div>
          <div class="gender-article-meta">
            <div class="nitem__support-item">
              <div class="nitem__support-icon">
                <img src={templates.AssetURL("/asset/images/newspage/icon-like-gray.svg")} alt="like icon">
              </div> 
              <span>1300</span>
            </div>
            <div class="nitem__support-item">
              <div class="nitem__support-icon">
                <img src={templates.AssetURL("/asset/images/newspage/icon-eyes-gray.svg")} alt="eyes icon">
              </div>
              <span>{ utils.FormatNumberWithDot(post.Views) }</span>
            </div>
          </div>
        </div>
    </article>
}
