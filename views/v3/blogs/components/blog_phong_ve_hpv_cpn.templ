package components

import "webgo/pkg/gos/templates"
import "webgo/hpv/blogs/transport/responses"
import "webgo/pkg/gos/utils"

templ BlogPhongVeHPVCpn(posts []responses.BlogItem) {
    <div class="ana-col">
        <div class="ana-grid-box">
            <h2 class="ana-grid-box__title">PHÒNG VỆ HPV</h2>
            <div class="txt-hover-red ana-grid-box__seemore">
                <span>Xem tất cả</span>
                <div class="lib-grid-box__seemore-icon">
                    <img src={ templates.AssetURL("/asset/images/newspage/icon-see-more.svg") } alt="icon see more">
                </div>
            </div>
        </div>
        <div class="ana-articles-2">
            for i, post := range posts {
                @blogPhongVeHPVItem(post, i)
            }
        </div>
        <div class="menubrc-wrapper">
            <div class="menu-line-2"></div>
            <img class="btn-show-more-2" src={ templates.AssetURL("/asset/images/newspage/icon-load-more.svg") } alt="icon arrow">
        </div>
    </div>
}

templ blogPhongVeHPVItem(post responses.BlogItem, i int) {
    <article class={ addClass(i) }>
        <div class="ana-col__thumb">
            <a href={ templates.SafeURL(post.PagePath) } title={ post.Title } class="nitem__thumb-link">
                <img class="ana__thumb-img" src={templates.ImgURL(post.Img)} alt={ post.Title }>
            </a>
        </div>
        <div class="ana-col__content">
            <h3 class="ana-col__title">
                <a class="txt-hover-green" href={ templates.SafeURL(post.PagePath) } title={ post.Title }>{ post.Title }</a>
            </h3>
            <div class="ana__desc">
                @templ.Raw( post.Description )
            </div>

            <div class="ana__bottom">
                <ul class="ana__support">
                    <li class="ana__support-item">
                        <div class="nitem__support-icon">
                            <img src={ templates.AssetURL("/asset/images/newspage/icon-like-gray.svg") } alt="like icon">
                        </div>
                        <span>1300</span>
                    </li>
                    <li class="ana__support-item">
                        <div class="nitem__support-icon">
                            <img src={ templates.AssetURL("/asset/images/newspage/icon-eyes-gray.svg") } alt="eyes icon">
                        </div>
                        <span>{ utils.FormatNumberWithDot(post.Views) }</span>
                    </li>
                </ul>
                <a class="ana-col__support-item txt-hover-red" href={ templates.SafeURL(post.PagePath) } title={ post.Title }>
                    <span>Xem ngay ></span>
                </a>
            </div>
        </div>
    </article>
}

func addClass(i int) string {
    if i > 3 {
        return "nitem-2 hidden"
    }
    return "nitem-2"
}