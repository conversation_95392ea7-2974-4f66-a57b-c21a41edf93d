package components

import "webgo/pkg/gos/templates"
import "webgo/hpv/blogs/transport/responses"

templ BlogThuVienBenhHocHPVCpn(posts []responses.BlogItem) {
    <div class="library-grid">
            if len(posts) > 0 {
                @blogThuVienBenhHocLeftItem(posts[0])
            } 

        <!-- 4 bài viết nhỏ bên phải -->
        <div class="library-articles">
            if len(posts) > 1 {
                for _, postRgt := range posts[1:] {
                    @blogThuVienBenhHocRightItem(postRgt)
                }   
            }
        </div>
    </div>
}

templ blogThuVienBenhHocLeftItem(post responses.BlogItem) {
    <article class="library-featured">
        <div class="library-featured-cont-img">
            <img src={templates.ImgURL(post.Img)} alt={ post.Title } class="library-featured-img" />
        </div>
        <div class="library-featured-content">
            <h3 class="library-featured-title txt-hover-green">
                { post.Title }
            </h3>
            <div class="library-featured-desc">
                @templ.Raw( post.Description )
            </div>
            <div class="gender-article-meta">
                <div class="nitem__support-item">
                    <div class="nitem__support-icon">
                        <img src={ templates.AssetURL("/asset/images/newspage/icon-like-gray.svg") } alt="like icon">
                    </div>
                    <span>1300</span>
                </div>
                <div class="nitem__support-item">
                    <div class="nitem__support-icon">
                        <img src={ templates.AssetURL("/asset/images/newspage/icon-eyes-gray.svg") } alt="eyes icon">
                    </div>
                    <span>1300</span>
                </div>
            </div>
        </div>
    </article>
}

templ blogThuVienBenhHocRightItem(post responses.BlogItem) {
    <article class="nitem lib--col liba__grid--col">
        <div class="lib__thumb lib__thumb--col">
            <a href={ templates.SafeURL(post.PagePath) } title={ post.Title } class="nitem__thumb-link">
                <img class="lib__thumb-img ana__thumb-img--row" src={templates.ImgURL(post.Img)} alt={ post.Title }>
            </a>
        </div>
        <div class="lib__content lib__content--col">
            <h3 class="lib__title">
                <a class="txt-hover-green" href={ templates.SafeURL(post.PagePath) } title={ post.Title }>{ post.Title }</a>
            </h3>
            <div class="lib__bottom">
                <ul class="lib__support">
                    <li class="lib__support-item">
                        <div class="nitem__support-icon">
                            <img src={ templates.AssetURL("/asset/images/newspage/icon-like-gray.svg") } alt="like icon">
                        </div>
                        <span>1300</span>
                    </li>
                    <li class="lib__support-item">
                        <div class="nitem__support-icon">
                            <img src={ templates.AssetURL("/asset/images/newspage/icon-eyes-gray.svg") } alt="eyes icon">
                        </div>
                        <span>1300</span>
                    </li>
                </ul>
            </div>
        </div>
    </article>
}