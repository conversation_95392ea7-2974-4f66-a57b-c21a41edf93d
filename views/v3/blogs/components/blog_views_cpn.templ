package components

import "webgo/pkg/gos/templates"
import "webgo/hpv/blogs/transport/responses"
import "webgo/pkg/gos/utils"

templ BlogViewsCpn(posts []responses.BlogItem) {
    <section class="container grid-wrapper">
    <div class="grid-wrapper__title">
        <span class="grid__analytic">BÀI VIẾT ĐƯỢC QUAN TÂM</span>
    </div>
    <div class="row-wrapper">
        for _, post := range posts {
            @blogViewsItem(post)
        }        
    </div>
</section>
}

templ blogViewsItem(post responses.BlogItem) {
    <article class="nitem nitem-row hover-gray">
        <div class="nitem__thumb nitem__thumb--row">              
            <a href="#" title="" class="nitem__thumb-link">
                <img class="nitem__thumb-img nitem__thumb-img--col" src={templates.ImgURL(post.Img)} alt={ post.Title }>
            </a>    
        </div>
        <div class="nitem__content nitem__content--row">
            <h3 class="nitem__title nitem__title__grid">
                <a class="txt-hover-green" href={ templates.SafeURL(post.PagePath) } title={ post.Title }>{ post.Title }</a>
            </h3>
            <div class="nitem-row__bottom">
                <ul class="nitem-row__support">
                    <li class="nitem__support-item">
                        <div class="nitem__support-icon">
                            <img src={ templates.AssetURL("/asset/images/newspage/icon-like-gray.svg") } alt="like icon">
                        </div>
                        <span>0</span>
                    </li>
                    <li class="nitem__support-item">
                        <div class="nitem__support-icon">
                            <img src={ templates.AssetURL("/asset/images/newspage/icon-eyes-gray.svg") } alt="eyes icon">
                        </div>
                        <span>{ utils.FormatNumberWithDot(post.Views) }</span>
                    </li>
                </ul> 
            </div>
        </div>
    </article>    
}