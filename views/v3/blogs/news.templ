package blogs

import "webgo/views/v3/layouts"
import "webgo/hpv/entity"
import "webgo/views/v3/blogs/components"
import "webgo/pkg/gos/templates"
import "webgo/hpv/blogs/transport/responses"

templ ListNews(seo *entity.Seo, datas *responses.BlogListResp) {
    @layouts.Master(seo,[]templ.Component{head()}, footer(datas.Options), scriptBlog(), jsBlogPhongVeHPV(datas), jsBlogGanhNangHPV(datas)) {
        <main>
            @components.BlogBannerHeadCpn()
            @components.BlogMenuSubCpn()  
            if datas != nil {
                if len(datas.BlogNew) > 0 {
                    @components.BlogNewCpn(datas.BlogNew) 
                }
                if len(datas.BlogViews) > 0 {
                    @components.BlogViewsCpn(datas.BlogViews) 
                }
            }     

            <section class="container ana-main">
                <div class="ana-main__wrapper">
                    <div class="wrap__left">
                        if datas != nil && len(datas.GiaiMaHPV) >0{
                            @components.BlogGiaiMaHPVCpn(datas.GiaiMaHPV)
                        }
                        if datas != nil && len(datas.GanhNangTuHPV) >0{
                            @components.BlogGanhNangHPVCpn(datas.GanhNangTuHPV)
                        }
                    </div>
                    <div class="wrap__right">
                        if datas != nil && len(datas.Options) >0{
                            @showBanner(datas.Options,"phong-ve-hpv-ngay-hom-nay")                            
                        }   
                    </div>
                </div>
            </section>

            <section class="container ana-main" id="protect-section">
                <div class="ana-main__wrapper">
                    <div class="wrap__left">
                        if datas != nil && len(datas.PhongVeHPV) > 0{
                            @components.BlogPhongVeHPVCpn(datas.PhongVeHPV) 
                        } 
                    </div>
                    <div class="wrap__right">
                        if datas != nil && len(datas.Options) >0{
                            @showBanner(datas.Options,"trac-nghiem-kien-thuc-hpv")                          
                        }                         
                    </div>
                </div>
            </section>

            if datas != nil && (len(datas.TreViThanhNien) > 0 || len(datas.ThanhThieuNien) > 0 || len(datas.NguoiTruongThanh) > 0){
                <section class="container section-gender">
                    <div class="gender-tabs-wrapper">
                        <div class="gender-tabs-background" id="gender-bg-1">
                            <img src={ templates.AssetURL("/asset/images/newspage/gender/gender-kid-symbol-bg.webp")} alt="Trẻ vị thành niên symbol">
                        </div>
                        <div class="gender-tabs-background"v id="gender-bg-2">
                            <img src={ templates.AssetURL("/asset/images/newspage/gender/gender-kid-symbol-bg-2.webp")} alt="Thanh thiếu niên symbol">
                        </div>
                        <div class="gender-tabs-background" id="gender-bg-3">
                            <img src={ templates.AssetURL("/asset/images/newspage/gender/gender-kid-symbol-bg-3.webp")} alt="Người trưởng thành symbol">
                        </div>
                        <div class="gender-modal" id="gender-modal-1">
                            <picture>
                                <source srcset="{{site}}/asset/images/newspage/gender/gender-kid-modal-mobile.webp" media="(max-width: 768px)">
                                <img class="gender-modal__img" src={ templates.AssetURL("/asset/images/newspage/gender/test1.png")} alt="Modal Kid" >
                            </picture>
                        </div>
                        <div class="gender-modal" id="gender-modal-2">
                            <picture>
                                <source srcset="{{site}}/asset/images/newspage/gender/gender-teen-modal-mobile.webp" media="(max-width: 768px)">
                                <img class="gender-modal__img" src={ templates.AssetURL("/asset/images/newspage/gender/gender-teen-modal.webp")} alt="Modal Teen" >
                            </picture>
                        </div>
                        <div class="gender-modal" id="gender-modal-3">
                            <picture>
                                <source srcset="{{site}}/asset/images/newspage/gender/gender-adult-modal-mobile.webp" media="(max-width: 768px)">
                                <img class="gender-modal__img" src={ templates.AssetURL("/asset/images/newspage/gender/gender-adult-modal.webp")} alt="Modal Adult" >
                            </picture>
                        </div>
                        <div class="gender-menu-tabs">
                            if datas != nil && len(datas.TreViThanhNien) > 0{
                                <div class="gender-tab" data-target="kid">Trẻ Vị Thành Niên</div>
                            }
                            if datas != nil && len(datas.ThanhThieuNien) > 0{
                                <div class="gender-tab" data-target="teen">Thanh Thiếu Niên</div>
                            }
                            if datas != nil && len(datas.NguoiTruongThanh) > 0{
                                <div class="gender-tab" data-target="adult">Người Trưởng Thành</div>
                            }
                        </div>      
                    </div>

                    if datas != nil && len(datas.TreViThanhNien) > 0 {
                        <div class="gender-articles-grid" id="gender-articles-1">
                            @components.BlogTreViThanhNienCpn(datas.TreViThanhNien)
                        </div>
                    }
                    if datas != nil && len(datas.ThanhThieuNien) > 0 {
                        <div class="gender-articles-grid" id="gender-articles-2">
                            @components.BlogThanhThieuNienCpn(datas.ThanhThieuNien)
                        </div>
                    }
                    if datas != nil && len(datas.NguoiTruongThanh) > 0 {
                        <div class="gender-articles-grid" id="gender-articles-3">
                            @components.BlogNguoiTruongThanhCpn(datas.NguoiTruongThanh)
                        </div>
                    } 
                </section>
            }  

            if datas != nil && len(datas.ThuVienBenhHocHPV) > 0{
                <section class="container library-wrapper" id="library-section">
                    <div class="lib-grid-box">
                        <span class="lib-grid-box__title">THƯ VIỆN BỆNH HỌC</span>
                        <div class="txt-hover-green lib-grid-box__seemore">
                            <span>Xem tất cả</span>
                            <div class="lib-grid-box__seemore-icon">
                                <img src={ templates.AssetURL("/asset/images/newspage/icon-see-more.svg") } alt="icon see more">
                            </div>
                        </div>
                    </div>                  
                    @components.BlogThuVienBenhHocHPVCpn(datas.ThuVienBenhHocHPV) 
                </section>
            }


            if datas != nil && len(datas.Options) >0{
                @showBanner(datas.Options,"banner-mino-bottom")
            }

            <div id="to_top" class="scroll-top" title="Lên đầu trang">
                <svg class="icon-up" width="800px" height="800px" viewBox="0 0 24 24" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M12 3C12.2652 3 12.5196 3.10536 12.7071 3.29289L19.7071 10.2929C20.0976 10.6834 20.0976 11.3166 19.7071 11.7071C19.3166 12.0976 18.6834 12.0976 18.2929 11.7071L13 6.41421V20C13 20.5523 12.5523 21 12 21C11.4477 21 11 20.5523 11 20V6.41421L5.70711 11.7071C5.31658 12.0976 4.68342 12.0976 4.29289 11.7071C3.90237 11.3166 3.90237 10.6834 4.29289 10.2929L11.2929 3.29289C11.4804 3.10536 11.7348 3 12 3Z"
                fill="#757575" />
                </svg>
            </div>    
        </main> 
    }
}

templ head(){
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_main.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_banner.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_menutabs.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_newest_article.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_related_article.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_analytic_article.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_burden_article.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_bannerdoc.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_protect_article.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_gender_article.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_library_article.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_banner_mino.css")}>
}

templ scriptBlog(){
    // JS Blog Gender
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const tabs = document.querySelectorAll(".gender-tab");

            const articleGrids = {
                kid: document.getElementById("gender-articles-1"),
                teen: document.getElementById("gender-articles-2"),
                adult: document.getElementById("gender-articles-3")
            };

            const modals = {
                kid: document.getElementById("gender-modal-1"),
                teen: document.getElementById("gender-modal-2"),
                adult: document.getElementById("gender-modal-3")
            };

            const backgrounds = {
                kid: document.getElementById("gender-bg-1"),
                teen: document.getElementById("gender-bg-2"),
                adult: document.getElementById("gender-bg-3")
            };

            function showTarget(target) {
                Object.values(articleGrids).forEach(el => { if (el) el.style.display = "none"; });
                if (articleGrids[target]) articleGrids[target].style.display = "grid";

                Object.values(modals).forEach(el => { if (el) el.style.display = "none"; });
                if (modals[target]) modals[target].style.display = "flex";

                Object.values(backgrounds).forEach(el => { if (el) el.style.display = "none"; });
                if (backgrounds[target]) backgrounds[target].style.display = "block";
            }

            let firstTab = tabs[0];
            if (firstTab) {
                tabs.forEach(t => t.classList.remove("active")); // Đảm bảo chỉ 1 tab active
                firstTab.classList.add("active");
                showTarget(firstTab.dataset.target);
            }

            tabs.forEach(tab => {
                tab.addEventListener("click", function () {
                    tabs.forEach(t => t.classList.remove("active"));
                    this.classList.add("active");
                    const target = this.dataset.target;
                    showTarget(target);
                });
            });

        });
    </script>

    // JS Scroll Section
    <script>
        document.querySelectorAll('.menubrc-item').forEach(item => {
            item.addEventListener('click', function () {
                const targetSelector = this.getAttribute('data-target');
                const targetElement = document.querySelector(targetSelector);

                if (targetElement) {
                    const offset = 140; // chiều cao menu
                    const elementPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                    const offsetPosition = elementPosition - offset;

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });

                    history.replaceState(null, null, ' ');
                }
            });
        });
    </script>

}

templ jsBlogPhongVeHPV(datas *responses.BlogListResp) {
    if datas != nil && len(datas.PhongVeHPV) > 0 {
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const articles2 = document.querySelectorAll('.ana-articles-2 .nitem-2');
                const btn2 = document.querySelector('.btn-show-more-2');
                const menuLine2 = document.querySelector('.menu-line-2');
                articles2.forEach((article2, index) => {
                    if (index >= 4) {
                        article2.classList.add('hidden');
                    }
                });
                btn2.addEventListener('click', function () {
                    articles2.forEach(article2 => article2.classList.remove('hidden'));
                    btn2.style.display = 'none';
                    menuLine2.style.display = 'none';
                });
            });
        </script>
    }
}


templ jsBlogGanhNangHPV(datas *responses.BlogListResp) {
    if datas != nil && len(datas.GanhNangTuHPV) > 0 {
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const articles = document.querySelectorAll('.ana-articles .nitem');
                const btn = document.querySelector('.btn-show-more');
                const menuLine = document.querySelector('.menu-line');
                articles.forEach((article, index) => {
                    if (index >= 4) {
                        article.classList.add('hidden');
                    }
                });
                btn.addEventListener('click', function () {
                    articles.forEach(article => article.classList.remove('hidden'));
                    btn.style.display = 'none';
                    menuLine.style.display = 'none';
                });
            });
        </script>
    }
}

func showBanner(options map[string]string, key string) templ.Component{
    if banner, ok := options[key]; ok {
        return templ.Raw(banner)
    }
    return templ.Raw("")
}

func footer(options map[string]string) templ.Component{
    if banner, ok := options["footer-blogs"]; ok {
        return templ.Raw(banner)
    }
    return nil
}