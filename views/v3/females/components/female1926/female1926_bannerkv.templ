package female1926

import "webgo/pkg/gos/templates"

templ Female1926Banner() {
    <section class="female-banner-main wow animate__pulse" data-wow-duration="1.5s" data-wow-delay="0s">
        <div class="description-banner-wrapper">
            <p class="description-banner-left">Không phải là bệnh nhân thật, chỉ dành cho mục đích minh họa. Nội dung này được phối hợp biên soạn bởi Hội Y học Dự phòng Việt Nam và MSD, kiểm nhận bởi Hội Y học Dự phòng Việt Nam và MSD tài trợ cho mục tiêu giáo dục VN-GSL-01914 17032027 <br>HPV được xem là nguyên nhân của gần như 100% các trường hợp ung thư cổ tử cung (1)</p>
            <p class="description-banner-right">(1) WHO, Cervical Cancer, Updated Mar 2024. https://www.emro.who.int/noncommunicable-diseases/campaigns/cervical-cancer-awareness-month-2024.html</p>    
        </div>
        <div class="content-banner">
            <span id="find-consultant-location-banner" class="btn-cta" alt="dia-diem-tu-van">THAM VẤN VỚI CHUYÊN GIA Y TẾ</span>
        </div>
        <div class="img-banner">
            <div class="img-banner__thumbnail">
                <div class="img-banner-background">
                <img class="" src={templates.AssetURL("/assetimages/female-19-26-adapt/female1926-desktop.webp")}
                    srcset={templates.AssetURL("/assetimages/female-19-26-adapt/female1926-mobile.webp 480w, /asset/images/female-19-26-adapt/female1926-desktop.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Male person in the age range of 19 to 26" />
                </div>
            </div>
        </div>
    </section>
}