package female1926

import "webgo/pkg/gos/templates"

templ Female1926ContentGoal() {
    <section class="container goal">
        <div class="swiper swiper-container-goal">
            <div class="boder-bg-slide">
                <div class="slide-to-show">
                    <svg style="transform: rotate(180deg);" class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#read-more-line-icon" />
                    </svg>
                    Lướt để xem
                </div>
            </div>
            <div class="swiper-wrapper">
                <div class="swiper-slide slide-1">
                    <div class="content-think-area">
                    <div class="pop-up-think right">
                        Đ<PERSON>u là những mục tiêu <br>
                        bạn luôn mơ <PERSON>?
                    </div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/female-19-26-adapt/female-1-small.webp")}
                    srcset={templates.AssetURL("/asset/images/female-19-26-adapt/female-1-small.webp 480w, /asset/images/female-19-26-adapt/female-1-big.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Đâu là những mục tiêu bạn luôn mơ ước?" />
                    </div>
                </div>
                <div class="swiper-slide slide-2">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu bạn luôn mơ ước?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think left">Tăng lương,<br>thăng chức</div>
                    <div class="pop-up-think right"> Chuẩn<br>"boss lady"</div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/female-19-26-adapt/female-2-small.webp")}
                    srcset={templates.AssetURL("/asset/images/female-19-26-adapt/female-2-small.webp 480w, /asset/images/female-19-26-adapt/female-2-big.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Đâu là những mục tiêu bạn luôn mơ ước?" />
                    </div>
                </div>
                <div class="swiper-slide slide-3">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu bạn luôn mơ ước?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">
                        Du lịch năm châu,<br>
                        khám phá văn hóa mới
                    </div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/female-19-26-adapt/female-3-small.webp")}
                    srcset={templates.AssetURL("/asset/images/female-19-26-adapt/female-3-small.webp 480w, /asset/images/female-19-26-adapt/female-3-big.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Đâu là những mục tiêu bạn luôn mơ ước?" />
                    </div>
                </div>
                <div class="swiper-slide slide-4">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu bạn luôn mơ ước?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">
                        Cưới người <br> mình thương
                    </div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/female-19-26-adapt/female-4-small.webp")}
                    srcset={templates.AssetURL("/asset/images/female-19-26-adapt/female-4-small.webp 480w, /asset/images/female-19-26-adapt/female-4-big.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Đâu là những mục tiêu bạn luôn mơ ước?" />
                    </div>
                </div>
                <div class="swiper-slide slide-5">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu bạn luôn mơ ước?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right mg-top">
                        Xây dựng <br> tổ ấm nhỏ
                    </div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/female-19-26-adapt/female-5-small.webp")}
                    srcset={templates.AssetURL("/asset/images/female-19-26-adapt/female-5-small.webp 480w, /asset/images/female-19-26-adapt/female-5-big.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Đâu là những mục tiêu bạn luôn mơ ước?" />
                    </div>
                </div>
                <div class="swiper-slide slide-6">
                    <div class="content-think-area">
                    <div class="pop-up-think right">
                        <span>Chủ động</span>
                        <span class="note">PHÒNG VỆ HPV <br>NGAY HÔM NAY</span>
                        <span class="sub_note">
                            để làm chủ tương lai<br> của chính mình
                        </span>
                        <a href="#result-section" class="now-btn">Tìm hiểu ngay về HPV</a>
                    </div>
                    </div>
                    <div class="img-swip-area">
                    <img class="swiper-lazy" data-src={templates.AssetURL("/asset/images/female-19-26-adapt/female-6-small.webp")}
                    srcset={templates.AssetURL("/asset/images/female-19-26-adapt/female-6-small.webp 480w, /asset/images/female-19-26-adapt/female-6-big.webp")}
                    sizes="(max-width: 480px) 480px, 1024px" alt="Đâu là những mục tiêu bạn luôn mơ ước?" />
                    </div>
                </div>
            </div>
            <div class="control-navigate-pagination">
                <div class="action-navigate">
                    <span class="btn-play">
                    <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#play-solid-icon" />
                    </svg>
                    </span>
                    <span class="btn-pause active">
                    <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#pause-solid-icon" />
                    </svg>
                    </span>
                </div>
                <div class="swiper-pagination bg-transparent"></div>
            </div>
        </div>
    </section>
}
