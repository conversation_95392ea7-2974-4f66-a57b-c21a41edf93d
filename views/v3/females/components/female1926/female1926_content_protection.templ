package female1926

import "webgo/pkg/gos/templates"

templ Female1926ContentProtection() {
    <section class="protection" id="protection">
        <div class="container-protection">
            <img class="protection-body-img lazyload" data-src={templates.AssetURL("/asset/images/female-19-26-adapt/protection-body.webp")} alt="Tương lai bao dự định, phòng vệ HPV ngay!">
            <div class="protection-box">
            <div class="protection-text-box">
                <h2>
                <span class="quote-symbol-top">“</span>
                Tương lai bao dự định,<br> phòng vệ ngay hôm nay!
                <span class="quote-symbol-bottom">”</span>
                </h2>
            </div>
            <div class="protection-btn">
                <a class="protection-btn-left" id="yaf-bot-ddtv_id" href="/dia-diem-tu-van" title="Địa điểm tư vấn">
                <span>
                    TÌM ĐỊA ĐIỂM TƯ VẤN<br>GẦN
                    BẠN NHẤT
                </span>
                </a>
                <div class="protection-btn-right open-popup-locations" id="find-consultant-location-goal"><span  title="Tham vấn với chuyên gia y tế">THAM VẤN<br>VỚI CHUYÊN
                    GIA Y TẾ</span></div>
            </div>
            </div>
        </div>
    </section>
}