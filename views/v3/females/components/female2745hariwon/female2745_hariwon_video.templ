package female2745hariwon

import "webgo/pkg/gos/templates"

templ HariwonVideoEmbed() {
	<section class="video">
		<div class="video__container">
			<div class="video__thumb" id="hariwon-video-trigger">
				<img
					class="video__img"
					src={ templates.AssetURL("/asset/images/female-27-45-hariwon/thumb-hariwon-video.png") }
					alt="Phòng vệ HPV - Hari Won"
					loading="lazy"
				/>
				<div class="video__play" id="hariwon-play-button">
					<span class="video__play-icon"></span>
				</div>
			</div>
			<div class="video__iframe" id="hariwon-youtube-container" style="display: none;"></div>
		</div>
		<script>
      // Hariwon Video Component
      document.addEventListener("DOMContentLoaded", function () {
        const hariwonTrigger = document.getElementById("hariwon-video-trigger");
        const hariwonPlayButton = document.getElementById("hariwon-play-button");
        const hariwonContainer = document.getElementById("hariwon-youtube-container");
        let isHariwonVideoLoaded = false;
        let autoPlayTimeout = null;

        // Load video with sound control
        function loadHariwonVideo(isManualClick = false) {
          if (isHariwonVideoLoaded) return;

          hariwonContainer.innerHTML = "";

          const hariwonIframe = document.createElement("iframe");
          hariwonIframe.id = "hariwon-youtube-player";

          // Manual click: unmuted, Auto-play: muted
          const muteParam = isManualClick ? "mute=0" : "mute=1";
          hariwonIframe.src = `https://www.youtube.com/embed/Pc64qjJDt60?autoplay=1&${muteParam}&rel=0&modestbranding=1&loop=1&playlist=Pc64qjJDt60&enablejsapi=1&start=0&controls=1`;

          hariwonIframe.title = "TỪ CÂU CHUYỆN TRỞ THÀNH NGUỒN CẢM HỨNG, BẢO VỆ SỨC KHỎE - LÀM CHỦ CUỘC ĐỜI CÙNG HARI WON";
          hariwonIframe.setAttribute("frameborder", "0");
          hariwonIframe.setAttribute("allow", "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share");
          hariwonIframe.setAttribute("allowfullscreen", "");
          hariwonIframe.setAttribute("referrerpolicy", "strict-origin-when-cross-origin");

          hariwonIframe.style.cssText = `
              position: absolute !important;
              top: 0 !important;
              left: 0 !important;
              width: 100% !important;
              height: 100% !important;
              border: none !important;
              display: block !important;
              z-index: 10 !important;
            `;

          hariwonContainer.style.display = "block";
          hariwonContainer.appendChild(hariwonIframe);

          // Smooth transition: fade out thumb, fade in video
          setTimeout(() => {
            hariwonTrigger.classList.add("video__thumb--fade-out");
            hariwonContainer.classList.add("video__iframe--fade-in");
          }, 100);

          // Remove thumb from DOM after fade out
          setTimeout(() => {
            hariwonTrigger.style.display = "none";
          }, 600);

          isHariwonVideoLoaded = true;
        }

        // Play button click handler
        hariwonPlayButton.addEventListener("click", function (e) {
          e.stopPropagation();
          clearAutoPlay();
          loadHariwonVideo(true); // Manual click with sound
        });

        // Clear auto play timeout
        function clearAutoPlay() {
          if (autoPlayTimeout) {
            clearTimeout(autoPlayTimeout);
            autoPlayTimeout = null;
          }
        }

        // Thumbnail click handler
        hariwonTrigger.addEventListener("click", function (e) {
          e.stopPropagation();
          clearAutoPlay();
          loadHariwonVideo(true); // Manual click with sound
        });

        // Auto-play on scroll
        const observerOptions = {
          root: null,
          rootMargin: '0px',
          threshold: 0.5
        };

        const videoObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting && !isHariwonVideoLoaded) {
              autoPlayTimeout = setTimeout(() => {
                loadHariwonVideo(false); // Auto-play muted
              }, 5000);
            } else if (!entry.isIntersecting) {
              clearAutoPlay();
            }
          });
        }, observerOptions);

        videoObserver.observe(hariwonTrigger);
      });
    </script>
	</section>
}
