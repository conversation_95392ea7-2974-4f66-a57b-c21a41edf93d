package female918

import "webgo/pkg/gos/templates"

templ Female918ContentProtection() {
    <section class="protection" id="protection">
        <div class="container-protection">
            <img class="protection-body-img lazyload" data-src={templates.AssetURL("/asset/images/female-9-18-adapt/protection-body.webp")} alt="Bảo vệ mình ngay hôm nay">
            <div class="protection-box">
            <div class="protection-text-box">
                <h2>
                <span class="quote-symbol-top">“</span>
                    Phòng vệ cho con,<br> nâng bước hoài bão lớn
                <span class="quote-symbol-bottom">”</span>
                </h2>
            </div>
            <div class="protection-btn">
                <a class="protection-btn-left" id="tg_ddtv_id" href="/dia-diem-tu-van" title="Địa điểm tư vấn">
                <span>
                    TÌM ĐỊA ĐIỂM TƯ VẤN<br>GẦN
                    BẠN NHẤT
                </span>
                </a>
                <div class="protection-btn-right" id="find-consultant-location-goal"><span title="Tham vấn với chuyên gia y tế">THAM
                    VẤN<br>VỚI CHUYÊN
                    GIA Y TẾ</span></div>
            </div>
            </div>
        </div>
    </section>
}