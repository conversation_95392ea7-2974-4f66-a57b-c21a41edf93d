package females

import "webgo/pkg/gos/templates"
import "webgo/views/v3/layouts"
import "webgo/hpv/femalesv3/transport/responses"

templ FemaleIndex(page *responses.FemaleV3Resp) {
    @layouts.Master(page.Seo,[]templ.Component{head()}, FooterFemaleIndex(page.Options), femaleIndexJS()) {
        <main class="container container-content-female py-15">
            <div class="content__left">
                <div class="title-img">
                    <img src={templates.AssetURL("/asset/images/female/banner-title-female.png")} alt="Dự phòng HPV cho Nam từ 27-45 tuổi">
                </div>
                <p class="description-banner">*Nội dung này do Hội Y học Dự phòng Việt Nam cung cấp và được MSD tài trợ vì mục
                    đích giáo dục CDC.<br>HPV and Men - Fact Sheet, Centers for Disease Control and Prevention. Updated Apr 2022..
                </p>
                <div class="swiper mySwiper-left">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <picture>
                                <source srcset={templates.AssetURL("/asset/images/female/female-bg-1-800px.webp")} media="(max-width: 768px)"
                                    type="image/webp">
                                <source srcset={templates.AssetURL("/asset/images/female/female-bg-1-1200px.webp")} type="image/webp">
                                <img src={templates.AssetURL("/asset/images/female/female-bg-1-1200px.webp")} alt="Dự phòng HPV cho Nữ">
                            </picture>
                        </div>
                        <div class="swiper-slide">
                            <picture>
                                <source srcset={templates.AssetURL("/asset/images/female/female-bg-2-800px.webp")} media="(max-width: 768px)"
                                    type="image/webp">
                                <source srcset={templates.AssetURL("/asset/images/female/female-bg-2-1200px.webp")} type="image/webp">
                                <img src={templates.AssetURL("/asset/images/female/female-bg-2-1200px.webp")} alt="Dự phòng HPV cho Nữ">
                            </picture>
                        </div>
                        <div class="swiper-slide">
                            <picture>
                                <source srcset={templates.AssetURL("/asset/images/female/female-bg-3-800px.webp")} media="(max-width: 768px)"
                                    type="image/webp">
                                <source srcset={templates.AssetURL("/asset/images/female/female-bg-3-1200px.webp")} type="image/webp">
                                <img src={templates.AssetURL("/asset/images/female/female-bg-3-1200px.webp")} alt="Dự phòng HPV cho Nữ">
                            </picture>
                        </div>
                    </div>
                    <div class="swiper-pagination-custom-new"></div>
                </div>
            </div>
            <div class="content__center">
                <span class="content-center__title">AI CẦN DỰ PHÒNG HPV?</span>
                <div class="group-box-female">
                    <a class="female-box box-1" href="/du-phong-hpv-cho-nu-tu-9-18-tuoi"
                        target="_blank" title="Du phong HPV nu 9-18 tuoi">
                        <div class="bg-overlay"></div>
                        <div class="female-box__img">
                            <img src={templates.AssetURL("/asset/images/female/female-9-18.png")} alt="female 9-18">
                        </div>
                        <span class="female-box__content">
                            TRẺ VỊ THÀNH NIÊN
                            <br>
                            9 - 18 TUỔI</span>
                        <button class="female-box__cta">
                            <span>Tìm hiểu thêm</span>
                            <img src={templates.AssetURL("/asset/images/female/arrow-right.svg")} alt="arrow-right">
                        </button>
                    </a>
                    <a class="female-box box-2" href="/du-phong-hpv-cho-nu-tu-19-26-tuoi"
                        target="_blank" title="Du phong HPV nu 19-26 tuoi">
                        <div class="bg-overlay"></div>
                        <div class="female-box__img">
                            <img src={templates.AssetURL("/asset/images/female/female-19-26.png")} alt="female 19-26">
                        </div>
                        <span class="female-box__content">
                            THANH THIẾU NIÊN
                            <br>
                            19 - 26 TUỔI</span>
                        <button class="female-box__cta">
                            <span>Tìm hiểu thêm</span>
                            <img src={templates.AssetURL("/asset/images/female/arrow-right.svg")} alt="arrow-right">
                        </button>
                    </a>
                    <a class="female-box box-3" href="/du-phong-hpv-cho-nu-tu-27-45-tuoi"
                        target="_blank" title="Du phong HPV nu 27-45 tuoi">
                        <div class="bg-overlay"></div>
                        <div class="female-box__img">
                            <img src={templates.AssetURL("/asset/images/female/female-27-45.png")} alt="female 27-45">
                        </div>
                        <span class="female-box__content">
                            NGƯỜI TRƯỞNG THÀNH
                            <br>
                            27 - 45 TUỔI</span>
                        <button class="female-box__cta">
                            <span>Tìm hiểu thêm</span>
                            <img src={templates.AssetURL("/asset/images/female/arrow-right.svg")} alt="arrow-right">
                        </button>
                    </a>
                </div>
            </div>
            <div class="content__right">
                <div class="content-right__top">
                    <div class="content-right__title">
                        Phòng vệ HPV<br>
                        <span>Ngay hôm nay!</span>
                    </div>
                    <div class="group-cta-female">
                        <div class="content-right__cta open-popup-locations">
                            THAM VẤN VỚI<br>CHUYÊN GIA Y TẾ
                        </div>
                        <a class="content-right__cta" href="/dia-diem-tu-van" title="dia-diem-tu-van">
                            Tìm địa điểm<br>tư vấn gần nhất
                        </a>
                    </div>
                </div>
                <div class="content-right__img">
                    <img src={templates.AssetURL("/asset/images/female/female-docter.png")} alt="docter">
                </div>
            </div>
        </main>
    }
}

templ head() {
    <link rel="stylesheet" href={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/female/female.css")}>
}

templ femaleIndexJS() {
    <script type="text/javascript" src={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.js")}></script>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            var swiper = new Swiper(".mySwiper-left", {
                loop: true,
                autoplay: {
                    delay: 5000,
                },
                pagination: {
                    el: ".swiper-pagination-custom-new",
                    clickable: true,
                },
            });
        });
    </script>
}

func FooterFemaleIndex(options map[string]string) templ.Component{
    if banner, ok := options["footer-female-index"]; ok {
        return templ.Raw(banner)
    }
    return nil
}