package females

import "webgo/pkg/gos/templates"
import "webgo/views/v3/layouts"
import "webgo/hpv/femalesv3/transport/responses"
import "webgo/views/v3/females/components/female2745"


templ Female2745(datas *responses.Female2745V3Resp) {
    @layouts.Master(datas.Seo,[]templ.Component{head2745()}, FooterFemale2745(datas),female2745JS()) {
        <main class="container container-content">
            <div class="main-container" id="main-container">
                @female2745.Female2745Banner()
                @female2745.Female2745ContentGoal()
                @female2745.Female2745ContentResult()
                @female2745.Female2745ContentFact()
                @female2745.Female2745ContentProtection()

                if datas != nil && len(datas.PostNew) > 0{
                    @female2745.Female2745ContentArticle(datas.PostNew)                
                }
                
                @female2745.Female2745SubFooter()

            </div>
        </main>    
    }
}

templ head2745() {
    <link rel="stylesheet" href={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/female-27-45/female-27-45-adapt.css")}>
}

templ female2745JS() {
    <script type="text/javascript" src={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.js")}></script>
    <script type="text/javascript" src={templates.AssetURL("/asset/js/pages/male-19-26.js")}></script>

    <script type="text/javascript">
      (() => {
         const scriptElement = document.createElement('script');
         scriptElement.id = "js-load-lib-swiper"
         document.body.appendChild(scriptElement);
      })()
   </script>
  
    <script>
      document.addEventListener("DOMContentLoaded", function () {
         const lazyImages = document.querySelectorAll("img.lazyload");

         const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach((entry) => {
               if (entry.isIntersecting) {
                  const img = entry.target;
                  img.src = img.dataset.src;
                  if (img.dataset.srcset) {
                     img.srcset = img.dataset.srcset;
                  }
                  img.classList.remove("lazyload");
                  observer.unobserve(img);
               }
            });
         });

         lazyImages.forEach((img) => {
            imageObserver.observe(img);
         });
      });
      document.querySelectorAll(".open-popup-locations").forEach(button => {
      button.addEventListener("click", () => {
         document.querySelector("#btn-location-round-id").click()
      });
      });
   </script>
   <script>
         document.addEventListener('DOMContentLoaded', function () {
         const contentContainers = document.querySelectorAll('.content-footer');
         
         contentContainers.forEach((content, index) => {
            const showMoreButton = document.createElement('div');
            showMoreButton.classList.add('show-more');
            showMoreButton.innerHTML = 'Xem thêm &#9660';
            content.parentNode.insertBefore(showMoreButton, content.nextSibling);
   
            showMoreButton.addEventListener('click', function () {
               if (content.style.maxHeight) {
               content.style.maxHeight = null;
               showMoreButton.innerHTML = 'Xem thêm &#9660'; 
               } else {
               content.style.maxHeight = content.scrollHeight + 'px';
               showMoreButton.innerHTML = 'Ẩn &#9650'; 
               }
            });
         });
         });
   </script>
}

func FooterFemale2745(datas *responses.Female2745V3Resp) templ.Component{
    if datas != nil && datas.Options != nil {
        if banner, ok := datas.Options["footer-female-2745"]; ok {
            return templ.Raw(banner)
        }
    }    
        return nil
}