package components

import "webgo/views/v3/partials"
import "webgo/pkg/gos/templates"

templ GenvHeader() {
    @partials.ScriptTracking()
    <img height="1" width="1" style="border-style:none;" alt="" src="https://insight.adsrvr.org/track/pxl/?adv=3dc1drw&ct=0:4a8ynez&fmt=3"/>
    <div class="video-gif-genv">
        <video id="genv-masthead-video" autoplay muted loop playsinline>
            Your browser does not support the video tag.
        </video>
    </div>
    <header class="menu-header" id="menu">
        <div class="container main-menu">
            <div class="modal-search" id="modal-search">
                <div class="overlay"></div>
                <div class="search-container">
                    <div class="search-content">
                        <div class="search-control">
                            <input type="text" placeholder="<PERSON>hậ<PERSON> từ kho<PERSON> tìm kiếm" class="input-control">
                            <button class="search">
                                T<PERSON><PERSON> kiếm
                            </button>
                            <span class="close-modal">
                                <svg width="1em" height="1em" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15.4516 1.32027L1.30942 15.4624M1.30943 1.32027L15.4516 15.4624"
                                        stroke="currentColor">
                                    </path>
                                </svg>
                            </span>
                        </div>
                        <div class="results-search">
                            <h3 class="title">Tìm kiếm nhiều nhất</h3>
                            <div class="list-results">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="btn-open-menu-mobile">
                <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#3-dots" fill="currentColor"></use>
                </svg>
            </div>

            <a href={templates.SafeURL("")}>
                <div class="logos">
                    <img src={templates.AssetURL("/asset/images/common/msd-hpv-logo.webp")} alt="hpv">
                </div>
            </a>
            <nav id="nav-menu" class="nav-menu">
                <ul>
                    <li class="nav-menu__item  ">
                        <a href={templates.SafeURL("/chien-dich-toan-quoc")}>
                            <span>Chiến dịch toàn quốc</span>
                        </a>
                    </li>
                    <li class="nav-menu__item  ">
                        <a href={templates.SafeURL("/wearegenv")}>
                            <span>GenV</span>
                        </a>
                    </li>
                    <li class="nav-menu__item is-female ">
                        <a href={templates.SafeURL("/du-phong-hpv-cho-nu")}>
                            <span>Nữ giới</span>
                            <img src={templates.AssetURL("/asset/images/male/arrow-down.svg")} alt="down arrow">
                        </a>
                        <ul class="sub-menu">
                            <li class="sub-menu__item">
                                <a class="nav-link" href={templates.SafeURL("/du-phong-hpv-cho-nu-tu-9-18-tuoi")}
                                    title="Du phong HPV nu 9-18 tuoi">
                                    Trẻ vị thành niên 9 - 18 Tuổi
                                </a>
                            </li>
                            <li class="sub-menu__item">
                                <a class="nav-link" href={templates.SafeURL("/du-phong-hpv-cho-nu-tu-19-26-tuoi")}
                                    title="Du phong HPV nu 19-26 tuoi">
                                    Thanh thiếu niên 19 - 26 Tuổi
                                </a>
                            </li>
                            <li class="sub-menu__item">
                                <a class="nav-link" href={templates.SafeURL("/du-phong-hpv-cho-nu-tu-27-45-tuoi")}
                                    title="Du phong HPV nu 27-45 tuoi">
                                    Trưởng thành 27 - 45 Tuổi
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-menu__item  is-male">
                        <a href={templates.SafeURL("/du-phong-hpv-cho-nam")}>
                            <span>Nam giới</span>
                            <img src={templates.AssetURL("/asset/images/male/arrow-down.svg")} alt="down arrow">
                        </a>
                        <ul class="sub-menu">
                            <li class="sub-menu__item">
                                <a class="nav-link" href={templates.SafeURL("/du-phong-hpv-cho-nam-tu-9-18-tuoi")}
                                    title="Du phong HPV nam 9-18 tuoi">
                                    Trẻ vị thành niên 9 - 18 Tuổi
                                </a>
                            </li>
                            <li class="sub-menu__item">
                                <a class="nav-link" href={templates.SafeURL("/du-phong-hpv-cho-nam-tu-19-26-tuoi")}
                                    title="Du phong HPV nam 19-26 tuoi">
                                    Thanh thiếu niên 19 - 26 Tuổi
                                </a>
                            </li>
                            <li class="sub-menu__item">
                                <a class="nav-link" href={templates.SafeURL("/du-phong-hpv-cho-nam-tu-27-45-tuoi")}
                                    title="Du phong HPV nam 27-45 tuoi">
                                    Người trưởng thành 27 - 45 Tuổi
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-menu__item  ">
                        <a href={templates.SafeURL("/dia-diem-tu-van")} id="btn-location-id-desktop">
                            <span>Địa điểm tư vấn</span>
                        </a>
                    </li>
                </ul>
                <div class="search-control" id="search-control" title="Search">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#search" fill="currentColor"></use>
                    </svg>
                </div>
            </nav>
        </div>
    </header>
    <div class="menu-footer-mobile" id="menu-footer-mobile">
        <div class="list-menu-items">

            <div class="menu-item">
                <a href={templates.SafeURL("")} title="Trang chủ" class="active" id="menu-bar-mobile-home-page">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#home-outline" fill="currentColor" />
                    </svg>
                </a>
            </div>
            <div class="menu-item">
                <a href={templates.SafeURL("/du-phong-hpv-cho-nu")} title="Nữ giới" id="menu-bar-mobile-female-page">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#female-outline" fill="currentColor" />
                    </svg>
                </a>
            </div>
            <div class="menu-item">
                <a href={templates.SafeURL("/du-phong-hpv-cho-nam")} title="Nam giới" id="menu-bar-mobile-male-page">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#male-outline" fill="currentColor" />
                    </svg>
                </a>
            </div>
            <div class="menu-item">
                <a href="https://m.me/hpvvietnam?ref=botshare" title="Chat">
                    <svg id="icon-chat" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 551.2 573.7">
                        <defs>
                            <style>
                                .st0 {
                                    fill: #fff;
                                }
                            </style>
                        </defs>
                        <path class="st0"
                            d="M130.7,405.5c-1.3,0-2.6-.3-3.9-.8-3.7-1.6-6.2-5.3-6.1-9.3l.7-53.1h-8.7c-17.6,0-31.8-14.3-31.8-31.8v-121.9c0-17.6,14.3-31.8,31.8-31.8h249.7c17.6,0,31.8,14.3,31.8,31.8v52.5c0,5.5-4.5,10-10,10s-10-4.5-10-10v-52.5c0-6.5-5.3-11.8-11.8-11.8H112.8c-6.5,0-11.8,5.3-11.8,11.8v121.9c0,6.5,5.3,11.8,11.8,11.8h18.8c2.7,0,5.2,1.1,7.1,3,1.9,1.9,2.9,4.5,2.9,7.2l-.5,38.7,46.1-45.9c1.9-1.9,4.4-2.9,7.1-2.9h31c5.5,0,10,4.5,10,10s-4.5,10-10,10h-26.8l-60.6,60.3c-1.9,1.9-4.5,2.9-7.1,2.9Z" />
                        <path class="st0"
                            d="M435,463.8c-2.5,0-4.9-.9-6.8-2.6l-45.7-41.9h-115.4c-14.9,0-27-12.1-27-27v-108.3c0-14.9,12.1-27,27-27h176.1c14.9,0,27,12.1,27,27v108.3c0,14.4-11.3,26.2-25.5,27l.3,34.5c0,4-2.3,7.6-5.9,9.2-1.3.6-2.7.9-4.1.9ZM267.2,276.9c-3.9,0-7,3.1-7,7v108.3c0,3.9,3.1,7,7,7h119.3c2.5,0,4.9.9,6.8,2.6l31.6,29-.2-21.5c0-2.7,1-5.2,2.9-7.1,1.9-1.9,4.4-3,7.1-3h8.6c3.9,0,7-3.1,7-7v-108.3c0-3.9-3.1-7-7-7h-176.1Z" />
                        <path class="st0"
                            d="M338.4,229.6h-196.9c-5.5,0-10-4.5-10-10s4.5-10,10-10h196.9c5.5,0,10,4.5,10,10s-4.5,10-10,10Z" />
                        <path class="st0"
                            d="M230.7,276.9h-89.2c-5.5,0-10-4.5-10-10s4.5-10,10-10h89.2c5.5,0,10,4.5,10,10s-4.5,10-10,10Z" />
                        <circle class="st0" cx="297.6" cy="339.3" r="11.2" />
                        <circle class="st0" cx="354.4" cy="339.3" r="11.2" />
                        <circle class="st0" cx="413.3" cy="339.3" r="11.2" />
                    </svg>
                </a>
            </div>
            <div class="menu-item open-popup-locations">
                <a href="javascript:void(0);">
                    <svg id="icon-lich" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 551.2 573.7">
                        <path class="st0"
                            d="M250.7,497.5H120.1c-25,0-45.4-20.4-45.4-45.4V168.3c0-25,20.4-45.4,45.4-45.4h313.3c25,0,45.4,20.4,45.4,45.4v101.3c0,5.5-4.5,10-10,10s-10-4.5-10-10v-101.3c0-14-11.4-25.4-25.4-25.4H120.1c-14,0-25.4,11.4-25.4,25.4v283.9c0,14,11.4,25.4,25.4,25.4h130.6c5.5,0,10,4.5,10,10s-4.5,10-10,10Z" />
                        <path class="st0"
                            d="M393,184.7c-5.5,0-10-4.5-10-10v-82.5c0-5.5,4.5-10,10-10s10,4.5,10,10v82.5c0,5.5-4.5,10-10,10Z" />
                        <path class="st0"
                            d="M275,184.7c-5.5,0-10-4.5-10-10v-82.5c0-5.5,4.5-10,10-10s10,4.5,10,10v82.5c0,5.5-4.5,10-10,10Z" />
                        <path class="st0"
                            d="M157,184.7c-5.5,0-10-4.5-10-10v-82.5c0-5.5,4.5-10,10-10s10,4.5,10,10v82.5c0,5.5-4.5,10-10,10Z" />
                        <path class="st0"
                            d="M468.7,235.5H84.7c-5.5,0-10-4.5-10-10s4.5-10,10-10h384c5.5,0,10,4.5,10,10s-4.5,10-10,10Z" />
                        <path class="st0"
                            d="M359.7,502.8c-63.4,0-115-51.6-115-115s51.6-115,115-115,115,51.6,115,115-51.6,115-115,115ZM359.7,292.7c-52.4,0-95,42.6-95,95s42.6,95,95,95,95-42.6,95-95-42.6-95-95-95Z" />
                        <path class="st0"
                            d="M358.6,426.5c-2.3,0-4.5-.8-6.4-2.3l-35-29.4c-4.2-3.5-4.8-9.9-1.2-14.1,3.5-4.2,9.9-4.8,14.1-1.2l27.5,23,43.2-50.1c3.6-4.2,9.9-4.6,14.1-1,4.2,3.6,4.6,9.9,1,14.1l-49.7,57.6c-2,2.3-4.8,3.5-7.6,3.5Z" />
                    </svg>
                </a>
            </div>
            <div class="menu-item">
                <a href={templates.SafeURL("/dia-diem-tu-van")} id="btn-location-id">
                    <svg id="dat-lich" viewBox="0 0 20 27" id="location-solid">
                        <path
                            d="M9.72999 0.490234C4.41999 0.490234 0.119995 4.79024 0.119995 10.1002C0.119995 10.9502 0.239995 11.7602 0.449995 12.5402C2.14 20.1402 9.73999 26.9602 9.73999 26.9602C9.73999 26.9602 17.33 20.1402 19.03 12.5402C19.24 11.7602 19.36 10.9402 19.36 10.1002C19.36 4.79024 15.06 0.490234 9.74999 0.490234H9.72999ZM9.72999 13.8902C7.63999 13.8902 5.95 12.2002 5.95 10.1102C5.95 8.02024 7.63999 6.33023 9.72999 6.33023C11.82 6.33023 13.51 8.02024 13.51 10.1102C13.51 12.2002 11.82 13.8902 9.72999 13.8902Z"
                            fill="currentColor" />
                    </svg>
                </a>
            </div>

        </div>
    </div>
    <div class="popup-menu-mobile" id="popup-menu-mobile" style="display: none;">
        <div class="overlay"></div>
        <div class="container-menu-mobile box-shadow-md" id="container-menu-mobile">
            <a href={templates.SafeURL("")}>
                <div class="logos">                 
                    <img src={templates.AssetURL("/asset/images/common/msd-hpv-logo.webp")} alt="hpv">
                </div>
            </a>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-menu__item click-mobile">
                    <div class="mobile mobile-menu">
                        <a href={templates.SafeURL("/chien-dich-toan-quoc")}>Chiến dịch toàn quốc</a>
                    </div>
                </li>
                <li class="nav-menu__item click-mobile">
                    <div class="mobile mobile-menu">
                        <a href={templates.SafeURL("/wearegenv")}>Gen V</a>
                    </div>
                </li>
                <li class="nav-menu__item click-mobile is-female">
                    <div class="mobile mobile-menu">
                        <a href={templates.SafeURL("/du-phong-hpv-cho-nu")}>Nữ giới</a>
                        <div class="toggle-btn">
                            <img src={templates.AssetURL("/asset/images/male/arrow-down.svg")} alt="down arrow">
                        </div>
                    </div>
                    <ul class="panel child-menu-mobile">
                        <li>
                            <a href={templates.SafeURL("/du-phong-hpv-cho-nu-tu-9-18-tuoi")} title="Du phong HPV nu 9-18 tuoi">
                                Trẻ vị thành niên 9 - 18 Tuổi
                            </a>
                        </li>
                        <li>
                            <a href={templates.SafeURL("/du-phong-hpv-cho-nu-tu-19-26-tuoi")} title="Du phong HPV nu 19-26 tuoi">
                                Thanh thiếu niên 19 - 26 Tuổi
                            </a>
                        </li>
                        <li>
                            <a href={templates.SafeURL("/du-phong-hpv-cho-nu-tu-27-45-tuoi")} title="Du phong HPV nu 27-45 tuoi">
                                Trưởng thành 27 - 45 Tuổi
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="nav-menu__item click-mobile is-male">
                    <div class="mobile mobile-menu">
                        <a href={templates.SafeURL("/du-phong-hpv-cho-nam")}>Nam giới</a>
                        <div class="toggle-btn">
                            <img src={templates.AssetURL("/asset/images/male/arrow-down.svg")} alt="down arrow">
                        </div>
                    </div>
                    <ul class="panel child-menu-mobile">
                        <li>
                            <a href={templates.SafeURL("/du-phong-hpv-cho-nam-tu-9-18-tuoi")} title="Du phong HPV nam 9-18 tuoi">
                                Trẻ vị thành niên 9 - 18 Tuổi
                            </a>
                        </li>
                        <li>
                            <a href={templates.SafeURL("/du-phong-hpv-cho-nam-tu-19-26-tuoi")} title="Du phong HPV nam 19-26 tuoi">
                                Thanh thiếu niên 19 - 26 Tuổi
                            </a>
                        </li>
                        <li>
                            <a href={templates.SafeURL("/du-phong-hpv-cho-nam-tu-27-45-tuoi")} title="Du phong HPV nam 27-45 tuoi">
                                Trưởng thành 27 - 45 Tuổi
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="nav-menu__item click-mobile">
                    <div class="mobile mobile-menu">
                       
                        <a href={templates.SafeURL("/dia-diem-tu-van")} id="menu-mobile-btn-location-id">Địa điểm tư vấn</a>
                    </div>
                </li>
            </ul>
            <div class="popup-menu-mobile__footer">
                <div class="content-menu-footer">
                    <span>Nội dung này được phối hợp biên soạn bởi Hội Y học Dự phòng Việt Nam và MSD, kiểm nhận bởi Hội
                        Y học Dự phòng Việt Nam và
                        MSD tài trợ cho mục tiêu giáo dục. VN-GSL-01368 22102026</span>
                    <span class="policy">
                        <a href="https://www.msd.com/our-commitment-to-accessibility-for-all/">Chính sách về Khả Năng
                            Tiếp Cận </a>
                        <a href="https://www.msdprivacy.com/vt/vt/">Điều khoản riêng tư</a>
                    </span>
                </div>
            </div>
        </div>
    </div>
}

