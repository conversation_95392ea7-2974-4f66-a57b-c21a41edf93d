package genv

import "webgo/pkg/gos/templates"
import "webgo/hpv/genvv3/transport/responses"
import "webgo/views/v3/genv/components"
import "webgo/views/v3/layouts"
import "webgo/views/v3/partials"

templ GenvV3(detail *responses.GenvV3Resp) {
    @layouts.MasterGenv(detail.Seo,[]templ.Component{head()}, scriptGenV()) {
        <body class="root-container">
            @components.GenvHeader()
            <main class="ldp-genv">
                @components.GenvMastheadLaunching(detail)
                // @components.Masthead()
                @components.GenvReason()
                @components.GenvConsultation()
                @components.GenvLocations()
                @components.GenvPolicyContent()
                @components.GenvFooter()
            </main>
        </body>
    }
}

templ head() {
    <link type="text/css" rel="stylesheet" href={templates.AssetURL("/asset/js/lib/wow-animate/animate.min.css")}>
    <link type="text/css" rel="stylesheet" href={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.css")}>
    <link type="text/css" rel="stylesheet" href={templates.AssetURL("/asset/css/components/fast-action-controls.css")}>
    <link type="text/css" rel="stylesheet" href={templates.AssetURL("/asset/css/pages/ldp_genv/section-masthead.css")}>
    <link type="text/css" rel="stylesheet" href={templates.AssetURL("/asset/css/pages/news/np_banner_mino.css")}>
    <link type="text/css" rel="stylesheet" href={templates.AssetURL("/asset/css/pages/ldp_genv/genv.css")}>
    @partials.ScriptLotame() 
}

templ scriptGenV() {
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const contentContainers = document.querySelectorAll('.content-policy');

            contentContainers.forEach((content, index) => {
                const showMoreButton = document.createElement('div');
                showMoreButton.classList.add('show-more');
                showMoreButton.innerHTML = '&#9660';
                content.parentNode.insertBefore(showMoreButton, content.nextSibling);

                showMoreButton.addEventListener('click', function () {
                    if (content.style.maxHeight) {
                        content.style.maxHeight = null;
                        showMoreButton.innerHTML = '&#9660';
                    } else {
                        content.style.maxHeight = content.scrollHeight + 'px';
                        showMoreButton.innerHTML = '&#9650';
                    }
                });
            });
        });
    </script>

    <!-- JS tracking GenV -->
    <script src="https://js.adsrvr.org/up_loader.1.1.0.js" type="text/javascript"></script>
    <script type="text/javascript">
        ttd_dom_ready( function() {
            if (typeof TTDUniversalPixelApi === 'function') {
                var universalPixelApi = new TTDUniversalPixelApi();
                universalPixelApi.init("3dc1drw", ["p3piyuk"], "https://insight.adsrvr.org/track/up");
            }
        });
    </script>

    <script>
        (function() {
            var video = document.getElementById('genv-masthead-video');
            function setVideoSource() {
        
                while (video.firstChild) {
                    video.removeChild(video.firstChild);
                }
                var src = window.innerWidth <= 992
                    ? "/asset/images/ldp-genv/Mobile_masthead.mp4"
                    : "/asset/images/ldp-genv/Desktop_masthead.mp4";
                var source = document.createElement('source');
                source.src = src;
                source.type = "video/mp4";
                video.appendChild(source);
                video.load();
            }
            setVideoSource();
            window.addEventListener('resize', setVideoSource);
        })();
    </script>
} 