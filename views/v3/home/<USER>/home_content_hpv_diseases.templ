package components

import "webgo/pkg/gos/templates"

templ ContentHpvDiseases() {
    <section class="common-hpv-diseases">
        <div class="common-hpv-diseases__wrapper">
            <div class="common-hpv-diseases__title">
                <div class="title-main">MỘT SỐ BỆNH, UNG THƯ PHỔ BIẾN</div>
                <div class="title-highlight">DO HPV GÂY RA <sup>(8)</sup></div>
                <div class="title-classify">
                    <div class="title-classify__tag male-tag" onclick="toggleActive(this,'male')" id="male-tag">
                        <div class="title-classify__gender">Ở NAM</div>
                        <div class="title-classify__icon">+</div>
                    </div>
                    <div class="title-classify__tag both-genders-tag active" onclick="toggleActive(this,'both-genders')"
                        id="both-genders-tag">
                        <div class="title-classify__gender">Ở 2 GIỚI</div>
                        <div class="title-classify__icon">+</div>
                    </div>
                    <div class="title-classify__tag female-tag" onclick="toggleActive(this,'female')" id="female-tag">
                        <div class="title-classify__gender">Ở NỮ</div>
                        <div class="title-classify__icon">+</div>
                    </div>
                </div>
            </div>
            <div class="common-hpv-diseases__contents">
                <div class="common-hpv-diseases__areas area-left">
                    <div class="common-hpv-diseases__statistics">
                        <div class="common-hpv-diseases__statistic statistic-both-genders active"
                            onclick="statisticToggleActive('both-genders')">
                            <div class="common-hpv-diseases__disease-percent">90%</div>
                            <div class="common-hpv-diseases__disease-name">MỤN CÓC SINH DỤC</div>
                        </div>
                        <div class="common-hpv-diseases__statistic statistic-both-genders active"
                            onclick="statisticToggleActive('both-genders')">
                            <div class="common-hpv-diseases__disease-percent">88%</div>
                            <div class="common-hpv-diseases__disease-name">UNG THƯ HẬU MÔN</div>
                        </div>
                        <div class="common-hpv-diseases__statistic statistic-male"
                            onclick="statisticToggleActive('male')">
                            <div class="common-hpv-diseases__disease-percent ">50%</div>
                            <div class="common-hpv-diseases__disease-name">UNG THƯ DƯƠNG VẬT</div>
                        </div>
                    </div>
                    <div class="common-hpv-diseases__button ">
                        <a class="btn-cta" href={templates.SafeURL("/du-phong-hpv-cho-nam")} title="du-phong-nam">DỰ PHÒNG CHO NAM</a>
                    </div>
                </div>
                <div class="common-hpv-diseases__areas">
                    <img  src={templates.AssetURL("/asset/images/new-home/diseases/common-hpv-model-nam-cropped.webp")}
                        alt="common hpv deseases model nam" class="common-hpv-diseases__image male active"
                        id="male-img">
                    <img src={templates.AssetURL("/asset/images/new-home/diseases/common-hpv-model-nu-cropped.webp")}
                        alt="common hpv deseases model nu" class="common-hpv-diseases__image female active"
                        id="female-img">
                </div>
                <div class="common-hpv-diseases__areas area-right">
                    <div class="common-hpv-diseases__statistics">
                        <div class="common-hpv-diseases__statistic statistic-both-genders active"
                            onclick="statisticToggleActive('both-genders')">
                            <div class="common-hpv-diseases__disease-percent">70%</div>
                            <div class="common-hpv-diseases__disease-name">UNG THƯ HẦU HỌNG</div>
                        </div>
                        <div class="common-hpv-diseases__statistic statistic-female"
                            onclick="statisticToggleActive('female')">
                            <div class="common-hpv-diseases__disease-percent">100%</div>
                            <div class="common-hpv-diseases__disease-name">UNG THƯ CỔ TỬ CUNG</div>
                        </div>
                        <div class="common-hpv-diseases__statistic statistic-female"
                            onclick="statisticToggleActive('female')">
                            <div class="common-hpv-diseases__disease-percent">78%</div>
                            <div class="common-hpv-diseases__disease-name">UNG THƯ ÂM ĐẠO</div>
                        </div>
                    </div>
                    <div class="common-hpv-diseases__button ">
                        <a class="btn-cta cta-secondary" href={templates.SafeURL("/du-phong-hpv-cho-nu")} title="du-phong-nu">DỰ PHÒNG
                            CHO NỮ</a>
                    </div>
                </div>
            </div>
            <div class="common-hpv-diseases__buttons mobile">
                <a class="btn-cta" href={templates.SafeURL("/du-phong-hpv-cho-nam")} title="du-phong-nam">DỰ PHÒNG CHO NAM</a>
                <a class="btn-cta cta-secondary" href={templates.SafeURL("/du-phong-hpv-cho-nu")} title="du-phong-nu">DỰ PHÒNG CHO NỮ</a>
            </div>
        </div>
    </section>
}