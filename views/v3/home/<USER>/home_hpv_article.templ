package components

import "webgo/hpv/homev3/transport/responses"
import "webgo/pkg/gos/templates"

templ HpvHomeArticles(postsNew []responses.HomeItem) {
  <section class="article">
    <div class="container">
      <div class="header-section">
        TÌM HIỂU THÊM
      </div>
      <div class="slide-article" id="male-19-26-slide-article">
        <div class="posts swiper-article swiper" id="article-list">
            <div class="swiper-wrapper">
                if len(postsNew) > 0 {
                    for _, post := range postsNew {
                        @HpvHomeArticlesItem(post)
                    }
                }            
            </div>
            <div class="control-navigate-pagination">
                <div class="swiper-button-prev"></div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-next"></div>
            </div>
        </div>
      </div>
    </div>
  </section>
}

templ HpvHomeArticlesItem(post responses.HomeItem) {
    <div class="swiper-slide post-item article-item box-shadow-sm">
        <div class="article-item__title ">
            <span>{ post.Title }</span>
        </div>
        <div class="article-item__avatar">
            <img src={ templates.ImgURL( post.Img )} alt={post.Title} />
        </div>
        <div class="article-item__action">
            <a href={templates.SafeURL( post.PagePath )} title={ post.Title }>
            Xem chi tiết
            </a>
        </div>
    </div>
}