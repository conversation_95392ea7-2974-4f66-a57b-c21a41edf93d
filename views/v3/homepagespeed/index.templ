package homepagespeed

import "webgo/pkg/gos/templates"
import "webgo/views/v3/home/<USER>"
import "webgo/hpv/homev3/transport/responses"
import "webgo/views/v3/layouts"

templ HomePagespeedHTML(datas *responses.HomeListResp) {
    @layouts.MasterPagespeed(datas.Seo,[]templ.Component{head()}, footerPSHome(datas), homePageSpeedJS()) {
        <main class="container container-content">
            <div class="main-container box-shadow-md" id="main-container">
                <div class="banner-main">
                    <picture>
                        <source srcset={templates.AssetURL("/asset/images/new-home/header-banner-desktop-1599px.webp")}
                            media="(max-width:1399px)">
                        <img class="banner-main__desktop"
                            src={templates.AssetURL("/asset/images/new-home/header-banner-desktop.webp")} loading="eager"
                            fetchpriority="high" title="banner hpv" alt="Banner HPV Desktop" />
                    </picture>
                    <picture>
                        <source srcset={templates.AssetURL("/asset/images/new-home/header-banner-mobile-640px.webp")}
                            media="(max-width:520px)">
                        <img class="banner-main__mobile"
                            src={templates.AssetURL("/asset/images/new-home/header-banner-mobile.webp")} loading="eager"
                            fetchpriority="high" title="banner hpv mobile" alt="Banner HPV Mobile" />
                    </picture>
                    <div class="male-box__cta cta-kv open-popup-locations" id="h1_clinic_id"
                        title="Tham vấn với chuyên gia y tế">
                        <span>Tham vấn với chuyên gia y tế</span>
                        <img src={templates.AssetURL("/asset/images/male/arrow-right.svg")} alt="Arrow right">
                    </div>
                </div>
                <div class="body-main-content">
                    <div class="main-content" id="body-main-content">
                        @components.ContentHpvInfo()
                        @components.ContentHpvFact()
                        @components.ContentHpvDiseases()
                        @components.ContentHpvInfection()
                        @components.ContentHpvPreventative()
                        @components.HomeSidebarMobile()
        
                        if datas != nil && len(datas.HomeNew) > 0{
                            @components.HpvHomeArticles(datas.HomeNew)
                        }
        
                    </div>
                    @components.HomeFooterContent()
                </div>
            </div>
            @components.HomeSidebar()
        </main>
    }
}

templ head() {
    <link rel="stylesheet" href={ templates.AssetURL("/asset/js/lib/wow-animate/animate.min.css") }>
    <link rel="stylesheet" href={ templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.css") }>	
    <link rel="stylesheet" href={ templates.AssetURL("/asset/css/pages/home/<USER>") }>
    <link rel="stylesheet" href={ templates.AssetURL("/asset/css/components/modal_pop_up_moh.css") }>             
    <link rel="preload" as="image" href={ templates.AssetURL("/asset/images/new-home/header-banner-desktop.webp") } type="image/webp">
}

func footerPSHome(datas *responses.HomeListResp) templ.Component{
    if banner, ok := datas.Options["footer-home"]; ok {
        return templ.Raw(banner)
    }
    return nil
}

templ homePageSpeedJS() {
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const lazyImages = document.querySelectorAll("img.lazyload");
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        if (img.dataset.srcset) {
                            img.srcset = img.dataset.srcset;
                        }
                        img.classList.remove("lazyload");
                        observer.unobserve(img);
                    }
                });
            });

            lazyImages.forEach((img) => {
                imageObserver.observe(img);
            });
        });


        document.querySelectorAll(".open-popup-locations").forEach(button => {
            button.addEventListener("click", () => {
                document.querySelector("#btn-location-round-id").click()
            });
        });
    </script>
    <script>
        window.addEventListener('load', function () {
            const banner = document.querySelector('.banner-main');
            banner.classList.add('wow', 'animate__pulse');
            banner.setAttribute('data-wow-duration', '1.5s');
            banner.setAttribute('data-wow-delay', '0s');
        });
    </script>
    <script type="text/javascript" src={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.min.js") }></script>
    <script src={templates.AssetURL("/asset/js/pages/new-home.js") }></script>
}
