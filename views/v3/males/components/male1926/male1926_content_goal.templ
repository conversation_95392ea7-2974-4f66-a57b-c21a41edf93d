package male1926

import "webgo/pkg/gos/templates"

templ Male1926ContentGoal() {
    <section class="container goal">
        <div class="swiper swiper-container-goal">
            <div class="boder-bg-slide">
                <div class="slide-to-show">
                    <svg style="transform: rotate(180deg);" class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#read-more-line-icon" />
                    </svg>
                    Lướt để xem
                </div>
            </div>
            <div class="swiper-wrapper">
                <div class="swiper-slide slide-1">
                    <div class="content-think-area">
                    <div class="pop-up-think right">Đ<PERSON>u là những mục tiêu <br> bạn luôn mơ <PERSON>ớc?</div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-19-26/male.webp")} alt="suy nghi">
                    </div>
                </div>
                <div class="swiper-slide slide-2">
                    <h2 class="title-slide">
                    Đ<PERSON><PERSON> là những mục tiêu <br> bạn luôn mơ ước?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think left">Đầu quân<br> tập đoàn lớn</div>
                    <div class="pop-up-think right">Lương<br> ngàn đô</div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-19-26/2-male.webp")} alt="duong toi thanh cong">
                    </div>
                </div>
                <div class="swiper-slide slide-3">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu <br> bạn luôn mơ ước?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">Độc lập<br> tài chính</div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-19-26/2-male-slide-3.webp")} alt="doc lap tai chinh">
                    </div>
                </div>
                <div class="swiper-slide slide-4">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu <br> bạn luôn mơ ước?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">Du lịch<br> nước ngoài</div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-19-26/2-male-slide-4.webp")} alt="du lich nuoc ngoai">
                    </div>
                </div>
                <div class="swiper-slide slide-5">
                    <h2 class="title-slide">
                    Đâu là những mục tiêu <br> bạn luôn mơ ước?
                    </h2>
                    <div class="content-think-area">
                    <div class="pop-up-think right">Lấy bằng<br> thạc sĩ/ tiến sĩ</div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-19-26/2-male-slide-5.webp")} alt="tot nghiep">
                    </div>
                </div>
                <div class="swiper-slide slide-6">
                    <div class="content-think-area">
                    <div class="pop-up-think left " style="font-weight: 400;">Để vươn đến những mục tiêu xa,<br>
                        đừng bỏ quên sức khỏe</div>
                    <div class="pop-up-think right">
                        <span>Chủ động</span>
                        <span class="note">DỰ PHÒNG<br> HPV NGAY</span>
                        <span>để có thể thực hiện<br> những điều trên</span>
                        <a href="#result-section" class="now-btn">Tìm hiểu ngay về HPV</a>
                    </div>
                    </div>
                    <div class="img-swip-area">
                    <img src={templates.AssetURL("/asset/images/male-19-26/2-per-slide-6.webp")} alt="du phong ngay">
                    </div>
                </div>
            </div>
            <div class="control-navigate-pagination">
                <div class="action-navigate">
                    <span class="btn-play">
                    <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#play-solid-icon" />
                    </svg>
                    </span>
                    <span class="btn-pause active">
                        <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <use xlink:href="#pause-solid-icon" />
                        </svg>
                    </span>
                </div>
                <div class="swiper-pagination bg-transparent"></div>
            </div>
        </div>
    </section>
}
