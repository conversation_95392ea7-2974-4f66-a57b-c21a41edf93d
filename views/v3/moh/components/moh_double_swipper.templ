package components

import "webgo/pkg/gos/templates"
import "webgo/hpv/mohV3/transport/responses"

templ MohDoubleSwipper(posts []responses.MohItem) {
    <div class="slide-result news-swiper" id="newsSwiper">
            <div class="posts swiper-result swiper" id="result-list">
                <div class="swiper-wrapper">
                if len(posts) > 0 {
                    for _, post := range posts {
                        @MohDoubleSwipperItem(post)
                    }
                }          
                </div>
                <div class="control-navigate-pagination">
                    <div class="action-navigate">
                        <span class="btn-play">
                            <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <use xlink:href="#play-solid-icon" />
                            </svg>
                        </span>
                        <span class="btn-pause active">
                            <svg class="icon" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <use xlink:href="#pause-solid-icon" />
                            </svg>
                        </span>
                    </div>
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-pagination bg-transparent"></div>
                    <div class="swiper-button-next"></div>
                </div>
            </div>
        </div>
}

templ MohDoubleSwipperItem(post responses.MohItem) {
    <div class="card swiper-slide result-item" data-swiper-autoplay="15000">
        <div class="content-top"></div>
        <img class="img-bg-card2" src={ templates.ImgURL(post.Img) } alt={ post.Title }>
        <a class="card-content" href={ templates.SafeURL(post.PagePath)} title={ post.Title }>
            <div class="card-text"> 
            @templ.Raw( post.Description ) 
            </div>
        </a>
    </div>  
}