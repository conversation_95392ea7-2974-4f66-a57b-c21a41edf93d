package moh

import "webgo/views/v3/layouts"
import "webgo/views/v3/moh/components"
import "webgo/pkg/gos/templates"
import "webgo/hpv/mohV3/transport/responses"
import "webgo/hpv/entity"

templ MohV3(seo *entity.Seo, datas *responses.MohListResp) {
    @layouts.Master(seo,[]templ.Component{head()}, footerMoh(datas.Options), scriptMohV2()) {
        <div class="bg-moh-ldp">
            <picture>
                <source srcset={templates.AssetURL("/asset/images/ldp-moh-v2/bg-moh-ldp-v2-mobile-5.webp")} media="(max-width: 575px)">
                <source srcset={templates.AssetURL("/asset/images/ldp-moh-v2/bg-moh-ldp-v2-mobile-2.webp")} media="(max-width: 768px)">
                <source srcset={templates.AssetURL("/asset/images/ldp-moh-v2/bg-moh-ldp-v2-small.webp")} media="(max-width: 1599px)">
                <img src={templates.AssetURL("/asset/images/ldp-moh-v2/bg-moh-ldp-v2.webp")} alt="background home City">
            </picture>
        </div>
        <main>
            @components.MohBannerKV()
            @components.MohAverageCount()
            @components.MohVideoEmbed()
            @components.MohCampaignInfo()
            @components.MohDetailSwipper()

            <section class="result" id="result-section">
                <div class="container" style="padding:0;">
                    <div class="tabs">
                        <div class="tab active" id="newsTab"><p>Tin tức</p></div>
                        <div class="tab" id="campaignTab"><p>Hoạt động chiến dịch</p></div>
                    </div>
                    if datas != nil && len(datas.MohNew) > 0{
                        @components.MohDoubleSwipper(datas.MohNew)                
                    }
                </div>
            </section>

            if datas != nil && len(datas.Options) >0{
                @showBannerMoh(datas.Options,"moh-banner-v3")
            }

            @components.MohDetailSwipperScript()
        </main>
    }
}

templ head() {
    <link rel="stylesheet" href={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/components/fast-action-controls.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/css/pages/ldp_moh_v2/moh_v2.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/asset/js/lib/wow-animate/animate.min.css")}>
}

templ scriptMohV2() {
    <script>
            document.querySelectorAll(".btn-tick-prevent, .tick-border, .btn-tick-prevent-cta").forEach((button) => {
                button.addEventListener("click", function (e) {
                    const parent = this.closest(".group-tick");
                    if (parent) {
                        const line = parent.querySelector(".line-moh");
                        const hand = parent.querySelector(".hand-img");
                        const btn = parent.querySelector(".btn-tick-prevent");
                        const btn2 = parent.querySelector(".btn-tick-prevent-cta");

                        if (line) line.style.clipPath = "inset(0 0 0 0)";
                        if (hand) hand.style.opacity = "1";
                        if (btn) btn.classList.add("active"); 
                        if (btn2) btn2.classList.add("active"); 
                    }
                });
            });

            const tickBtnMoh = document.getElementById("btn-tick-border-prevent-ldp-moh");
            const preventBtnMoh = document.getElementById("btn-tick-prevent-ldp-moh");

            const tickBtnMohTop = document.getElementById("btn-tick-border-prevent-ldp-moh-top");
            const preventBtnMohTop = document.getElementById("btn-tick-prevent-ldp-moh-top");

            const handleBtnClick = () => {
                setTimeout(() => {
                    window.location.href = "https://hpv.vn/dia-diem-tu-van";
                }, 1000);
            };

            if (tickBtnMoh && preventBtnMoh) {
                [tickBtnMoh, preventBtnMoh].forEach((btn) => {
                    btn.addEventListener("click", handleBtnClick);
                });
            }

            if (tickBtnMohTop && preventBtnMohTop) {
                [tickBtnMohTop, preventBtnMohTop].forEach((btn) => {
                    btn.addEventListener("click", handleBtnClick);
                });
            }
    </script>
       
    <script type="text/javascript">
        (() => {
            const scriptElement = document.createElement('script');
            scriptElement.id = "js-load-lib-swiper"
            document.body.appendChild(scriptElement);
        })()
    </script>


    <script type="text/javascript">
    window.addEventListener("DOMContentLoaded", () => {
        const swiperWrapper = document.getElementById("result-list");
        const swiperSlides = swiperWrapper.getElementsByClassName("swiper-slide");
        var swiperResult = new Swiper('#result-list', {
            effect: "coverflow",
            grabCursor: true,       
            coverflowEffect: {
                rotate: 0,
                stretch: 0,
                depth: 50,
                modifier: 0.5,
                scale: 0.8,
                slideShadows: true,
            }, 
            centeredSlides: true,
            loop: true,
            slidesPerView: 1,
            spaceBetween: 50,    
            lazy: {
                loadPrevNext: true,
                loadPrevNextAmount: 2,
                loadOnTransitionStart: true,
            },
            breakpoints: {
                575: {
                    slidesPerView: 1,
                    spaceBetween: 50
                },
                800: {
                    slidesPerView: 3,
                    spaceBetween: 20
                },
                1100: {
                    slidesPerView: 3,
                    spaceBetween: 30
                },
            },
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                renderBullet: function (index, className) {
                    return `<span class="${className}"></span>`;
                },
            },
    on: {
        slideChange: function () {
        const realIndex = this.realIndex;
        const bullets = document.querySelectorAll('.swiper-pagination span');
        bullets.forEach((b, i) => {
            b.classList.toggle('swiper-pagination-bullet-active', i === realIndex);
        });
        },
        init: function () {
            setTimeout(() => {
            this.update();
            this.slideTo(this.activeIndex, 0);
            }, 50);
        }
    },
        });

        swiperResult.on('click', function (swiperResult, event) {
            const slideClicked = swiperResult.clickedSlide
            var realIndex = slideClicked && slideClicked.getAttribute('data-swiper-slide-index');
            if (realIndex !== null && !slideClicked.className.includes('swiper-slide-active')) {
                swiperResult.slideToLoop(realIndex);
            }
        });

        swiperResult.el.addEventListener('mouseenter', () => {
            swiperResult.autoplay.pause();
        });

        swiperResult.el.addEventListener('mouseleave', () => {
            swiperResult.autoplay.resume();
        });

        const btnPlay = swiperResult.el.querySelector('.btn-play')
        const btnPause = swiperResult.el.querySelector('.btn-pause')
        btnPause.onclick = () => {
            btnPlay.classList.toggle('active')
            btnPause.classList.toggle('active')
            swiperResult.autoplay.stop();

        }
        btnPlay.onclick = () => {
            btnPlay.classList.toggle('active')
            btnPause.classList.toggle('active')
            swiperResult.autoplay.start();
        }
    })

    document.getElementById('newsTab').addEventListener('click', () => {
        document.getElementById('newsTab').classList.add('active');
        document.getElementById('campaignTab').classList.remove('active');
        document.getElementById('newsSwiper').style.display = 'block';
        document.getElementById('campaignSwiper').style.display = 'none';
    });

    document.getElementById('campaignTab').addEventListener('click', () => {
        document.getElementById('campaignTab').classList.add('active');
        document.getElementById('newsTab').classList.remove('active');
        document.getElementById('newsSwiper').style.display = 'none';
        document.getElementById('campaignSwiper').style.display = 'block';
    });
    </script>

    <script type="text/javascript" src={templates.AssetURL("/asset/js/lib/swiper/swiper-bundle.js")}></script>
    <script type="text/javascript" src={templates.AssetURL("/asset/js/components/fast-action-controls.js")}></script>   
    <script src="https://www.youtube.com/iframe_api"></script>
}

func showBannerMoh(options map[string]string, key string) templ.Component{
    if banner, ok := options[key]; ok {
        return templ.Raw(banner)
    }
    return templ.Raw("")
}

func footerMoh(options map[string]string) templ.Component{
    if banner, ok := options["footer-moh"]; ok {
        return templ.Raw(banner)
    }
    return nil
}