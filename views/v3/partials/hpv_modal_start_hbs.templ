package partials

templ HPVModalStart() {
	<div class="modal-start-hbs" id="modal-start-hbs">
		<div class="overlay"></div>
		<div class="modal-container">
			<div class="modal-content">
				<div class="ribbon-white"></div>
				<div class="title">
					<div class="title__left">
						Xin chào!
						<br/>
						<PERSON><PERSON><PERSON> chọn để tiếp tục
					</div>
					<div class="test-hbs">
						<a id="pixel-yes-yes" href="javascript:void(0)" onclick="console.log('Click: Đ<PERSON> biết - <PERSON>ã có')" height="1" width="1" style="border-style:none;"></a>
						<a id="pixel-yes-no" href="javascript:void(0)" onclick="console.log('Click: Đã biết - Chưa có')" height="1" width="1" style="border-style:none;"></a>
						<a id="pixel-no-yes" href="javascript:void(0)" onclick="console.log('Click: Chưa biết - <PERSON><PERSON> có')" height="1" width="1" style="border-style:none;"></a>
						<a id="pixel-no-no" href="javascript:void(0)" onclick="console.log('Click: Chưa biết - Chưa có')" height="1" width="1" style="border-style:none;"></a>
					</div>
				</div>
				<div class="content">
					<div class="questions">
						<div class="question-item" id="know-hpv">
							<p>
								Bạn đã biết HPV
								trước đây chưa?
							</p>
							<div class="item-selects">
								<input hidden type="radio" id="know-hpv-yes" name="know-hpv" value="1"/>
								<label for="know-hpv-yes">Đã biết</label>
								<input hidden type="radio" id="know-hpv-no" name="know-hpv" value="0"/>
								<label for="know-hpv-no">chưa biết</label>
							</div>
						</div>
						<div class="question-item" id="injected-hpv">
							<p>
								Bạn đã có kế hoạch dự phòng
								HPV chưa?
							</p>
							<div class="item-selects">
								<input hidden type="radio" id="injected-hpv-yes" name="injected-hpv" value="1"/>
								<label for="injected-hpv-yes">Đã có</label>
								<input hidden type="radio" id="injected-hpv-no" name="injected-hpv" value="0"/>
								<label for="injected-hpv-no">chưa có</label>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="thank-you">
				<p>Cảm ơn</p>
				<p>Bạn đã lựa chọn thành công!</p>
			</div>
		</div>
	</div>
    <script>
        const getHBS = () => {
            const match = document.cookie.match(/(^| )hpvquestion=([^;]+)/);
            return match ? match[2] : "";
        };

        function saveHBSAnswers(answers, reportHSB) {
            // Lưu kết quả vào cookie
            document.cookie = `hpvquestion=${String(answers.knownHpv) + String(answers.injectHpv)
                }; Max-Age=15768000`;

            // Hiển thị thông báo cảm ơn
            const tk = reportHSB.querySelector(".thank-you");
            if (tk) {
                tk.style.display = "block";
                setTimeout(() => {
                    tk.classList.add("active");
                }, 100);
            }
            setTimeout(() => {
                reportHSB.style.display = "none";
            }, 2000);
        }

        function checkReportHSB() {
            if (!getHBS()) {
                const reportHSB = document.querySelector(
                    ".modal-start-hbs#modal-start-hbs"
                );

                if (reportHSB) {
                    reportHSB.style.display = "flex";
                    setTimeout(() => reportHSB.classList.add("active"), 2000);

                    const answers = { knownHpv: undefined, injectHpv: undefined };
                    const handleChange = (type, value) => {
                        answers[type] = value;
                        if (answers.knownHpv !== undefined && answers.injectHpv !== undefined) {
                            saveHBSAnswers(answers, reportHSB);
                        }
                    };

                    reportHSB
                        .querySelectorAll(".question-item#know-hpv input[name='know-hpv']")
                        .forEach((item) =>
                            item.addEventListener("change", () =>
                                handleChange("knownHpv", item.value)
                            )
                        );

                    reportHSB
                        .querySelectorAll(".question-item#injected-hpv input[name='injected-hpv']")
                        .forEach((item) =>
                            item.addEventListener("change", () =>
                                handleChange("injectHpv", item.value)
                            )
                        );
                }
            }
        }

        window.addEventListener("DOMContentLoaded", function () {
            checkReportHSB();
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const radios = {
                knowYes: document.getElementById('know-hpv-yes'),
                knowNo: document.getElementById('know-hpv-no'),
                injectedYes: document.getElementById('injected-hpv-yes'),
                injectedNo: document.getElementById('injected-hpv-no'),
            };

            const pixels = {
                '1-1': document.getElementById('pixel-yes-yes'),
                '1-0': document.getElementById('pixel-yes-no'),
                '0-1': document.getElementById('pixel-no-yes'),
                '0-0': document.getElementById('pixel-no-no'),
            };

            function handleSelection() {
                const know = radios.knowYes.checked ? '1' : (radios.knowNo.checked ? '0' : null);
                const injected = radios.injectedYes.checked ? '1' : (radios.injectedNo.checked ? '0' : null);

                if (know !== null && injected !== null) {
                    const key = `${know}-${injected}`;
                    const pixel = pixels[key];
                    if (pixel) {
                        pixel.click();
                    }
                }
            }

            // Lắng nghe thay đổi cả hai nhóm radio
            ['know-hpv', 'injected-hpv'].forEach(name => {
                document.querySelectorAll(`input[name="${name}"]`).forEach(input => {
                    input.addEventListener('change', handleSelection);
                });
            });
        });
    </script>
}
