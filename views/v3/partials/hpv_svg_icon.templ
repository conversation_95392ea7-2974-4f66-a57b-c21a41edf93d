package partials

templ HPVSvgIcon() {
    <div style="display: none;">
    <!-- Shape Disease Routes Item -->
    <svg id="shape-disease-routes-item" width="1em" height="1em" viewBox="0 0 132 63" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M116 17.7182H15.3211L0 2.47673V51.8794C0 57.8001 4.8127 62.5924 10.7587 62.5924H120.523C126.469 62.5924 131.282 57.8001 131.282 51.8794V0L116 17.7182Z"
            fill="#169787" />
    </svg>

    <!-- Search Outline Icon -->
    <svg id="search" viewBox="0 0 24 24">
        <path
            d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14">
        </path>
    </svg>

    <!-- Home Outline Icon  -->
    <svg id="home-outline" viewBox="0 0 25 21">
        <path
            d="M23.3188 20.2663H1.4769C0.900538 20.2663 0.435181 19.7989 0.435181 19.22V6.12959C0.435181 5.69653 0.704149 5.30635 1.10547 5.15199L12.0819 0.975735C12.321 0.885693 12.5857 0.885693 12.8205 0.975735L23.6817 5.15199C24.083 5.30635 24.352 5.69653 24.352 6.12959V19.22C24.352 19.7989 23.8867 20.2663 23.3103 20.2663H23.3188ZM2.51862 18.1781H22.2814V6.85422L12.4576 3.07673L2.52289 6.85851V18.1824L2.51862 18.1781Z"
            fill="currentColor" />
    </svg>

    <!-- 3 Dots Icon -->
    <svg id="3-dots" viewBox="0 0 17 4">
        <circle cx="2" cy="2" r="2" fill="currentColor" />
        <circle cx="9" cy="2" r="2" fill="currentColor" />
        <circle cx="15" cy="2" r="2" fill="currentColor" />
    </svg>

    <!-- Female Outline Icon  -->
    <svg id="female-outline" viewBox="0 0 18 27">
        <path
            d="M17.8184 9.04381C17.8184 4.25441 13.959 0.36972 9.19011 0.36972C4.42126 0.36972 0.578857 4.25441 0.578857 9.04381C0.578857 13.413 3.81502 17.0276 8.00751 17.6278V20.4835H5.98811C5.30502 20.4835 4.77135 21.0109 4.77135 21.6969C4.77135 22.3872 5.30502 22.9489 5.98811 22.9489H8.00751V25.4015C8.00751 26.0875 8.5241 26.6407 9.20719 26.6407C9.89028 26.6407 10.4069 26.0875 10.4069 25.4015V22.9489H12.4092C13.088 22.9489 13.643 22.3915 13.643 21.7055C13.643 21.0237 13.088 20.4792 12.4092 20.4792H10.4069V17.6236C14.6591 17.0233 17.8227 13.4087 17.8227 9.03952L17.8184 9.04381ZM9.19438 15.2396C5.79172 15.2396 3.02519 12.4611 3.02519 9.04381C3.02519 5.62648 5.79172 2.84803 9.19438 2.84803C12.597 2.84803 15.3636 5.62648 15.3636 9.04381C15.3636 12.4611 12.597 15.2396 9.19438 15.2396Z"
            fill="currentColor" />
    </svg>

    <!-- Male Outline Icon  -->
    <svg id="male-outline" viewBox="0 0 24 23">
        <path
            d="M22.8136 0.457871C22.7795 0.432145 22.7496 0.406419 22.7154 0.380693C22.6984 0.367829 22.6813 0.359254 22.6642 0.346391C22.5916 0.303513 22.5148 0.264924 22.4379 0.23491C22.3995 0.222046 22.3568 0.209183 22.3141 0.19632C22.2117 0.170593 22.1007 0.153442 21.9897 0.153442H15.21C14.4884 0.153442 13.8523 0.727999 13.8523 1.45263C13.8523 2.18154 14.4799 2.78611 15.2014 2.78611L18.8645 2.80326L15.3295 6.36208C11.739 3.65652 6.64993 3.93951 3.39669 7.20676C-0.168203 10.787 -0.176737 16.584 3.38816 20.16C6.95306 23.7403 12.7252 23.736 16.2901 20.1557C19.5433 16.8885 19.7909 11.7389 17.1397 8.18008L20.6832 4.62126V8.27012C20.6832 8.99475 21.2681 9.58217 21.9897 9.58217C22.7112 9.58217 23.2961 8.99475 23.2961 8.27441V1.46549C23.2961 1.05815 23.1125 0.693697 22.8222 0.453584L22.8136 0.457871ZM14.4372 18.3077C11.8969 20.8589 7.75996 20.8589 5.21544 18.3077C2.67518 15.7565 2.67518 11.6017 5.21544 9.0462C7.75569 6.495 11.8927 6.495 14.4372 9.0462C16.9774 11.5974 16.9774 15.7522 14.4372 18.3077Z"
            fill="currentColor" />
    </svg>

    <!-- Location Solid Icon -->
    <svg viewBox="0 0 20 27" id="location-solid">
        <path
            d="M9.72999 0.490234C4.41999 0.490234 0.119995 4.79024 0.119995 10.1002C0.119995 10.9502 0.239995 11.7602 0.449995 12.5402C2.14 20.1402 9.73999 26.9602 9.73999 26.9602C9.73999 26.9602 17.33 20.1402 19.03 12.5402C19.24 11.7602 19.36 10.9402 19.36 10.1002C19.36 4.79024 15.06 0.490234 9.74999 0.490234H9.72999ZM9.72999 13.8902C7.63999 13.8902 5.95 12.2002 5.95 10.1102C5.95 8.02024 7.63999 6.33023 9.72999 6.33023C11.82 6.33023 13.51 8.02024 13.51 10.1102C13.51 12.2002 11.82 13.8902 9.72999 13.8902Z"
            fill="currentColor" />
    </svg>

    <!-- Zalo Outline Icon -->
    <svg id="zalo-outline" viewBox="0 0 26 29">
        <path
            d="M15.2849 2.4226H7.02796C6.4943 2.4226 6.00759 2.51264 5.56358 2.69701C5.11957 2.88139 4.70972 3.16009 4.33828 3.53741C4.18459 3.69177 4.05223 3.85041 3.93269 4.01335C3.25814 4.03907 2.6092 4.14198 1.98588 4.31778C2.00295 4.26632 2.02431 4.21916 2.04565 4.17199C2.31035 3.52455 2.70314 2.94141 3.22399 2.41831C3.74485 1.89521 4.32549 1.50073 4.96589 1.2349C5.60202 0.964768 6.28939 0.831848 7.02372 0.831848H19.0205C19.7549 0.831848 20.4422 0.964768 21.0826 1.23061C21.7273 1.49645 22.3079 1.89092 22.8288 2.41402C23.3539 2.94141 23.7424 3.52454 24.0071 4.1677C24.2718 4.81086 24.4042 5.50119 24.4042 6.23868V6.26012H22.8203V6.23868C22.8203 5.70271 22.7306 5.21391 22.547 4.76799C22.3634 4.32206 22.0859 3.91044 21.7145 3.53741C21.3345 3.1558 20.9289 2.88139 20.4892 2.69701C20.0495 2.51693 19.5585 2.4226 19.0248 2.4226H15.2849ZM19.9256 9.80179H25.083V11.0581L22.2738 13.9266H25.2538V15.393H19.7079V14.0038L22.3848 11.2725H19.9214V9.80608L19.9256 9.80179ZM16.6639 7.68365H18.7985V9.13719H16.6639V7.68365ZM16.6639 9.80179H18.7985V15.393H16.6639V9.80179ZM15.2891 15.393H13.2953V14.4926C12.9965 14.8613 12.7019 15.1272 12.3988 15.2858C12.0957 15.4444 11.7285 15.5216 11.2888 15.5216C10.7039 15.5216 10.2428 15.3458 9.90977 14.9899C9.57676 14.6384 9.41026 14.0938 9.41026 13.3606V9.80179H11.5535V12.8804C11.5535 13.232 11.6175 13.4807 11.7456 13.6264C11.8737 13.7765 12.053 13.8451 12.2878 13.8451C12.5397 13.8451 12.7446 13.7465 12.9111 13.5493C13.0734 13.352 13.1545 13.0004 13.1545 12.4902V9.79321H15.2891V15.3844V15.393ZM7.46344 14.261C7.75803 14.4668 7.95014 14.5998 8.04407 14.6512C8.17642 14.7284 8.36001 14.8184 8.59055 14.9213L7.93733 16.2548C7.60432 16.0919 7.27559 15.9032 6.95539 15.6803C6.63092 15.4573 6.40466 15.2901 6.27658 15.1786C5.75145 15.4059 5.09822 15.5216 4.3084 15.5216C3.14287 15.5216 2.2207 15.2172 1.54615 14.604C0.747782 13.8837 0.350739 12.8718 0.350739 11.564C0.350739 10.2563 0.700803 9.3087 1.39671 8.60551C2.09261 7.90232 3.06603 7.55073 4.31694 7.55073C5.56786 7.55073 6.57541 7.89375 7.26704 8.57978C7.95868 9.26582 8.30876 10.2477 8.30876 11.5255C8.30876 12.666 8.03127 13.575 7.47199 14.2567L7.46344 14.261ZM5.6447 13.039C5.83682 12.7003 5.93074 12.1943 5.93074 11.5126C5.93074 10.7365 5.7856 10.1791 5.49956 9.84467C5.20924 9.51022 4.81217 9.34729 4.30412 9.34729C3.83023 9.34729 3.44599 9.5188 3.15567 9.85753C2.86109 10.1963 2.71594 10.7279 2.71594 11.4483C2.71594 12.2887 2.8611 12.8804 3.14715 13.2191C3.43746 13.5578 3.82598 13.7294 4.32549 13.7294C4.48773 13.7294 4.63716 13.7122 4.78231 13.6822C4.58165 13.4892 4.26998 13.3092 3.83451 13.1334L4.20594 12.2758C4.41513 12.3144 4.58165 12.3616 4.69692 12.4173C4.81646 12.473 5.04273 12.6188 5.38428 12.8589C5.4654 12.9147 5.55078 12.9747 5.6447 13.0305V13.039ZM3.51858 19.0419C3.69789 19.4621 3.97112 19.8565 4.33401 20.2167L4.36817 20.2553C4.73107 20.6112 5.13238 20.8813 5.56358 21.0571C6.00332 21.2372 6.49005 21.3315 7.02372 21.3315H10.5715C11.0113 21.3315 11.3614 21.6874 11.3614 22.1247C11.3614 22.2191 11.3443 22.3091 11.3144 22.3949C11.1863 22.8965 11.0369 23.3939 10.8704 23.8741C10.6953 24.3801 10.5075 24.8689 10.2983 25.332C10.2086 25.5421 10.1062 25.7436 9.99088 25.9494C10.6782 25.6364 11.3229 25.2805 11.9249 24.8817C12.5909 24.4401 13.2057 23.9556 13.7735 23.4239C14.3499 22.8837 14.8708 22.292 15.3489 21.6574C15.5026 21.4516 15.7417 21.3401 15.9808 21.3401H19.0248C19.5585 21.3401 20.0452 21.25 20.4849 21.0657C20.9289 20.8813 21.3431 20.6026 21.7188 20.2253C22.0902 19.8522 22.3677 19.4406 22.5513 18.9947L22.5684 18.9518H24.2291C24.1694 19.1748 24.1011 19.3892 24.0114 19.5993C23.7467 20.2424 23.3539 20.8298 22.8288 21.3529C22.3037 21.876 21.723 22.2705 21.0826 22.5364C20.4422 22.8022 19.7549 22.9351 19.0248 22.9351H16.365C15.8996 23.5311 15.3959 24.0799 14.8537 24.5902C14.2133 25.1948 13.5259 25.7393 12.7916 26.2238C12.053 26.7126 11.2632 27.1414 10.4264 27.5058C9.59384 27.8703 8.70583 28.179 7.76658 28.4234C7.48907 28.4963 7.18166 28.4106 6.97673 28.179C6.68642 27.8531 6.72059 27.3515 7.04506 27.0599C7.46345 26.6869 7.81781 26.3139 8.11239 25.9365L8.14653 25.8894C8.44538 25.5035 8.6802 25.109 8.85098 24.7145V24.7059C9.04737 24.2686 9.22241 23.827 9.38037 23.3725C9.4316 23.2267 9.47858 23.0809 9.52554 22.9351H7.02372C6.28939 22.9351 5.60202 22.8022 4.96589 22.5364C4.34257 22.2791 3.779 21.8975 3.26668 21.4001C3.2496 21.3872 3.23252 21.3701 3.21545 21.3572C2.69459 20.8341 2.3018 20.251 2.03711 19.6035C1.92183 19.3206 1.8322 19.029 1.76389 18.7246C2.3189 18.8875 2.9038 18.9947 3.51858 19.0461V19.0419Z"
            fill="currentColor" />
    </svg>

    <!-- Chat cloud Icon -->
    <svg id="chat-could" viewBox="0 0 42 38" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M20.1901 14.2971H36.0623C39.0061 14.2971 41.3969 16.688 41.3969 19.6317V27.9575C41.3969 30.8644 39.064 33.2289 36.1729 33.2869L35.1618 38.0001L29.4638 33.2921H20.1901C17.2463 33.2921 14.8555 30.9066 14.8555 27.9575V19.6317C14.8555 16.688 17.241 14.2971 20.1901 14.2971Z"
            fill="currentColor" />
        <path
            d="M27.5835 1H7.68802C3.99118 1 1 3.99118 1 7.68802V18.1203C1 21.7644 3.92272 24.724 7.5511 24.7977L8.82024 30.7064L15.9611 24.803H27.5835C31.2751 24.803 34.2716 21.8118 34.2716 18.115V7.68802C34.2716 3.99644 31.2804 1 27.5835 1Z"
            fill="currentColor" stroke="#009585" stroke-miterlimit="10" />
    </svg>

    <!-- Xmark  -->
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" id="x-mark-solid">
        <path fill="currentColor" clip-rule="evenodd"
            d="M23.9062 11.9531C23.9062 18.4805 18.4922 23.9062 11.9531 23.9062C5.42578 23.9062 0 18.4805 0 11.9531C0 5.41406 5.41406 0 11.9414 0C18.4805 0 23.9062 5.41406 23.9062 11.9531ZM7.05469 15.9023C7.05469 16.4414 7.47656 16.875 8.01562 16.875C8.28516 16.875 8.53125 16.7812 8.71875 16.5938L11.9531 13.3359L15.1992 16.5938C15.375 16.7812 15.6211 16.875 15.8906 16.875C16.4414 16.875 16.875 16.4414 16.875 15.9023C16.875 15.6328 16.7812 15.3867 16.582 15.2109L13.3359 11.9648L16.5938 8.70703C16.8047 8.49609 16.8867 8.28516 16.8867 8.01562C16.8867 7.47656 16.4531 7.05469 15.9141 7.05469C15.6562 7.05469 15.4453 7.13672 15.2461 7.33594L11.9531 10.6055L8.68359 7.34766C8.49609 7.17188 8.28516 7.07812 8.01562 7.07812C7.47656 7.07812 7.05469 7.48828 7.05469 8.03906C7.05469 8.29688 7.14844 8.53125 7.33594 8.71875L10.582 11.9648L7.33594 15.2227C7.14844 15.3984 7.05469 15.6445 7.05469 15.9023Z" />
    </svg>

    <!-- chevron-backward-circle-mark -->
    <svg viewBox="0 0 24 24" id="chevron-backward-circle-solid" xmlns="http://www.w3.org/2000/svg">
        <path clip-rule="evenodd"
            d="M23.9062 11.9531C23.9062 18.4805 18.4922 23.9062 11.9531 23.9062C5.42578 23.9062 0 18.4805 0 11.9531C0 5.41406 5.41406 0 11.9414 0C18.4805 0 23.9062 5.41406 23.9062 11.9531ZM13.0312 17.7189C13.3594 18.0235 13.9688 18.047 14.2734 17.754C14.6367 17.3907 14.625 16.8282 14.2852 16.5001L9.46875 11.9532L14.2852 7.42976C14.6367 7.10163 14.625 6.51569 14.2617 6.17585C13.9336 5.85945 13.3945 5.87116 13.0312 6.21101L8.15625 10.8165C7.48828 11.4259 7.48828 12.504 8.15625 13.1134L13.0312 17.7189Z"
            fill="currentColor" />
    </svg>

    <!-- chevron-next-circle-mark -->
    <svg id="chevron-next-circle-solid" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path clip-rule="evenodd"
            d="M4.85461e-05 11.9532C4.79754e-05 5.42575 5.41405 5.12077e-05 11.9531 5.0636e-05C18.4805 5.00654e-05 23.9062 5.42575 23.9062 11.9531C23.9062 18.4922 18.4922 23.9063 11.9649 23.9063C5.42575 23.9063 4.91178e-05 18.4922 4.85461e-05 11.9532ZM10.875 6.18738C10.5468 5.88278 9.93745 5.85928 9.63285 6.15228C9.26955 6.51558 9.28125 7.07808 9.62105 7.40618L14.4375 11.9531L9.62105 16.4765C9.26955 16.8046 9.28125 17.3906 9.64455 17.7304C9.97265 18.0468 10.5117 18.0351 10.875 17.6952L15.75 13.0898C16.418 12.4804 16.418 11.4023 15.75 10.7929L10.875 6.18738Z"
            fill="currentColor" />
    </svg>

    <!-- location solid icon -->
    <svg class="address__icon" id="location-solid-icon" aria-hidden="true" viewBox="0 0 24 24">
        <path fill="currentColor"
            d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5">
        </path>
    </svg>

    <!-- clock solid icon -->
    <svg id="clock-solid-icon" class="working-hour__icon" viewBox="0 0 20 20">
        <path
            d="M1 10C1 5.02944 5.02944 1 10 1C14.9706 1 19 5.02944 19 10C19 14.9706 14.9706 19 10 19C5.02944 19 1 14.9706 1 10ZM12 9.13795V5.5C12 4.39543 11.1046 3.5 10 3.5C8.89543 3.5 8 4.39543 8 5.5V10.0486C8 10.5756 8.20799 11.0813 8.57876 11.4558L11.5228 14.4293C12.3469 15.2617 13.7061 15.2129 14.4683 14.3236C15.1394 13.5407 15.1067 12.3763 14.3929 11.6322L12 9.13795Z"
            fill="currentColor" />
    </svg>

    <!-- Phone solid icon -->
    <svg id="phone-solid-icon" viewBox="0 0 24 24">
        <path fill="currentColor"
            d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56-.35-.12-.74-.03-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99">
        </path>
    </svg>

    <!-- Address solid icon -->
    <svg id="address-solid-icon" aria-hidden="true" viewBox="0 0 24 24">
        <path fill="currentColor"
            d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5">
        </path>
    </svg>

    <!-- Position line icon -->
    <svg id="address-solid-icon" aria-hidden="true" viewBox="0 0 24 24">
        <path fill="currentColor"
            d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5">
        </path>
    </svg>

    <!-- Read more line icon -->
    <svg id="read-more-line-icon" viewBox="0 0 16 16" aria-hidden="true">
        <path fill="none"
            d="M8.5 1.5L14.64 7.65C14.6878 7.69489 14.726 7.74911 14.752 7.80931C14.7781 7.8695 14.7915 7.9344 14.7915 8C14.7915 8.0656 14.7781 8.1305 14.752 8.19069C14.726 8.25089 14.6878 8.30511 14.64 8.35L8.5 14.5"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M1.25 1.5L7.4 7.65C7.49161 7.74346 7.54293 7.86912 7.54293 8C7.54293 8.13088 7.49161 8.25654 7.4 8.35L1.25 14.5" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </svg>

    <!-- play solid icon -->
    <svg id="play-solid-icon" viewBox="0 0 17 22" aria-hidden="true">
        <path d="M0 21.5L16.5 11L0 0.5V21.5Z" fill="currentColor" />
    </svg>

    <!-- Pause solid icon -->
    <svg id="pause-solid-icon" viewBox="0 0 18 21" aria-hidden="true">
        <path
            d="M0 18C0 19.6569 1.34315 21 3 21V21C4.65685 21 6 19.6569 6 18V3C6 1.34315 4.65685 0 3 0V0C1.34315 0 0 1.34315 0 3V18ZM15 0C13.3431 0 12 1.34315 12 3V18C12 19.6569 13.3431 21 15 21V21C16.6569 21 18 19.6569 18 18V3C18 1.34315 16.6569 0 15 0V0Z"
            fill="currentColor" />
    </svg>
    <svg id="liked-icon" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 11L11 2C11.7956 2 12.5587 2.31607 13.1213 2.87868C13.6839 3.44129 14 4.20435 14 5V9H19.66C19.9499 8.99672 20.2371 9.0565 20.5016 9.17522C20.7661 9.29393 21.0016 9.46873 21.1919 9.68751C21.3821 9.90629 21.5225 10.1638 21.6033 10.4423C21.6842 10.7207 21.7035 11.0134 21.66 11.3L20.28 20.3C20.2077 20.7769 19.9654 21.2116 19.5979 21.524C19.2304 21.8364 18.7623 22.0055 18.28 22H7M7 11V22M7 11H4C3.46957 11 2.96086 11.2107 2.58579 11.5858C2.21071 11.9609 2 12.4696 2 13V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H7" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    
    <svg id="eye-icon" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>   
    <svg id="calendar-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M19 4H5C3.89543 4 3 4.89543 3 6V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V6C21 4.89543 20.1046 4 19 4Z" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M16 2V6" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8 2V6" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M3 10H21" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>

    <svg id="fb-icon" viewBox="0 0 13 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 1H9C7.67392 1 6.40215 1.52678 5.46447 2.46447C4.52678 3.40215 4 4.67392 4 6V9H1V13H4V21H8V13H11L12 9H8V6C8 5.73478 8.10536 5.48043 8.29289 5.29289C8.48043 5.10536 8.73478 5 9 5H12V1Z" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    
    <svg style="display: none;">
        <symbol id="mail-icon" viewBox="0 0 24 24">
            <path 
            d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" 
            fill="none" 
            stroke="currentColor" 
            stroke-width="2" 
            stroke-linecap="round" 
            stroke-linejoin="round"
            />
            <polyline 
            points="22,6 12,13 2,6"
            fill="none" 
            stroke="currentColor" 
            stroke-width="2" 
            stroke-linecap="round" 
            stroke-linejoin="round"
            />
        </symbol>
    </svg>

    <svg id="link-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10 13C10.4295 13.5741 10.9774 14.0491 11.6066 14.3929C12.2357 14.7367 12.9315 14.9411 13.6467 14.9923C14.3618 15.0435 15.0796 14.9403 15.7513 14.6897C16.4231 14.4392 17.0331 14.047 17.54 13.54L20.54 10.54C21.4508 9.59695 21.9548 8.33394 21.9434 7.02296C21.932 5.71198 21.4061 4.45791 20.4791 3.53087C19.5521 2.60383 18.298 2.07799 16.987 2.0666C15.676 2.0552 14.413 2.55918 13.47 3.46997L11.75 5.17997" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M14 11.0002C13.5705 10.4261 13.0226 9.95104 12.3934 9.60729C11.7642 9.26353 11.0684 9.05911 10.3533 9.00789C9.63816 8.95667 8.92037 9.05986 8.24861 9.31044C7.57685 9.56103 6.96684 9.95316 6.45996 10.4602L3.45996 13.4602C2.54917 14.4032 2.04519 15.6662 2.05659 16.9772C2.06798 18.2882 2.59382 19.5423 3.52086 20.4693C4.4479 21.3964 5.70197 21.9222 7.01295 21.9336C8.32393 21.945 9.58694 21.441 10.53 20.5302L12.24 18.8202" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>

    <svg id="instagram-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M17 2H7C4.23858 2 2 4.23858 2 7V17C2 19.7614 4.23858 22 7 22H17C19.7614 22 22 19.7614 22 17V7C22 4.23858 19.7614 2 17 2Z" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M16 11.3698C16.1234 12.2021 15.9812 13.052 15.5937 13.7988C15.2062 14.5456 14.5931 15.1512 13.8416 15.5295C13.0901 15.9077 12.2384 16.0394 11.4077 15.9057C10.5771 15.7721 9.80971 15.3799 9.21479 14.785C8.61987 14.1901 8.22768 13.4227 8.09402 12.592C7.96035 11.7614 8.09202 10.9097 8.47028 10.1582C8.84854 9.40667 9.45414 8.79355 10.2009 8.40605C10.9477 8.01856 11.7977 7.8764 12.63 7.99981C13.4789 8.1257 14.2648 8.52128 14.8716 9.12812C15.4785 9.73496 15.8741 10.5209 16 11.3698Z"  stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M17.5 6.5H17.51" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>   
    <svg id="arrow-up-icon" viewBox="0 0 21 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M19.4697 10.2119L10.6462 1.72809L1.82275 10.2119" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    </div> 
}