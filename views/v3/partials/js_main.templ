package partials

import "webgo/pkg/gos/templates"

templ JSMain() {
<script type="text/javascript" src={ templates.AssetURL("/asset/js/layouts/main-menu-v3.js") }></script>
<script type="text/javascript" src={ templates.AssetURL("/asset/js/components/fast-action-controls.js") }></script>
<script async type="text/javascript" src={ templates.AssetURL("/asset/js/lib/lazysizes/lazysizes.min.js") }></script>
// <script src={ templates.AssetURL("/asset/js/layouts/search.js") } type="module"></script>
<script>	
    document.addEventListener("DOMContentLoaded", function () {
        const lazyImages = document.querySelectorAll("img.lazyload");

        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    if (img.dataset.srcset) {
                        img.srcset = img.dataset.srcset;
                    }
                    img.classList.remove("lazyload");
                    observer.unobserve(img);
                }
            });
        });

        lazyImages.forEach((img) => {
            imageObserver.observe(img);
        });
    });

    
    document.querySelectorAll(".open-popup-locations").forEach(button => {
        button.addEventListener("click", () => {
            document.querySelector("#btn-location-round-id").click()
        });
    });
</script>
<script>
    window.addEventListener("scroll", function () {
        const toTopBtn = document.getElementById("to_top");
        if (!toTopBtn) return;
        if (window.scrollY > 300) {
            toTopBtn.classList.add("show");
        } else {
            toTopBtn.classList.remove("show");
        }
    });
    
    const toTopBtn = document.getElementById("to_top");
    if (toTopBtn) {
        toTopBtn.addEventListener("click", function () {
            window.scrollTo({
                top: 0,
                behavior: "smooth"
            });
        });
    }
</script>
}