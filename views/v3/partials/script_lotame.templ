package partials


templ ScriptLotame() {
    <link rel="preconnect" href="https://tags.crwdcntrl.net"> 
    <link rel="preconnect" href="https://bcp.crwdcntrl.net"> 
    <link rel="preconnect" href="https://c.ltmsphrcl.net"> 
    <link rel="dns-prefetch" href="https://tags.crwdcntrl.net"> 
    <link rel="dns-prefetch" href="https://bcp.crwdcntrl.net"> 
    <link rel="dns-prefetch" href="https://c.ltmsphrcl.net"> 
    <script> 
        !function() { 
            var lotameClientId = '17974'; 
            var lotameTagInput = { 
                data: {}, 
                config: { 
                clientId: Number(lotameClientId) 
                } 
            }; 

            var lotameConfig = lotameTagInput.config || {}; 
            var namespace = window['lotame_' + lotameConfig.clientId] = {}; 
            namespace.config = lotameConfig; 
            namespace.data = lotameTagInput.data || {}; 
            namespace.cmd = namespace.cmd || []; 
        }(); 
    </script> 
    <script async src="https://tags.crwdcntrl.net/lt/c/17974/lt.min.js"></script>
}