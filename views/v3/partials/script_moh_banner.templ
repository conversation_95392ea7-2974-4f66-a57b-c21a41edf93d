package partials

templ ScriptMOHBanner() {
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const modal = document.getElementById("pop-up-moh");
            const closeModal = document.querySelector(".btn-close-modal-moh");
            const overlay = document.querySelector("#pop-up-moh .overlay");

            const excludedRoutes = ["/dia-diem-tu-van", "/chien-dich-toan-quoc", "/chien-dich-toan-quoc-v2"];
            const currentPath = window.location.pathname;

            if (!excludedRoutes.includes(currentPath)) {
                function getCookie(name) {
                    const cookies = document.cookie.split("; ");
                    for (let i = 0; i < cookies.length; i++) {
                        const [key, value] = cookies[i].split("=");
                        if (key === name) return decodeURIComponent(value);
                    }
                    return null;
                }

                function setCookie(name, value, days) {
                    const date = new Date();
                    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
                    document.cookie = `${name}=${encodeURIComponent(value)}; expires=${date.toUTCString()}; path=/`;
                }

                function getPopupCount() {
                    const count = getCookie("popup_count");
                    return count ? parseInt(count, 10) : 0;
                }

                function incrementPopupCount() {
                    const count = getPopupCount();
                    setCookie("popup_count", count + 1, 1); // Lưu trong 1 ngày
                }

                function openModal() {
                    if (getPopupCount() < 2) {
                        modal.classList.add("active");
                        incrementPopupCount();
                    }
                }

                function closeModalFunction() {
                    modal.classList.remove("active");
                }

                closeModal.addEventListener("click", closeModalFunction);
                overlay.addEventListener("click", closeModalFunction);
                document.getElementById("btn-tick-prevent-banner").addEventListener("click", function () {
                    openModal();
                });

                setTimeout(openModal, 50000);
            }
        });
    </script>
    <script>
        document.querySelectorAll("#btn-tick-prevent-banner, #tick-border-banner, #bottom-disc-moh__left").forEach((button) => {
            button.addEventListener("click", function () {
                const groupTick = document.querySelector(".group-tick");
                if (groupTick) {
                    const line = groupTick.querySelector(".line-moh");
                    const hand = groupTick.querySelector(".hand-img");
                    const btn = groupTick.querySelector(".btn-tick-prevent");

                    if (line) line.style.clipPath = "inset(0 0 0 0)"; // Clip path effect
                    if (hand) hand.style.opacity = "1"; // Show img
                    if (btn) btn.classList.add("active"); // Active button
                }
            });
        });

        const tickBtn = document.getElementById("tick-border-banner");
        const preventBtn = document.getElementById("btn-tick-prevent-banner");

        if (tickBtn && preventBtn) {
            [tickBtn, preventBtn].forEach((btn) => {
                btn.addEventListener("click", function () {

                    setTimeout(() => {
                        window.location.href = "https://hpv.vn/chien-dich-toan-quoc";
                    }, 1500);
                });
            });
        }
    </script>
}