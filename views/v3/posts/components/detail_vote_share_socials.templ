package components

import "webgo/hpv/postsv3/transport/responses"
import "webgo/pkg/gos/utils"

templ VoteShareSocial(post *responses.PostDetail) {
    <section class="mt-2">
        <ul class="article-meta">       
            if post != nil && post.Views > 0 {
                <li class="article-meta-item">
                    <span class="article-meta-icon">
                        <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                            <use xlink:href="#eye-icon" fill="currentColor"></use>
                        </svg>
                    </span>           
                    <span>{ utils.FormatNumberWithDot(post.Views) }</span>
                </li>
            }
            <li class="article-meta-item">            
                <a class="article-meta-icon" href="javascript: void(0);" id="fb-share-icon">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#fb-icon" fill="currentColor"></use>
                    </svg>
                </a>   
            </li>
            <li class="article-meta-item">            
                <a class="article-meta-icon" href="javascript:void(0);" id="mail-share-btn">
                <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="#mail-icon" fill="currentColor"></use>
                </svg>
                </a>   
            </li>
            <li class="article-meta-item">            
                <a class="article-meta-icon" href="javascript: void(0);" id="copy-link">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink">
                        <use xlink:href="#link-icon" fill="currentColor"></use>
                    </svg>
                </a>   
            </li>
            <!-- Box thông báo -->
            <div id="copyToast">Copy link thành công</div>
        </ul>
    </section>
}